{"name": "web", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve  --configuration development --host 0.0.0.0 --disable-host-check", "uat": "ng build --configuration=uat", "build": "ng build --configuration production", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "ext-uat": "ng run web:ext --configuration=uat && bash ext/i18n-ext.sh", "ext": "ng run web:ext --configuration=production && bash ext/i18n-ext.sh", "macos": "ng run web:macos --configuration=production && cd macos && ./i18n-macos.sh", "windows": "ng run web:windows --configuration=production && cd windows && .\\i18n-windows.bat", "watch:web-core": "ng build web-core --watch", "build:web-core": "ng build web-core", "publish:web-core": "cd dist/web-core && npm publish"}, "private": true, "dependencies": {"@angular/animations": "^14.2.7", "@angular/cdk": "^14.2.7", "@angular/common": "^14.2.7", "@angular/compiler": "^14.2.7", "@angular/core": "^14.2.7", "@angular/forms": "^14.2.7", "@angular/localize": "^14.2.7", "@angular/material": "^14.2.7", "@angular/platform-browser": "^14.2.7", "@angular/platform-browser-dynamic": "^14.2.7", "@angular/router": "^14.2.7", "@stripe/stripe-js": "^3.3.0", "@types/big.js": "^6.2.0", "angularx-qrcode": "^14.0.0", "big.js": "^6.2.0", "canvas-confetti": "^1.6.0", "dexie": "^2.0.4", "firebase": "^9.18.0", "hammerjs": "^2.0.8", "lodash": "^4.17.21", "material-icons": "^0.3.1", "moment": "^2.29.4", "ngx-infinite-scroll": "^14.0.0", "ngx-skeleton-loader": "^6.0.0", "ngx-swiper-wrapper": "^10.0.0", "qrcode": "^1.5.3", "rxjs": "^6.5.5", "superagent": "^7.0.2", "swiper": "^6.8.4", "tslib": "^2.0.0", "yaml": "^1.10.2", "zone.js": "^0.11.8"}, "devDependencies": {"@angular-devkit/build-angular": "^14.2.13", "@angular/cli": "^14.2.13", "@angular/compiler-cli": "^14.2.7", "@angular/language-service": "^14.2.7", "@types/jasmine": "^3.3.16", "@types/jasminewd2": "^2.0.9", "@types/node": "^12.20.15", "@types/qrcode": "^1.5.5", "codelyzer": "^6.0.2", "jasmine-core": "^3.8.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.1", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "ng-packagr": "^14.2.0", "protractor": "^7.0.0", "ts-node": "^7.0.1", "tslint": "~6.1.0", "typescript": ">=4.6.2 <4.9.0"}}