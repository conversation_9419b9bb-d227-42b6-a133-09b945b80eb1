name: Publish Web Core Package
on:
  push:
    branches:
      - "feature/web-core" # Triggers on version tags

jobs:
  build-and-publish:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - uses: actions/checkout@v3

      - uses: actions/setup-node@v3
        with:
          node-version: "20.x"
          registry-url: "https://npm.pkg.github.com"
          scope: "@flashvpn-io"

      - name: Install dependencies
        run: npm ci

      - name: Build web-core
        run: npm run build:web-core

      - name: Publish package
        run: cd dist/web-core && npm publish
        env:
          NODE_AUTH_TOKEN: ${{secrets.GITHUB_TOKEN}}
