steps:
  - name: 'node:14.21.1'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "@flashvpn-io:registry=https://npm.pkg.github.com" >> .npmrc
        echo "//npm.pkg.github.com/:_authToken=$_NPM_TOKEN" >> .npmrc
        npm install
    id: npm_install
  - name: 'node:14.21.1'
    entrypoint: npm
    args: ['run', 'build:web-core']
    id: build_core
  - name: 'node:14.21.1'
    entrypoint: npm
    args: ['run', 'uat']
    id: build
  - name: 'ubuntu'
    entrypoint: bash
    args: ['i18n-index.sh']
    id: localize-index
  - name: 'gcr.io/cloud-builders/docker'
    args: [ 'build', '-t', 'gcr.io/$PROJECT_ID/web:$COMMIT_SHA', '.' ]
    id: build_image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/web:$COMMIT_SHA']
    id: push_image
  - name: "ubuntu"
    args: ["sed", "-i" ,"s/latest/$COMMIT_SHA/", "k8s/uat/deployment.yaml"]
    id: update_config
  - name: "bitnami/kubectl"
    waitFor:
      - push_image
    args:
      - --server=$_CLUSTER_URL
      - --token=$_CLUSTER_TOKEN
      - --insecure-skip-tls-verify=true
      - apply
      - -f
      - k8s/uat/deployment.yaml
    id: kube_deploy
timeout: '1600s'
