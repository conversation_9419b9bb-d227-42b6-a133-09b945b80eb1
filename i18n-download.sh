#!/usr/bin/env bash


# download key value json translations from https://poeditor.com
wget https://api.crowdin.com/api/project/flashvpn/download/zh-CN.zip?key=3776b623b24ef1f6574820ef30436d6e -O /tmp/zh-CN.zip && unzip -d /tmp/zh-CN /tmp/zh-CN.zip && mv /tmp/zh-CN/messages.xlf src/locale/messages.zh-Hans.xlf
wget https://api.crowdin.com/api/project/flashvpn/download/zh-TW.zip?key=3776b623b24ef1f6574820ef30436d6e -O /tmp/zh-TW.zip && unzip -d /tmp/zh-TW /tmp/zh-TW.zip && mv /tmp/zh-TW/messages.xlf src/locale/messages.zh-Hant.xlf
#wget https://api.crowdin.com/api/project/flashvpn/download/es-ES.zip?key=3776b623b24ef1f6574820ef30436d6e -O /tmp/es-ES.zip && unzip -d /tmp /tmp/es-ES.zip && mv /tmp/messages.xlf src/locale/messages.es-ES.xlf
#wget https://api.crowdin.com/api/project/flashvpn/download/ru.zip?key=3776b623b24ef1f6574820ef30436d6e -O /tmp/ru.zip && unzip -d /tmp /tmp/ru.zip && mv /tmp/messages.xlf src/locale/messages.ru.xlf
#wget https://api.crowdin.com/api/project/flashvpn/download/ja.zip?key=3776b623b24ef1f6574820ef30436d6e -O /tmp/ja.zip && unzip -d /tmp /tmp/ja.zip && mv /tmp/messages.xlf src/locale/messages.ja.xlf

