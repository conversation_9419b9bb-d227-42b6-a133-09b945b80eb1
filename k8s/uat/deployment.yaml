kind: Deployment
apiVersion: apps/v1
metadata:
  labels:
    k8s-app: web
  name: web
  namespace: flash
spec:
  strategy:
    type: RollingUpdate
  selector:
    matchLabels:
      k8s-app: web
  template:
    metadata:
      labels:
        k8s-app: web
    spec:
      imagePullSecrets:
        - name: gcr-flash
      containers:
        - name: web
          image: gcr.io/flashvpn-253908/web:latest

---

kind: Service
apiVersion: v1
metadata:
  name: web
  namespace: flash
  labels:
    k8s-app: web
spec:
  ports:
    - name: web-tcp-80
      protocol: TCP
      port: 80
      targetPort: 80
  selector:
    k8s-app: web
  type: ClusterIP
