#!/usr/bin/env bash

# extract
ng xi18n --output-path=src/locale

# upload english base translations from https://poeditor.com
#curl -X POST https://api.poeditor.com/v2/projects/upload -F api_token="f0ef1e4affe2b90488b7e11de778132e" -F id="289989" -F language="en-US" -F updating="terms_translations" -F read_from_source=1 -F overwrite=1 -F sync_terms=1 -F file=@"src/locale/messages.xlf"

#upload to crowdin
curl -X POST https://api.crowdin.com/api/project/flashvpn/update-file?key=3776b623b24ef1f6574820ef30436d6e -F "files[messages.xlf]=@src/locale/messages.xlf"

