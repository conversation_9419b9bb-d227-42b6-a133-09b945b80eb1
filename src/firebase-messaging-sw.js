importScripts('https://www.gstatic.com/firebasejs/9.18.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.18.0/firebase-messaging-compat.js');

firebase.initializeApp({
  apiKey: 'AIzaSyCLbpdC6AnWT0SnalNmBtFPuhzNeVvjEpE',
  authDomain: 'flashvpn-253908.firebaseapp.com',
  databaseURL: 'https://flashvpn-253908.firebaseio.com',
  projectId: 'flashvpn-253908',
  storageBucket: 'flashvpn-253908.appspot.com',
  messagingSenderId: '20910832588',
  appId: '1:20910832588:web:e8a718c480e184cd8afb69',
  measurementId: 'G-G92MLYCXKV'
});

// Retrieve an instance of Firebase Messaging so that it can handle background
// messages.
const messaging = firebase.messaging();


messaging.onBackgroundMessage(function(payload) {
  // Customize notification here
  const notificationTitle = payload.title;
  const notificationOptions = {
    body: payload.body,
    icon: payload.imageUrl,
    data: payload.data,
    click_action: `https://flashvpn.io/${payload.data.path}`
  };

  self.registration.showNotification(notificationTitle,
    notificationOptions);
});
