<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>FlashVPN</title>
  <base href="/">
  <meta content='FlashVPN' name='apple-mobile-web-app-title'>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <meta content='{description}' name='description'>
  <meta name='keyword' content='{keyword}'>
  <meta name="og:title" property="og:title" content="FlashVPN">
  <meta name="og:description" property="og:description" content="{description}">
  <meta property="og:image" content="https://pub-0841c119624949269deec25f5bd284e8.r2.dev/feature.png" />
  <meta name='twitter:site' content='@vpn_flash'>
  <meta name="twitter:card" content="summary" />
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <!-- Google Tag Manager -->
  <script>(function (w, d, s, l, i) {
      w[l] = w[l] || []; w[l].push({
        'gtm.start':
          new Date().getTime(), event: 'gtm.js'
      }); var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
          'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
    })(window, document, 'script', 'dataLayer', 'GTM-5GW79KD');</script>
  <!-- End Google Tag Manager -->
</head>

<body>
  <!-- Google Tag Manager (noscript) -->
  <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5GW79KD" height="0" width="0"
      style="display:none;visibility:hidden"></iframe></noscript>
  <!-- End Google Tag Manager (noscript) -->
  <app-root></app-root>
  <script>
    (function (d, t) {
      var BASE_URL = "https://app.chatwoot.com";
      var g = d.createElement(t), s = d.getElementsByTagName(t)[0];
      g.src = BASE_URL + "/packs/js/sdk.js";
      g.defer = true;
      g.async = true;
      s.parentNode.insertBefore(g, s);
      var isEn = /en-US/.test(location.href)
      window.chatwootSettings = {
        hideMessageBubble: false,
        locale: isEn ? 'en' : 'zh', // Language to be set
        type: 'expanded_bubble',
        launcherTitle: isEn ? 'Chat with us' : '人工客服',
      }
      g.onload = function () {
        window.chatwootSDK.run({
          websiteToken: '9nkVAG5jfvnfnbQbpfK5TSDp',
          baseUrl: BASE_URL
        })
      }
    })(document, "script");
  </script>
  <script src="vendors/lazysizes.min.js" async></script>
</body>

</html>