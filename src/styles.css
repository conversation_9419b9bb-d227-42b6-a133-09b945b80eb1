/* You can add global styles to this file, and also import other style files */

/**
Google fonts
 */

/* latin */
@font-face {
  font-family: "Libre Franklin";
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: local("Libre Franklin Light"), local("LibreFranklin-Light"), url(/assets/fonts/jizAREVItHgc8qDIbSTKq4XkRi20-SI0q1vjitOh.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212,
    U+2215, U+FEFF, U+FFFD;
}

/* latin */
@font-face {
  font-family: "Libre Franklin";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local("Libre Franklin"), local("LibreFranklin-Regular"), url(/assets/fonts/jizDREVItHgc8qDIbSTKq4XkRiUf2zcZiVbJ.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212,
    U+2215, U+FEFF, U+FFFD;
}

/* latin */
@font-face {
  font-family: "Libre Franklin";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local("Libre Franklin Medium"), local("LibreFranklin-Medium"),
    url(/assets/fonts/jizAREVItHgc8qDIbSTKq4XkRi3s-CI0q1vjitOh.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212,
    U+2215, U+FEFF, U+FFFD;
}

/* latin */
@font-face {
  font-family: "Merriweather";
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: local("Merriweather Bold Italic"), local("Merriweather-BoldItalic"),
    url(/assets/fonts/u-4l0qyriQwlOrhSvowK_l5-eR71Wvf4jvzDP3WG.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212,
    U+2215, U+FEFF, U+FFFD;
}

/* latin */
@font-face {
  font-family: "Merriweather";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local("Merriweather Regular"), local("Merriweather-Regular"), url(/assets/fonts/u-440qyriQwlOrhSvowK_l5-fCZMdeX3rg.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212,
    U+2215, U+FEFF, U+FFFD;
}

/**
Material icon
 */

/* fallback */
@font-face {
  font-family: "Material Icons";
  font-style: normal;
  font-weight: 400;
  src: url(/assets/fonts/flUhRq6tzZclQEJ-Vdg-IuiaDsNcIhQ8tQ.woff2) format("woff2");
}

.material-icons {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
}

/**
  our own style
 */
* {
  box-sizing: border-box;
}
html,
body {
  height: 100%;
  min-width: 320px;
  min-height: 568px;
  font-size: 14px;
}
body {
  margin: 0;
  font-size: 14px;
  font-family: "Libre Franklin", sans-serif;
}
h1 {
  font-size: 36px;
  font-weight: normal;
}
h2 {
  font-size: 26px;
  font-weight: normal;
}
h3 {
  font-size: 18px;
  font-weight: normal;
}
h4 {
  font-size: 14px;
  font-weight: normal;
}

.h1Size {
  font-size: 36px;
  font-weight: normal;
}
.h2Size {
  font-size: 26px;
  font-weight: normal;
}
.h3Size {
  font-size: 18px;
  font-weight: normal;
}
.h4Size {
  font-size: 16px;
  font-weight: normal;
}
.h5Size {
  font-size: 12px;
  font-weight: normal;
}
.bodyTextColor {
  color: #505050;
}
.action-button {
  font-size: 16px;
  font-family: "Libre Franklin", sans-serif;
  font-weight: bold;
  color: #ff5e5e;
  background: rgba(255, 94, 94, 0.1);
  border: 1px solid #ff5e5e;
  box-sizing: border-box;
  border-radius: 2px;
  padding: 5px 10px 5px 10px;
  display: inline-block;
  text-decoration: none;
  cursor: pointer;
}

.highlighted-button {
  color: #ff5e5e;
}

.highlighted-button svg path {
  fill: #ff5e5e !important;
}

.callout-button {
  padding: 5px;
  font-size: 16px;
  font-family: "Libre Franklin", sans-serif;
  font-weight: bold;
  color: #ffffff;
  background-color: #ff5e5e;
  border-radius: 2px;
  margin: 16px 0;
}
a,
a:visited {
  text-decoration: none;
  color: #ff5e5e;
}

.divider {
  width: 100%;
  height: 1px;
  background: #c4c4c4;
}
.action-button svg path {
  fill: #ff5e5e !important;
}

.btn-black {
  background: #000;
  color: #fff;
}

/*overriding*/
.mat-select-arrow {
  color: #ff5e5e !important;
}
.highlighted {
  font-weight: bold;
  color: #ff5e5e;
}

.mat-button-toggle-checked {
  background-color: #ff5e5e !important;
}
.mat-button-toggle-checked.mat-button-toggle-appearance-standard {
  color: white !important;
}

.custom-overlay {
  background-color: #fff;
  padding: 20px;
  border-radius: 12px;
}
