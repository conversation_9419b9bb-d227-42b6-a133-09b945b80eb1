/* You can add global styles to this file, and also import other style files */


/**
Google fonts
 */

/* latin */
@font-face {
  font-family: 'Libre Franklin';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: local('Libre Franklin Light'), local('LibreFranklin-Light'), url(/assets/fonts/jizAREVItHgc8qDIbSTKq4XkRi20-SI0q1vjitOh.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* latin */
@font-face {
  font-family: 'Libre Franklin';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local('Libre Franklin'), local('LibreFranklin-Regular'), url(/assets/fonts/jizDREVItHgc8qDIbSTKq4XkRiUf2zcZiVbJ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* latin */
@font-face {
  font-family: 'Libre Franklin';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local('Libre Franklin Medium'), local('LibreFranklin-Medium'), url(/assets/fonts/jizAREVItHgc8qDIbSTKq4XkRi3s-CI0q1vjitOh.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* latin */
@font-face {
  font-family: 'Merriweather';
  font-style: italic;
  font-weight: 700;
  font-display: swap;
  src: local('Merriweather Bold Italic'), local('Merriweather-BoldItalic'), url(/assets/fonts/u-4l0qyriQwlOrhSvowK_l5-eR71Wvf4jvzDP3WG.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* latin */
@font-face {
  font-family: 'Merriweather';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local('Merriweather Regular'), local('Merriweather-Regular'), url(/assets/fonts/u-440qyriQwlOrhSvowK_l5-fCZMdeX3rg.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}


/**
Material icon
 */

/* fallback */
@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url(/assets/fonts/flUhRq6tzZclQEJ-Vdg-IuiaDsNcIhQ8tQ.woff2) format('woff2');
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
}


/**
  our own style
 */
html, body { height: 100%; min-width: 320px; min-height: 568px; font-size: 14px;}
body { margin: 0; font-size: 14px;  font-family: 'Libre Franklin', sans-serif;}
h1 {font-size: 36px; font-weight: normal;}
h2 {font-size: 26px; font-weight: normal;}
h3 {font-size: 18px; font-weight: normal;}
h4 {font-size: 14px; font-weight: normal;}

.h1Size{font-size: 36px; font-weight: normal;}
.h2Size{font-size: 26px; font-weight: normal;}
.h3Size{font-size: 18px; font-weight: normal;}
.h4Size{font-size: 16px; font-weight: normal;}
.h5Size{font-size: 12px; font-weight: normal;}
.bodyTextColor{color: #505050}
.action-button{
  font-size: 16px;
  font-family:'Libre Franklin',
  sans-serif;
  font-weight: bold;
  color: #FF5E5E;
  background: rgba(255, 94, 94, 0.1);
  border: 1px solid #FF5E5E;
  box-sizing: border-box;
  border-radius: 2px;
  padding: 5px 10px 5px 10px;
  display: inline-block;
  text-decoration: none;
  cursor: pointer;
}

.highlighted-button{
  color: #FF5E5E;
}

.highlighted-button svg path {
  fill: #FF5E5E!important;
}

.callout-button {
  padding: 5px;
  font-size: 16px;
  font-family:'Libre Franklin', sans-serif;
  font-weight: bold;
  color: #FFFFFF;
  background-color: #FF5E5E;
  border-radius: 2px;
  margin: 16px 0;
}
a, a:visited {
  text-decoration: none;
  color: #FF5E5E
}

.divider {
  width: 100%;
  height: 1px;
  background: #C4C4C4;
}
.action-button svg path {
  fill: #FF5E5E!important;
}

.btn-black {
  background: #000;
  color: #fff;
}

/*overriding*/
.mat-select-arrow{
  color: #FF5E5E!important;
}
.highlighted {
  font-weight: bold;color: #FF5E5E
}

.mat-button-toggle-checked{
  background-color: #FF5E5E!important;
}
.mat-button-toggle-checked.mat-button-toggle-appearance-standard{
  color: white!important;
}

.custom-overlay {
  background-color: #fff;
  padding: 20px;
  border-radius: 12px;
}


