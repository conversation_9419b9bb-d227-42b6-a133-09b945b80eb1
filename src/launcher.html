<!DOCTYPE html>
<html lang="en" style="height: 100%">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <title>返回App继续使用</title>
  <script src="https://cdn.staticfile.org/vue/3.2.37/vue.global.min.js"></script>
</head>
<body>
<div id="app">
  <div class="logo-container">
    <img src="assets/images/logo.png" class="logo" />
  </div>

  <div class="container" v-if="isValid">
    <div class="user-info">
      <div>当前账号</div>
      <div>{{email}}</div>
    </div>
    <div class="connect">
      <div class="button" @click="connectApp()">返回APP继续使用</div>
    </div>
    <div class="not-me">
      <div>不是 {{email}}? <a href="/api/v1/users/oauth/google?target=app" class="another">登录其他账号</a></div>
    </div>
  </div>

  <div class="container" v-else>
    <div class="user-info">
      <div>连接错误，请关闭此页面重试！</div>
    </div>
  </div>
</div>

<style>
  a {
    text-decoration: none;
  }
  body {
    margin: 0;
    font-size: .2rem;
    display: flex;
    flex-direction: column;
    padding: 24px;
  }
  .logo-container {
    min-height: 0.1rem;
    display: flex;
    flex-direction: column;
    padding: 10px 0;
  }
  .logo-container img {
    min-width: 130px;
    width: 1.3rem;
    max-width: 260px;
  }

  .logo {
    margin-left: 10px;
  }

  .container {
    margin-top: 90px;
    display: flex;
    flex-direction: column;
  }

  .user-info {
    display: flex;
    flex-direction: column;
    margin-top: 10px;
  }

  .connect {
    margin-top: 40px;
  }
  .button {
    background: #ff5e5e;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #FFFFFF;
    min-height: 62px;

    font-family: 'Oswald';
    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    line-height: 20px;

    text-transform: capitalize;
  }

  .button:hover {
    opacity: 0.5;
  }

  .user-info {
    display: flex;
    flex-direction: column;

    font-style: normal;
    font-weight: 500;
    font-size: 20px;
    line-height: 30px;
    text-align: center;

    /* main color/08 */

    color: #022247;
  }

  .not-me {
    display: flex;
    flex-direction: column;
    margin-top: 25px;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    /* identical to box height */

    text-align: center;

    /* main color/04 */

    color: #A0AEC0;

  }

  .another {
    color: #ff5e5e;
  }
</style>
<script>
  document.addEventListener("DOMContentLoaded",function(){
    document.getElementsByTagName("html")[0].style.fontSize=(document.documentElement.clientWidth/375)*100+"px";
  });

  window.onresize = function(){
    document.getElementsByTagName("html")[0].style.fontSize=(document.documentElement.clientWidth/375)*100+"px";
  }
  const getQueryString = (name) => {
    const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
    const r = window.location.search.substr(1).match(reg);
    if (r != null) {
      return unescape(r[2]);
    }
    return null;
  }

  const Counter = {
    data() {
      const email = getQueryString("email");
      const token = getQueryString("token");
      return {
        email: email || "<EMAIL>",
        isValid: token && email,
      }
    },
    methods: {
      connectApp: () => {
        window.location.href=`flash://services?token=${getQueryString("token")}&email=${getQueryString("email")}&isNew=${getQueryString("isNew") || 0}`;
      }
    }
  }
  Vue.createApp(Counter).mount('#app')
</script>
</body>
</html>
