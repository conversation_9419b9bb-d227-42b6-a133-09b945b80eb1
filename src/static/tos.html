<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/><title>FlashVPN Terms of Service</title><style>
  /* webkit printing magic: print all background colors */
  html {
    -webkit-print-color-adjust: exact;
  }
  * {
    box-sizing: border-box;
    -webkit-print-color-adjust: exact;
  }

  html,
  body {
    margin: 0;
    padding: 0;
  }
  @media only screen {
    body {
      margin: 2em auto;
      max-width: 900px;
      color: rgb(55, 53, 47);
    }
  }

  body {
    line-height: 1.5;
    white-space: pre-wrap;
  }

  a,
  a.visited {
    color: inherit;
    text-decoration: underline;
  }

  .pdf-relative-link-path {
    font-size: 80%;
    color: #444;
  }

  h1,
  h2,
  h3 {
    letter-spacing: -0.01em;
    line-height: 1.2;
    font-weight: 600;
    margin-bottom: 0;
  }

  .page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 0.75em;
  }

  h1 {
    font-size: 1.875rem;
    margin-top: 1.875rem;
  }

  h2 {
    font-size: 1.5rem;
    margin-top: 1.5rem;
  }

  h3 {
    font-size: 1.25rem;
    margin-top: 1.25rem;
  }

  .source {
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 1.5em;
    word-break: break-all;
  }

  .callout {
    border-radius: 3px;
    padding: 1rem;
  }

  figure {
    margin: 1.25em 0;
    page-break-inside: avoid;
  }

  figcaption {
    opacity: 0.5;
    font-size: 85%;
    margin-top: 0.5em;
  }

  mark {
    background-color: transparent;
  }

  .indented {
    padding-left: 1.5em;
  }

  hr {
    background: transparent;
    display: block;
    width: 100%;
    height: 1px;
    visibility: visible;
    border: none;
    border-bottom: 1px solid rgba(55, 53, 47, 0.09);
  }

  img {
    max-width: 100%;
  }

  @media only print {
    img {
      max-height: 100vh;
      object-fit: contain;
    }
  }

  @page {
    margin: 1in;
  }

  .collection-content {
    font-size: 0.875rem;
  }

  .column-list {
    display: flex;
    justify-content: space-between;
  }

  .column {
    padding: 0 1em;
  }

  .column:first-child {
    padding-left: 0;
  }

  .column:last-child {
    padding-right: 0;
  }

  .table_of_contents-item {
    display: block;
    font-size: 0.875rem;
    line-height: 1.3;
    padding: 0.125rem;
  }

  .table_of_contents-indent-1 {
    margin-left: 1.5rem;
  }

  .table_of_contents-indent-2 {
    margin-left: 3rem;
  }

  .table_of_contents-indent-3 {
    margin-left: 4.5rem;
  }

  .table_of_contents-link {
    text-decoration: none;
    opacity: 0.7;
    border-bottom: 1px solid rgba(55, 53, 47, 0.18);
  }

  table,
  th,
  td {
    border: 1px solid rgba(55, 53, 47, 0.09);
    border-collapse: collapse;
  }

  table {
    border-left: none;
    border-right: none;
  }

  th,
  td {
    font-weight: normal;
    padding: 0.25em 0.5em;
    line-height: 1.5;
    min-height: 1.5em;
    text-align: left;
  }

  th {
    color: rgba(55, 53, 47, 0.6);
  }

  ol,
  ul {
    margin: 0;
    margin-block-start: 0.6em;
    margin-block-end: 0.6em;
  }

  li > ol:first-child,
  li > ul:first-child {
    margin-block-start: 0.6em;
  }

  ul > li {
    list-style: disc;
  }

  ul.to-do-list {
    text-indent: -1.7em;
  }

  ul.to-do-list > li {
    list-style: none;
  }

  .to-do-children-checked {
    text-decoration: line-through;
    opacity: 0.375;
  }

  ul.toggle > li {
    list-style: none;
  }

  ul {
    padding-inline-start: 1.7em;
  }

  ul > li {
    padding-left: 0.1em;
  }

  ol {
    padding-inline-start: 1.6em;
  }

  ol > li {
    padding-left: 0.2em;
  }

  .mono ol {
    padding-inline-start: 2em;
  }

  .mono ol > li {
    text-indent: -0.4em;
  }

  .toggle {
    padding-inline-start: 0em;
    list-style-type: none;
  }

  /* Indent toggle children */
  .toggle > li > details {
    padding-left: 1.7em;
  }

  .toggle > li > details > summary {
    margin-left: -1.1em;
  }

  .selected-value {
    display: inline-block;
    padding: 0 0.5em;
    background: rgba(206, 205, 202, 0.5);
    border-radius: 3px;
    margin-right: 0.5em;
    margin-top: 0.3em;
    margin-bottom: 0.3em;
    white-space: nowrap;
  }

  .collection-title {
    display: inline-block;
    margin-right: 1em;
  }

  time {
    opacity: 0.5;
  }

  .icon {
    display: inline-block;
    max-width: 1.2em;
    max-height: 1.2em;
    text-decoration: none;
    vertical-align: text-bottom;
    margin-right: 0.5em;
  }

  img.icon {
    border-radius: 3px;
  }

  .user-icon {
    width: 1.5em;
    height: 1.5em;
    border-radius: 100%;
    margin-right: 0.5rem;
  }

  .user-icon-inner {
    font-size: 0.8em;
  }

  .text-icon {
    border: 1px solid #000;
    text-align: center;
  }

  .page-cover-image {
    display: block;
    object-fit: cover;
    width: 100%;
    height: 30vh;
  }

  .page-header-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .page-header-icon-with-cover {
    margin-top: -0.72em;
    margin-left: 0.07em;
  }

  .page-header-icon img {
    border-radius: 3px;
  }

  .link-to-page {
    margin: 1em 0;
    padding: 0;
    border: none;
    font-weight: 500;
  }

  p > .user {
    opacity: 0.5;
  }

  td > .user,
  td > time {
    white-space: nowrap;
  }

  input[type="checkbox"] {
    transform: scale(1.5);
    margin-right: 0.6em;
    vertical-align: middle;
  }

  p {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  .image {
    border: none;
    margin: 1.5em 0;
    padding: 0;
    border-radius: 0;
    text-align: center;
  }

  .code,
  code {
    background: rgba(135, 131, 120, 0.15);
    border-radius: 3px;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-size: 85%;
    tab-size: 2;
  }

  code {
    color: #eb5757;
  }

  .code {
    padding: 1.5em 1em;
  }

  .code > code {
    background: none;
    padding: 0;
    font-size: 100%;
    color: inherit;
  }

  blockquote {
    font-size: 1.25em;
    margin: 1em 0;
    padding-left: 1em;
    border-left: 3px solid rgb(55, 53, 47);
  }

  .bookmark-href {
    font-size: 0.75em;
    opacity: 0.5;
  }

  .sans { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol"; }
  .code { font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace; }
  .serif { font-family: Lyon-Text, Georgia, KaiTi, STKaiTi, '华文楷体', KaiTi_GB2312, '楷体_GB2312', serif; }
  .mono { font-family: Nitti, 'Microsoft YaHei', '微软雅黑', monospace; }
  .pdf .sans { font-family: Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC', 'Noto Sans CJK KR'; }

  .pdf .code { font-family: Source Code Pro, 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC', 'Noto Sans Mono CJK KR'; }

  .pdf .serif { font-family: PT Serif, Lyon-Text, Georgia, KaiTi, STKaiTi, '华文楷体', KaiTi_GB2312, '楷体_GB2312', serif, 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC', 'Noto Sans CJK KR'; }

  .pdf .mono { font-family: PT Mono, Nitti, 'Microsoft YaHei', '微软雅黑', monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC', 'Noto Sans Mono CJK KR'; }

  .highlight-default {
  }
  .highlight-gray {
    color: rgb(155,154,151);
  }
  .highlight-brown {
    color: rgb(100,71,58);
  }
  .highlight-orange {
    color: rgb(217,115,13);
  }
  .highlight-yellow {
    color: rgb(223,171,1);
  }
  .highlight-teal {
    color: rgb(15,123,108);
  }
  .highlight-blue {
    color: rgb(11,110,153);
  }
  .highlight-purple {
    color: rgb(105,64,165);
  }
  .highlight-pink {
    color: rgb(173,26,114);
  }
  .highlight-red {
    color: rgb(224,62,62);
  }
  .highlight-gray_background {
    background: rgb(235,236,237);
  }
  .highlight-brown_background {
    background: rgb(233,229,227);
  }
  .highlight-orange_background {
    background: rgb(250,235,221);
  }
  .highlight-yellow_background {
    background: rgb(251,243,219);
  }
  .highlight-teal_background {
    background: rgb(221,237,234);
  }
  .highlight-blue_background {
    background: rgb(221,235,241);
  }
  .highlight-purple_background {
    background: rgb(234,228,242);
  }
  .highlight-pink_background {
    background: rgb(244,223,235);
  }
  .highlight-red_background {
    background: rgb(251,228,228);
  }
  .block-color-default {
    color: inherit;
    fill: inherit;
  }
  .block-color-gray {
    color: rgba(55, 53, 47, 0.6);
    fill: rgba(55, 53, 47, 0.6);
  }
  .block-color-brown {
    color: rgb(100,71,58);
    fill: rgb(100,71,58);
  }
  .block-color-orange {
    color: rgb(217,115,13);
    fill: rgb(217,115,13);
  }
  .block-color-yellow {
    color: rgb(223,171,1);
    fill: rgb(223,171,1);
  }
  .block-color-teal {
    color: rgb(15,123,108);
    fill: rgb(15,123,108);
  }
  .block-color-blue {
    color: rgb(11,110,153);
    fill: rgb(11,110,153);
  }
  .block-color-purple {
    color: rgb(105,64,165);
    fill: rgb(105,64,165);
  }
  .block-color-pink {
    color: rgb(173,26,114);
    fill: rgb(173,26,114);
  }
  .block-color-red {
    color: rgb(224,62,62);
    fill: rgb(224,62,62);
  }
  .block-color-gray_background {
    background: rgb(235,236,237);
  }
  .block-color-brown_background {
    background: rgb(233,229,227);
  }
  .block-color-orange_background {
    background: rgb(250,235,221);
  }
  .block-color-yellow_background {
    background: rgb(251,243,219);
  }
  .block-color-teal_background {
    background: rgb(221,237,234);
  }
  .block-color-blue_background {
    background: rgb(221,235,241);
  }
  .block-color-purple_background {
    background: rgb(234,228,242);
  }
  .block-color-pink_background {
    background: rgb(244,223,235);
  }
  .block-color-red_background {
    background: rgb(251,228,228);
  }

  .checkbox {
    display: inline-flex;
    vertical-align: text-bottom;
    width: 16;
    height: 16;
    background-size: 16px;
    margin-left: 2px;
    margin-right: 5px;
  }

  .checkbox-on {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%2358A9D7%22%2F%3E%0A%3Cpath%20d%3D%22M6.71429%2012.2852L14%204.9995L12.7143%203.71436L6.71429%209.71378L3.28571%206.2831L2%207.57092L6.71429%2012.2852Z%22%20fill%3D%22white%22%2F%3E%0A%3C%2Fsvg%3E");
  }

  .checkbox-off {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20x%3D%220.75%22%20y%3D%220.75%22%20width%3D%2214.5%22%20height%3D%2214.5%22%20fill%3D%22white%22%20stroke%3D%22%2336352F%22%20stroke-width%3D%221.5%22%2F%3E%0A%3C%2Fsvg%3E");
  }

</style></head><body><article id="e2d7df22-ce3e-4eaa-a8f7-eeee40421180" class="page sans"><header><h1 class="page-title">FlashVPN 服务条款</h1></header><div class="page-body"><p id="764f9392-1804-42bd-95b3-f2d92d57a0e8" class="">
</p><p id="ade15010-bdda-44a3-93f1-418a38caba48" class="">本服务条款文件（“<strong>条款</strong>”）概述了FlashVPN Network 提供的 VPN 服务（“<strong>服务</strong>”或“<strong>服务</strong>”）的使用条款和条件。（“ <strong>FlashVPN</strong> ”）。这些条款还管辖 FlashVPN服务（“<strong>服务</strong>”）的使用和访问，其中包括 FlashVPN 的网站（“<strong>网站</strong>”）、应用程序（“<strong>应用程序</strong>”）以及 FlashVPN 提供的任何软件（“<strong>软件</strong>” ）。</p><p id="4216a61e-12c2-4b79-b888-560717d87162" class="">同意这些条款，即表示您同意隐私政策（“<strong>隐私政策</strong>”）， 您可以在<a href="https://flashvpn.io/static/privacy.html">此处</a>阅读。</p><p id="************************************" class="">
</p><h1 id="6e23467b-7861-44d0-b6c3-6ffb2241d32f" class="">1. 验收</h1><p id="170ca6fe-bbea-4aaa-855d-ea727a302e08" class="">访问内容或服务，即表示您代表您自己或您所代表的人（“<strong>您</strong>”）同意全部遵守这些条款并受其法律约束。这些条款构成您与 FlashVPN 之间具有法律约束力的协议（“<strong>协议</strong>”）。如果您不同意本条款的任何部分，则不得使用我们的服务。</p><p id="912aa0ec-9d95-49c4-b8a3-f40c53c8ce3a" class="">通过创建使用我们<strong>服务</strong>的帐户，您声明您已年满十八 (18) 岁或您是有效的法人实体，并且您提供的注册信息是准确和完整的。</p><p id="d656a50b-9850-403a-a74a-3fae8f1b6dea" class="">
</p><h1 id="1d8ce100-74d9-4a4d-b603-da5b107dbbb8" class="">2. 修改</h1><p id="53683282-b334-451b-b22b-885f16eeeaff" class="">FlashVPN 可能会不时更新条款，恕不另行通知。如果您在这些更改生效后继续使用 FlashVPN 的<strong>服务</strong>，则表示您同意修改后的条款。条款的当前版本可在网站上找到。您理解并同意，您有义务不时查看这些条款，以便随时了解当前的规则和义务。有关条款的任何核心更改的通知将通过电子邮件或网站更新提供给订阅者。您在这些条款更改后使用<strong>服务</strong>即表示您接受更改后的条款。</p><p id="f7fe149b-b2bd-4e75-83e1-6ec2e5fcc397" class="">
</p><p id="481ae788-5122-4847-8dab-4543fab8c84a" class="">
</p><h1 id="c93118a6-9abd-4638-be4a-74187ec42080" class="">3. 服务寿命</h1><p id="9617b871-8dd6-47fc-96fb-e9495cdec834" class="">在网站上购买服务包后，您即可使用FlashVPN<strong>服务</strong>。FlashVPN 保留在网站上发布或通过电子邮件发送的合理提前通知后随时修改订阅费用或制定新费用的权利。定价的任何更改都不会影响订阅者当前的订阅期，并将在订阅续订时生效。订阅购买和退款通过多家第三方支付公司处理。</p><p id="bb74cf2c-493f-4a22-a82a-f03ec2b10cd2" class="">该服务不支持自动续订，到期发票将在服务到期前 15 天的香港时间上午 12 点通过电子邮件或通过我们的应用程序通知发送给您。 </p><p id="1637aca0-9066-451d-9497-1c77e5e8c385" class="">
</p><p id="1c5b8260-59ea-42d8-8a03-c86812d51c41" class="">
</p><h1 id="563f0c5e-b770-4ab3-913f-7d277baf4e08" class="">4. 试用和退款政策</h1><p id="ebdd4a53-9b87-4b12-a760-d41f5caf8431" class="">FlashVPN为新用户提供Trail服务，当我们的服务质量达到您的期望时，您可以通过支付相应的发票继续使用我们的服务，否则请立即停止使用我们的服务。但是，一旦您成为付费客户，我们将不提供任何退款机制。当然，我们非常重视我们的声誉，并将不断提高我们的质量。</p><p id="7167471e-a814-4c8e-9bb0-924b226eedf5" class="">
</p><h1 id="a6e14aa5-50e8-405d-b7cb-a07014e2b582" class="">5. 免责声明</h1><p id="087af69f-e239-4918-88fa-40aa5a86c6a4" class="">我们将努力防止对网站和服务的干扰。但是，这些是在“原样”和“可用”的基础上提供的，我们不保证，无论是明示还是暗示，通过网站或服务提供的任何材料或信息的准确性，或其适用性任何特定目的。我们明确否认任何形式的保证，无论是明示的还是暗示的，包括但不限于适销性或特定用途适用性或不侵权的保证。我们不保证服务将满足您的要求，或者服务不会中断、及时、安全或没有错误，或者缺陷（如果有）将得到纠正。您承认您自行承担风险和酌情权访问本网站和服务。</p><p id="12be2e7a-0c39-4272-ba3c-5de5c7731f9a" class="">VPN 服务覆盖范围、速度、服务器位置和质量可能会有所不同。FlashVPN 将始终尝试使服务可用。但是，服务可能会因我们无法控制的各种因素而无法使用，包括但不限于紧急情况；第三方服务故障；传输、设备或网络问题或限制、干扰或信号强度；并且可能会被打断、拒绝、限制或缩减。对于因服务、通信服务或网络的中断或性能问题而导致的数据、消息或页面丢失、未交付、延迟或误导，我们概不负责。我们可以自行决定施加使用或服务限制、暂停服务、终止 VPN 帐户或阻止某些类型的使用，以保护订阅者或服务。不保证收到的数据的准确性和及时性；可能会出现延误或遗漏。</p><p id="bb3519ef-cad8-47e0-b7a5-16c3d18bd8ea" class="">FlashVPN 保留调查我们认为违反这些条款的事项的权利。我们可以但没有义务自行决定在不通知的情况下以任何方式删除、阻止、过滤或限制我们认为实际或潜在违反这些条款中规定的限制的任何材料或信息，以及可能使 FlashVPN 或我们的客户承担责任的任何其他活动。FlashVPN 对我们未能阻止此类材料或信息通过服务传输和/或传输到您的计算设备不承担任何责任。</p><p id="85660b0a-3b1d-456a-901c-c991ad593351" class="">
</p><p id="95ec45dc-46a9-4b3a-a8e3-ce8203de74a9" class="">
</p><h1 id="0ca1b60a-dbff-4c16-adcb-12b393729cde" class="">6. 责任限制</h1><p id="bc749367-203e-4b4e-85ac-ff9e416bf940" class="">FlashVPN 不对任何订户或其他个人在以下情况下遭受的任何损失或损害承担任何责任：</p><ol id="685a1774-e135-483e-b69a-a97eef25af14" class="numbered-list" start="1"><li>网站或服务的任何故障或中断；</li></ol><ol id="2b498374-a8dd-4322-9a74-509be0a49260" class="numbered-list" start="2"><li>向您提供网站或服务或其中包含的数据所涉及的任何第三方的任何作为或不作为；</li></ol><ol id="35319df0-0a5d-4783-8dde-827dcb373b46" class="numbered-list" start="3"><li>与您访问或使用或无法访问或使用本网站或其内容的任何部分有关的任何其他原因；</li></ol><ol id="2b290aa8-e70f-47a7-b3e5-3f246cd61539" class="numbered-list" start="4"><li>您在网站或服务上的互动；</li></ol><ol id="aaddcea9-946a-4e23-9050-80b013ebdddf" class="numbered-list" start="5"><li>您未能遵守本协议；</li></ol><ol id="578e6ebd-aca3-4ee3-87b1-f7013d07657e" class="numbered-list" start="6"><li>采购替代商品或服务的成本；或者</li></ol><ol id="538924f4-46f9-44c9-a5cd-b266b1d64be6" class="numbered-list" start="7"><li>未经授权访问或更改您的传输或数据，无论导致此类原因的情况是否在 FlashVPN 或为网站或服务提供软件、服务或支持的任何供应商的控制范围内。</li></ol><p id="35c89dc2-9758-4166-a018-8d1bdd393b1f" class="">在任何情况下，FlashVPN、其合作伙伴、关联公司、子公司、成员、管理人员或员工均不对任何直接、特殊、间接、后果性或附带损害或任何其他损失或损害承担任何责任，即使他们有被告知其可能性。上述规定不适用于适用法律禁止的范围。</p><p id="14fed4a5-8520-49d0-ae6d-3b92bdf9522a" class="">
</p><p id="6f7e0773-275e-49d3-a5bc-bef5cc5a01b1" class="">FlashVPN 网络</p></div></article></body></html>
