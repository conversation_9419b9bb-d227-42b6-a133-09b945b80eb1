<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/><title>FlashVPN Terms of Service</title><style>
  /* webkit printing magic: print all background colors */
  html {
    -webkit-print-color-adjust: exact;
  }
  * {
    box-sizing: border-box;
    -webkit-print-color-adjust: exact;
  }

  html,
  body {
    margin: 0;
    padding: 0;
  }
  @media only screen {
    body {
      margin: 2em auto;
      max-width: 900px;
      color: rgb(55, 53, 47);
    }
  }

  body {
    line-height: 1.5;
    white-space: pre-wrap;
  }

  a,
  a.visited {
    color: inherit;
    text-decoration: underline;
  }

  .pdf-relative-link-path {
    font-size: 80%;
    color: #444;
  }

  h1,
  h2,
  h3 {
    letter-spacing: -0.01em;
    line-height: 1.2;
    font-weight: 600;
    margin-bottom: 0;
  }

  .page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 0.75em;
  }

  h1 {
    font-size: 1.875rem;
    margin-top: 1.875rem;
  }

  h2 {
    font-size: 1.5rem;
    margin-top: 1.5rem;
  }

  h3 {
    font-size: 1.25rem;
    margin-top: 1.25rem;
  }

  .source {
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 1.5em;
    word-break: break-all;
  }

  .callout {
    border-radius: 3px;
    padding: 1rem;
  }

  figure {
    margin: 1.25em 0;
    page-break-inside: avoid;
  }

  figcaption {
    opacity: 0.5;
    font-size: 85%;
    margin-top: 0.5em;
  }

  mark {
    background-color: transparent;
  }

  .indented {
    padding-left: 1.5em;
  }

  hr {
    background: transparent;
    display: block;
    width: 100%;
    height: 1px;
    visibility: visible;
    border: none;
    border-bottom: 1px solid rgba(55, 53, 47, 0.09);
  }

  img {
    max-width: 100%;
  }

  @media only print {
    img {
      max-height: 100vh;
      object-fit: contain;
    }
  }

  @page {
    margin: 1in;
  }

  .collection-content {
    font-size: 0.875rem;
  }

  .column-list {
    display: flex;
    justify-content: space-between;
  }

  .column {
    padding: 0 1em;
  }

  .column:first-child {
    padding-left: 0;
  }

  .column:last-child {
    padding-right: 0;
  }

  .table_of_contents-item {
    display: block;
    font-size: 0.875rem;
    line-height: 1.3;
    padding: 0.125rem;
  }

  .table_of_contents-indent-1 {
    margin-left: 1.5rem;
  }

  .table_of_contents-indent-2 {
    margin-left: 3rem;
  }

  .table_of_contents-indent-3 {
    margin-left: 4.5rem;
  }

  .table_of_contents-link {
    text-decoration: none;
    opacity: 0.7;
    border-bottom: 1px solid rgba(55, 53, 47, 0.18);
  }

  table,
  th,
  td {
    border: 1px solid rgba(55, 53, 47, 0.09);
    border-collapse: collapse;
  }

  table {
    border-left: none;
    border-right: none;
  }

  th,
  td {
    font-weight: normal;
    padding: 0.25em 0.5em;
    line-height: 1.5;
    min-height: 1.5em;
    text-align: left;
  }

  th {
    color: rgba(55, 53, 47, 0.6);
  }

  ol,
  ul {
    margin: 0;
    margin-block-start: 0.6em;
    margin-block-end: 0.6em;
  }

  li > ol:first-child,
  li > ul:first-child {
    margin-block-start: 0.6em;
  }

  ul > li {
    list-style: disc;
  }

  ul.to-do-list {
    text-indent: -1.7em;
  }

  ul.to-do-list > li {
    list-style: none;
  }

  .to-do-children-checked {
    text-decoration: line-through;
    opacity: 0.375;
  }

  ul.toggle > li {
    list-style: none;
  }

  ul {
    padding-inline-start: 1.7em;
  }

  ul > li {
    padding-left: 0.1em;
  }

  ol {
    padding-inline-start: 1.6em;
  }

  ol > li {
    padding-left: 0.2em;
  }

  .mono ol {
    padding-inline-start: 2em;
  }

  .mono ol > li {
    text-indent: -0.4em;
  }

  .toggle {
    padding-inline-start: 0em;
    list-style-type: none;
  }

  /* Indent toggle children */
  .toggle > li > details {
    padding-left: 1.7em;
  }

  .toggle > li > details > summary {
    margin-left: -1.1em;
  }

  .selected-value {
    display: inline-block;
    padding: 0 0.5em;
    background: rgba(206, 205, 202, 0.5);
    border-radius: 3px;
    margin-right: 0.5em;
    margin-top: 0.3em;
    margin-bottom: 0.3em;
    white-space: nowrap;
  }

  .collection-title {
    display: inline-block;
    margin-right: 1em;
  }

  time {
    opacity: 0.5;
  }

  .icon {
    display: inline-block;
    max-width: 1.2em;
    max-height: 1.2em;
    text-decoration: none;
    vertical-align: text-bottom;
    margin-right: 0.5em;
  }

  img.icon {
    border-radius: 3px;
  }

  .user-icon {
    width: 1.5em;
    height: 1.5em;
    border-radius: 100%;
    margin-right: 0.5rem;
  }

  .user-icon-inner {
    font-size: 0.8em;
  }

  .text-icon {
    border: 1px solid #000;
    text-align: center;
  }

  .page-cover-image {
    display: block;
    object-fit: cover;
    width: 100%;
    height: 30vh;
  }

  .page-header-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .page-header-icon-with-cover {
    margin-top: -0.72em;
    margin-left: 0.07em;
  }

  .page-header-icon img {
    border-radius: 3px;
  }

  .link-to-page {
    margin: 1em 0;
    padding: 0;
    border: none;
    font-weight: 500;
  }

  p > .user {
    opacity: 0.5;
  }

  td > .user,
  td > time {
    white-space: nowrap;
  }

  input[type="checkbox"] {
    transform: scale(1.5);
    margin-right: 0.6em;
    vertical-align: middle;
  }

  p {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  .image {
    border: none;
    margin: 1.5em 0;
    padding: 0;
    border-radius: 0;
    text-align: center;
  }

  .code,
  code {
    background: rgba(135, 131, 120, 0.15);
    border-radius: 3px;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-size: 85%;
    tab-size: 2;
  }

  code {
    color: #eb5757;
  }

  .code {
    padding: 1.5em 1em;
  }

  .code > code {
    background: none;
    padding: 0;
    font-size: 100%;
    color: inherit;
  }

  blockquote {
    font-size: 1.25em;
    margin: 1em 0;
    padding-left: 1em;
    border-left: 3px solid rgb(55, 53, 47);
  }

  .bookmark-href {
    font-size: 0.75em;
    opacity: 0.5;
  }

  .sans { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol"; }
  .code { font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace; }
  .serif { font-family: Lyon-Text, Georgia, KaiTi, STKaiTi, '华文楷体', KaiTi_GB2312, '楷体_GB2312', serif; }
  .mono { font-family: Nitti, 'Microsoft YaHei', '微软雅黑', monospace; }
  .pdf .sans { font-family: Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC', 'Noto Sans CJK KR'; }

  .pdf .code { font-family: Source Code Pro, 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC', 'Noto Sans Mono CJK KR'; }

  .pdf .serif { font-family: PT Serif, Lyon-Text, Georgia, KaiTi, STKaiTi, '华文楷体', KaiTi_GB2312, '楷体_GB2312', serif, 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC', 'Noto Sans CJK KR'; }

  .pdf .mono { font-family: PT Mono, Nitti, 'Microsoft YaHei', '微软雅黑', monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC', 'Noto Sans Mono CJK KR'; }

  .highlight-default {
  }
  .highlight-gray {
    color: rgb(155,154,151);
  }
  .highlight-brown {
    color: rgb(100,71,58);
  }
  .highlight-orange {
    color: rgb(217,115,13);
  }
  .highlight-yellow {
    color: rgb(223,171,1);
  }
  .highlight-teal {
    color: rgb(15,123,108);
  }
  .highlight-blue {
    color: rgb(11,110,153);
  }
  .highlight-purple {
    color: rgb(105,64,165);
  }
  .highlight-pink {
    color: rgb(173,26,114);
  }
  .highlight-red {
    color: rgb(224,62,62);
  }
  .highlight-gray_background {
    background: rgb(235,236,237);
  }
  .highlight-brown_background {
    background: rgb(233,229,227);
  }
  .highlight-orange_background {
    background: rgb(250,235,221);
  }
  .highlight-yellow_background {
    background: rgb(251,243,219);
  }
  .highlight-teal_background {
    background: rgb(221,237,234);
  }
  .highlight-blue_background {
    background: rgb(221,235,241);
  }
  .highlight-purple_background {
    background: rgb(234,228,242);
  }
  .highlight-pink_background {
    background: rgb(244,223,235);
  }
  .highlight-red_background {
    background: rgb(251,228,228);
  }
  .block-color-default {
    color: inherit;
    fill: inherit;
  }
  .block-color-gray {
    color: rgba(55, 53, 47, 0.6);
    fill: rgba(55, 53, 47, 0.6);
  }
  .block-color-brown {
    color: rgb(100,71,58);
    fill: rgb(100,71,58);
  }
  .block-color-orange {
    color: rgb(217,115,13);
    fill: rgb(217,115,13);
  }
  .block-color-yellow {
    color: rgb(223,171,1);
    fill: rgb(223,171,1);
  }
  .block-color-teal {
    color: rgb(15,123,108);
    fill: rgb(15,123,108);
  }
  .block-color-blue {
    color: rgb(11,110,153);
    fill: rgb(11,110,153);
  }
  .block-color-purple {
    color: rgb(105,64,165);
    fill: rgb(105,64,165);
  }
  .block-color-pink {
    color: rgb(173,26,114);
    fill: rgb(173,26,114);
  }
  .block-color-red {
    color: rgb(224,62,62);
    fill: rgb(224,62,62);
  }
  .block-color-gray_background {
    background: rgb(235,236,237);
  }
  .block-color-brown_background {
    background: rgb(233,229,227);
  }
  .block-color-orange_background {
    background: rgb(250,235,221);
  }
  .block-color-yellow_background {
    background: rgb(251,243,219);
  }
  .block-color-teal_background {
    background: rgb(221,237,234);
  }
  .block-color-blue_background {
    background: rgb(221,235,241);
  }
  .block-color-purple_background {
    background: rgb(234,228,242);
  }
  .block-color-pink_background {
    background: rgb(244,223,235);
  }
  .block-color-red_background {
    background: rgb(251,228,228);
  }

  .checkbox {
    display: inline-flex;
    vertical-align: text-bottom;
    width: 16;
    height: 16;
    background-size: 16px;
    margin-left: 2px;
    margin-right: 5px;
  }

  .checkbox-on {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%2358A9D7%22%2F%3E%0A%3Cpath%20d%3D%22M6.71429%2012.2852L14%204.9995L12.7143%203.71436L6.71429%209.71378L3.28571%206.2831L2%207.57092L6.71429%2012.2852Z%22%20fill%3D%22white%22%2F%3E%0A%3C%2Fsvg%3E");
  }

  .checkbox-off {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20x%3D%220.75%22%20y%3D%220.75%22%20width%3D%2214.5%22%20height%3D%2214.5%22%20fill%3D%22white%22%20stroke%3D%22%2336352F%22%20stroke-width%3D%221.5%22%2F%3E%0A%3C%2Fsvg%3E");
  }

</style></head><body><article id="e2d7df22-ce3e-4eaa-a8f7-eeee40421180" class="page sans"><header><h1 class="page-title">FlashVPN Terms of Service</h1></header><div class="page-body"><p id="764f9392-1804-42bd-95b3-f2d92d57a0e8" class="">
</p><p id="ade15010-bdda-44a3-93f1-418a38caba48" class="">This Terms of Service document (the “<strong>Terms</strong>”) outlines the terms and conditions of use of the VPN services (the “<strong>Services</strong>” or “<strong>Service</strong>”) provided by FlashVPN Network. (“<strong>FlashVPN</strong>”). These Terms also govern the use of and access to FlashVPN&#x27;s <strong>Services</strong> (the “<strong>Services</strong>”), which includes the FlashVPN&#x27;s website (the “<strong>Site</strong>”), applications (the “<strong>Apps</strong>”), and any software provided by FlashVPN (the “<strong>Software</strong>”).</p><p id="4216a61e-12c2-4b79-b888-560717d87162" class="">By agreeing to these Terms, you are also agreeing to the Privacy Policy (“<strong>Privacy Policy</strong>”), which you can read <a href="https://flashvpn.io/static/privacy.html">here</a>.</p><p id="************************************" class="">
</p><h1 id="6e23467b-7861-44d0-b6c3-6ffb2241d32f" class="">1. Acceptance</h1><p id="170ca6fe-bbea-4aaa-855d-ea727a302e08" class="">By accessing the Content or Services, you are agreeing on behalf of yourself or those you represent (“you”) to comply with and be legally bound by these Terms in their entirety. These Terms constitute a legally binding agreement (the “<strong>Agreement</strong>”) between you and FlashVPN. If you do not agree with any part of the Terms, you may not use our Services.</p><p id="912aa0ec-9d95-49c4-b8a3-f40c53c8ce3a" class="">By creating an account for using our <strong>Services</strong>, you represent that you are at least eighteen (18) years of age or that you are a valid legal entity, and that the registration information you have provided is accurate and complete.</p><p id="d656a50b-9850-403a-a74a-3fae8f1b6dea" class="">
</p><h1 id="1d8ce100-74d9-4a4d-b603-da5b107dbbb8" class="">2. Modification</h1><p id="********-b334-451b-b22b-885f16eeeaff" class="">FlashVPN may update the Terms from time to time without notice. If you continue to use FlashVPN&#x27;s <strong>Services </strong>after these changes take effect, then you agree to the revised Terms. The current version of the Terms is available on the Site. You understand and agree that it is your obligation to review these Terms from time to time in order to stay informed on current rules and obligations. Notification on any core changes to the Terms will be provided to subscribers through an email message or update to the Site. Your use of <strong>Services</strong> following the changes to these Terms constitutes your acceptance of the changed Terms.</p><p id="f7fe149b-b2bd-4e75-83e1-6ec2e5fcc397" class="">
</p><p id="481ae788-5122-4847-8dab-4543fab8c84a" class="">
</p><h1 id="c93118a6-9abd-4638-be4a-74187ec42080" class="">3. Services lifetime</h1><p id="9617b871-8dd6-47fc-96fb-e9495cdec834" class="">FlashVPN <strong>Services</strong> are available to you upon purchase a service package on the Site. FlashVPN reserves the right to amend subscription fees or institute new fees at any time upon reasonable advance notice posted on the Site or sent via email. Any changes to the pricing will not affect Subscriber’s current subscription period and will become effective upon subscription renewal. Subscription purchases and refunds are handled via multiple third-party payment companies.</p><p id="bb74cf2c-493f-4a22-a82a-f03ec2b10cd2" class="">Auto-renewal is not supported on the service, a due invoice will be automatically generated and sent to you by email or notification on our apps, at the 12 am hong kong time 15 days prior to the expired date of service. </p><p id="1637aca0-9066-451d-9497-1c77e5e8c385" class="">
</p><p id="1c5b8260-59ea-42d8-8a03-c86812d51c41" class="">
</p><h1 id="563f0c5e-b770-4ab3-913f-7d277baf4e08" class="">4. Trial and Refund Policy</h1><p id="ebdd4a53-9b87-4b12-a760-d41f5caf8431" class="">FlashVPN provides the new user a Trail service,  and when our quality of service meets up your expectations, you continue using our service by paying corresponding invoices, if not please stop using our services immediately. However, once you become a paid customer, we don&#x27;t provide any refund mechanism. Of course, we value our reputation very much and will keep improving our quality.</p><p id="7167471e-a814-4c8e-9bb0-924b226eedf5" class="">
</p><h1 id="a6e14aa5-50e8-405d-b7cb-a07014e2b582" class="">5. Disclaimers</h1><p id="087af69f-e239-4918-88fa-40aa5a86c6a4" class="">We will strive to prevent interruptions to the Site and Services. However, these are provided on an “as-is” and “as-available” basis, and we do not warrant, either expressly or by implication, the accuracy of any materials or information provided through the Site or Service, or their suitability for any particular purpose. We expressly disclaim all warranties of any kind, whether express or implied, including but not limited to warranties of merchantability or fitness for a particular purpose, or non-infringement. We do not make any warranty that the Services will meet your requirements, or that it will be uninterrupted, timely, secure, or error-free, or that defects, if any, will be corrected. You acknowledge that you access the Site and Services at your sole risk and discretion.</p><p id="12be2e7a-0c39-4272-ba3c-5de5c7731f9a" class="">VPN service coverage, speeds, server locations, and quality may vary. FlashVPN will attempt to make the Service available at all times. However, the Service may be subject to unavailability for a variety of factors beyond our control, including but not limited to emergencies; third-party-service failures; or transmission, equipment, or network problems or limitations, interference, or signal strength; and may be interrupted, refused, limited, or curtailed. We are not responsible for data, messages, or pages lost, not delivered, delayed, or misdirected because of interruptions or performance issues with the Service, communications services, or networks. We may impose usage or Service limits, suspend Service, terminate VPN accounts, or block certain kinds of usage in our sole discretion to protect Subscribers or the Service. The accuracy and timeliness of data received is not guaranteed; delays or omissions may occur.</p><p id="bb3519ef-cad8-47e0-b7a5-16c3d18bd8ea" class="">FlashVPN reserves the right to investigate matters we consider to be violations of these Terms. We may, but are not obligated to, in our sole discretion and without notice, remove, block, filter, or restrict by any means any materials or information that we consider to be actual or potential violations of the restrictions set forth in these Terms, and any other activities that may subject FlashVPN or our customers to liability. FlashVPN disclaims any and all liability for any failure on our part to prevent such materials or information from being transmitted over the Service and/or into your computing device.</p><p id="85660b0a-3b1d-456a-901c-c991ad593351" class="">
</p><p id="95ec45dc-46a9-4b3a-a8e3-ce8203de74a9" class="">
</p><h1 id="0ca1b60a-dbff-4c16-adcb-12b393729cde" class="">6. Limitations of Liability</h1><p id="bc749367-203e-4b4e-85ac-ff9e416bf940" class="">FlashVPN shall not be liable and shall not have responsibility of any kind to any Subscriber or other individual for any loss or damage that you incur in the event of:</p><ol id="685a1774-e135-483e-b69a-a97eef25af14" class="numbered-list" start="1"><li>any failure or interruption of the Site or Service;</li></ol><ol id="2b498374-a8dd-4322-9a74-509be0a49260" class="numbered-list" start="2"><li>any act or omission of any Third Party involved in making the Site or Service or the data contained therein available to you;</li></ol><ol id="35319df0-0a5d-4783-8dde-827dcb373b46" class="numbered-list" start="3"><li>any other cause relating to your access or use, or inability to access or use, any portion of the Site or its Content;</li></ol><ol id="2b290aa8-e70f-47a7-b3e5-3f246cd61539" class="numbered-list" start="4"><li>your interactions on the Site or Service;</li></ol><ol id="aaddcea9-946a-4e23-9050-80b013ebdddf" class="numbered-list" start="5"><li>your failure to comply with this Agreement;</li></ol><ol id="578e6ebd-aca3-4ee3-87b1-f7013d07657e" class="numbered-list" start="6"><li>the cost of procurement of substitute goods or services; or</li></ol><ol id="538924f4-46f9-44c9-a5cd-b266b1d64be6" class="numbered-list" start="7"><li>unauthorized access to or alteration of your transmissions or data, whether or not the circumstances giving rise to such cause may have been within the control of FlashVPN or of any vendor providing software, services, or support for the Site or Service.</li></ol><p id="35c89dc2-9758-4166-a018-8d1bdd393b1f" class="">In no event will FlashVPN, its partners, affiliates, subsidiaries, members, officers, or employees be liable for any direct, special, indirect, consequential, or incidental damages, or for any other loss or damages of any kind, even if they have been advised of the possibility thereof. The foregoing shall not apply to the extent prohibited by applicable law.</p><p id="14fed4a5-8520-49d0-ae6d-3b92bdf9522a" class="">
</p><p id="6f7e0773-275e-49d3-a5bc-bef5cc5a01b1" class="">FlashVPN Network</p></div></article></body></html>
