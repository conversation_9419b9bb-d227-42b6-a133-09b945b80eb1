<!DOCTYPE html>
<html lang="en" style="height: 100%">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <title>跳转中...</title>
  <script src="/static/vue.3.2.37.global.min.js"></script>
</head>
<body>
<div id="app">
  <form id="dopay" :action="url" method="post" v-if="checkParamsPass">
    <slot v-for="item in paymentData">
      <input type="hidden" :name="item.name" :value="item.value">
    </slot>
    <input type="submit" value="跳转中">
  </form>
  <div v-else>参数错误，请重新支付或联系客服。</div>
</div>
<script>
  const getQueryString = (name) => {
    const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
    const r = window.location.search.substr(1).match(reg);
    if (r != null) {
      return unescape(r[2]);
    }
    return null;
  }
  let isValid = true;

  const jsonToArray = (json) => {
    const keys = Object.keys(json);
    let tempArray = [];
    keys.map(value => {
      tempArray.push({name: value, value: json[value]});
      if (!json[value]) {
        isValid = false;
      }
    });
    return tempArray;
  }

  const Redirect = {
    data() {
      const pid = getQueryString("pid");
      const type = getQueryString("type");
      const notify_url = getQueryString("notify_url");
      const return_url = getQueryString("return_url");
      const out_trade_no = getQueryString("out_trade_no");
      const name = getQueryString("name");
      const money = getQueryString("money");
      const clientip = getQueryString("clientip");
      const sign = getQueryString("sign");
      const sign_type = getQueryString("sign_type");
      const url = getQueryString('url')
      const paymentData = {
        pid,
        type,
        notify_url,
        return_url,
        out_trade_no,
        name,
        money,
        clientip,
        sign,
        sign_type,
      };
      return {
        paymentData: jsonToArray(paymentData),
        checkParamsPass: isValid,
        url,
      };
    },
    mounted: () => {
      if (isValid) {
        document.getElementById('dopay').submit();
      }
    }
  }
  Vue.createApp(Redirect).mount('#app');
</script>
</body>
</html>
