<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="ng2.template" target-language="zh-TW">
    <body>
      <trans-unit id="248ffe8270f5c960463d8014fbdc4a9966c87abe" datatype="html">
        <source>Account Balance</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/account/my-account/my-account.component.html</context>
          <context context-type="linenumber">6</context>
        </context-group>
        <target state="needs-translation">Account Balance</target>
      </trans-unit>
      <trans-unit id="33010cf236a957021f3bae3266426715881751dd" datatype="html">
        <source>recharge</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/account/my-account/my-account.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
        <target state="needs-translation">recharge</target>
      </trans-unit>
      <trans-unit id="1fb8945c83b8c762e8ab477fb4d8152c1647f880" datatype="html">
        <source>Balance Details</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/account/my-account/my-account.component.html</context>
          <context context-type="linenumber">14</context>
        </context-group>
        <target state="needs-translation">Balance Details</target>
      </trans-unit>
      <trans-unit id="efe80dce26451bb0dcf746d04d3fa534c665692e" datatype="html">
        <source> Recharge </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/account/my-account/my-account.component.html</context>
          <context context-type="linenumber">17,19</context>
        </context-group>
        <target state="needs-translation"> Recharge </target>
      </trans-unit>
      <trans-unit id="df04d10cd73c676603a27168944daf422c09209b" datatype="html">
        <source> Consumption </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/account/my-account/my-account.component.html</context>
          <context context-type="linenumber">20,22</context>
        </context-group>
        <target state="needs-translation"> Consumption </target>
      </trans-unit>
      <trans-unit id="58c5b6f02278abaca2bf43df4290ab3b365f46a6" datatype="html">
        <source>No records</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/account/my-account/my-account.component.html</context>
          <context context-type="linenumber">63</context>
        </context-group>
        <target state="needs-translation">No records</target>
      </trans-unit>
      <trans-unit id="323fe90061f7b5d5e5faeb7fb8316edd62f7bc0b" datatype="html">
        <source> Successful Recharge </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/account/pay-status/pay-status.component.html</context>
          <context context-type="linenumber">20,22</context>
        </context-group>
        <target state="needs-translation"> Successful Recharge </target>
      </trans-unit>
      <trans-unit id="3dc6073b304eb733206059e0b7dcde1a7d3c7c4c" datatype="html">
        <source>Balance Recharge</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/account/recharge/recharge.component.html</context>
          <context context-type="linenumber">15</context>
        </context-group>
        <target state="needs-translation">Balance Recharge</target>
      </trans-unit>
      <trans-unit id="d62e8cfcb1f444a033b402e050b851a7d8a3532f" datatype="html">
        <source>Payment Method</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/account/recharge/recharge.component.html</context>
          <context context-type="linenumber">28</context>
        </context-group>
        <target state="needs-translation">Payment Method</target>
      </trans-unit>
      <trans-unit id="c07079875b5faa50b41d4a68e5322dd381141db0" datatype="html">
        <source>Recharge Now</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/account/recharge/recharge.component.html</context>
          <context context-type="linenumber">44</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/account/recharge/recharge.component.html</context>
          <context context-type="linenumber">45</context>
        </context-group>
        <target state="needs-translation">Recharge Now</target>
      </trans-unit>
      <trans-unit id="ac9ca0cb7cca3231493d9cc9be5bb102f9de52da" datatype="html">
        <source>FlashVPN</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">205</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">179</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">182</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">323</context>
        </context-group>
        <target state="translated">Flash加速器</target>
      </trans-unit>
      <trans-unit id="2c6df42ae9eb4f9d59bc27179f1c7fd3f9a63a7d" datatype="html">
        <source>Verify Email</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">211</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/noservice/noservice.component.html</context>
          <context context-type="linenumber">17</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-verify/signup-verify.component.html</context>
          <context context-type="linenumber">131</context>
        </context-group>
        <target state="translated">驗證郵箱</target>
      </trans-unit>
      <trans-unit id="419d940613972cc3fae9c8ea0a4306dbf80616e5" datatype="html">
        <source>Services</source>
        <target>服務</target>
      </trans-unit>
      <trans-unit id="myservices_menu" datatype="html">
        <source>My Services</source>
        <target>我的服務</target>
      </trans-unit>
      <trans-unit id="c0d4baf68f21ee9c4246d66f0ac828c5b73ae745" datatype="html">
        <source>Wallet</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">240</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">189</context>
        </context-group>
        <target state="translated">錢包</target>
      </trans-unit>
      <trans-unit id="ba4f24bf9bf3dc4db3d6bc1b8b63339295f0b806" datatype="html">
        <source>Sign In</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">251</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">184</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">334</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-success/signup-success.component.html</context>
          <context context-type="linenumber">17</context>
        </context-group>
        <target state="translated">登入</target>
      </trans-unit>
      <trans-unit id="28629af803ab8b5f6e995b17d486e6a1527e2a96" datatype="html">
        <source>Sign Up</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">259</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">183</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">333</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signin/signin.component.html</context>
          <context context-type="linenumber">295</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signup/signup.component.html</context>
          <context context-type="linenumber">296</context>
        </context-group>
        <target state="translated">註冊</target>
      </trans-unit>
      <trans-unit id="798754666f9784ba56c8a52d5a0c8df3424cf965" datatype="html">
        <source>Welfare Center</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">266</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">190</context>
        </context-group>
        <target state="needs-translation">Welfare Center</target>
      </trans-unit>
      <trans-unit id="38cbf4da03b0c84e5caeebb9cb49e31aa97d5edb" datatype="html">
        <source>APPS</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">273</context>
        </context-group>
        <target state="needs-translation">APPS</target>
      </trans-unit>
      <trans-unit id="69703274543d03e3dcf78103d49d441c84f72252" datatype="html">
        <source>Help Center</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">281</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">193</context>
        </context-group>
        <target state="translated">幫助中心</target>
      </trans-unit>
      <trans-unit id="121cc5391cd2a5115bc2b3160379ee5b36cd7716" datatype="html">
        <source>Settings</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">299</context>
        </context-group>
        <target state="translated">設置</target>
      </trans-unit>
      <trans-unit id="bb694b49d408265c91c62799c2b3a7e3151c824d" datatype="html">
        <source>Logout</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">307</context>
        </context-group>
        <target state="needs-translation">Logout</target>
      </trans-unit>
      <trans-unit id="copied" datatype="html">
        <source>Copied</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">159</context>
        </context-group>
        <target state="needs-translation">Copied</target>
      </trans-unit>
      <trans-unit id="chrome-login-hint" datatype="html">
        <source>Login successfully, please reopen our chrome extension again</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">166</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">83</context>
        </context-group>
        <target state="translated">登陸成功，請再次打開chrome插件。</target>
      </trans-unit>
      <trans-unit id="module-users" datatype="html">
        <source>users</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">220</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">60</context>
        </context-group>
        <target state="needs-translation">users</target>
      </trans-unit>
      <trans-unit id="module-services" datatype="html">
        <source>services</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">222</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">61</context>
        </context-group>
        <target state="needs-translation">services</target>
      </trans-unit>
      <trans-unit id="module-billings" datatype="html">
        <source>billings</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">224</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">62</context>
        </context-group>
        <target state="needs-translation">billings</target>
      </trans-unit>
      <trans-unit id="module-products" datatype="html">
        <source>products</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">226</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">63</context>
        </context-group>
        <target state="needs-translation">products</target>
      </trans-unit>
      <trans-unit id="module-invoices" datatype="html">
        <source>invoices</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">228</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">64</context>
        </context-group>
        <target state="needs-translation">invoices</target>
      </trans-unit>
      <trans-unit id="module-plans" datatype="html">
        <source>plans</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">230</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">65</context>
        </context-group>
        <target state="needs-translation">plans</target>
      </trans-unit>
      <trans-unit id="module-settings" datatype="html">
        <source>settings</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">232</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">66</context>
        </context-group>
        <target state="needs-translation">settings</target>
      </trans-unit>
      <trans-unit id="module-rewards" datatype="html">
        <source>rewards</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">234</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">67</context>
        </context-group>
        <target state="needs-translation">rewards</target>
      </trans-unit>
      <trans-unit id="module-transactions" datatype="html">
        <source>transactions</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">236</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">68</context>
        </context-group>
        <target state="needs-translation">transactions</target>
      </trans-unit>
      <trans-unit id="module-payment-status" datatype="html">
        <source>Payment Status</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">238</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">77</context>
        </context-group>
        <target state="needs-translation">Payment Status</target>
      </trans-unit>
      <trans-unit id="module-register-buy" datatype="html">
        <source>Confirm Purchase Bill</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">240</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">78</context>
        </context-group>
        <target state="needs-translation">Confirm Purchase Bill</target>
      </trans-unit>
      <trans-unit id="module-payment-methods" datatype="html">
        <source>Buy Service</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">242</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">79</context>
        </context-group>
        <target state="needs-translation">Buy Service</target>
      </trans-unit>
      <trans-unit id="module-wallet" datatype="html">
        <source>wallet</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">244</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">69</context>
        </context-group>
        <target state="needs-translation">wallet</target>
      </trans-unit>
      <trans-unit id="module-recharge" datatype="html">
        <source>recharge</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">246</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">70</context>
        </context-group>
        <target state="needs-translation">recharge</target>
      </trans-unit>
      <trans-unit id="module-wake" datatype="html">
        <source>wake</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">248</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">71</context>
        </context-group>
        <target state="needs-translation">wake</target>
      </trans-unit>
      <trans-unit id="module-welfare" datatype="html">
        <source>Welfare</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">250</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">72</context>
        </context-group>
        <target state="needs-translation">Welfare</target>
      </trans-unit>
      <trans-unit id="module-invite-record" datatype="html">
        <source>Invitation reward record</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">252</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">73</context>
        </context-group>
        <target state="needs-translation">Invitation reward record</target>
      </trans-unit>
      <trans-unit id="module-my-voucher" datatype="html">
        <source>My Voucher</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">254</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">74</context>
        </context-group>
        <target state="needs-translation">My Voucher</target>
      </trans-unit>
      <trans-unit id="module-select-voucher" datatype="html">
        <source>Select Voucher</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">256</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">75</context>
        </context-group>
        <target state="needs-translation">Select Voucher</target>
      </trans-unit>
      <trans-unit id="module-telegram" datatype="html">
        <source>telegram</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">258</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">76</context>
        </context-group>
        <target state="needs-translation">telegram</target>
      </trans-unit>
      <trans-unit id="billing-monthly" datatype="html">
        <source>Monthly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">262</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">40</context>
        </context-group>
        <target state="translated">月度</target>
      </trans-unit>
      <trans-unit id="billing-quarterly" datatype="html">
        <source>Quarterly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">264</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">41</context>
        </context-group>
        <target state="translated">季度</target>
      </trans-unit>
      <trans-unit id="billing-semiannually" datatype="html">
        <source>Semi-Annually</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">266</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">42</context>
        </context-group>
        <target state="translated">半年度</target>
      </trans-unit>
      <trans-unit id="billing-annually" datatype="html">
        <source>Annually</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">268</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">43</context>
        </context-group>
        <target state="translated">年度</target>
      </trans-unit>
      <trans-unit id="billing-biennially" datatype="html">
        <source>Biennially</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">270</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">44</context>
        </context-group>
        <target state="translated">两年</target>
      </trans-unit>
      <trans-unit id="misc-or" datatype="html">
        <source>or</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">272</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">99</context>
        </context-group>
        <target state="translated">或者</target>
      </trans-unit>
      <trans-unit id="invoices-properties-status-unpaid" datatype="html">
        <source>Unpaid</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">274</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">38</context>
        </context-group>
        <target state="translated">未支付</target>
      </trans-unit>
      <trans-unit id="invoices-properties-status-paid" datatype="html">
        <source>Paid</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">276</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">37</context>
        </context-group>
        <target state="translated">已支付</target>
      </trans-unit>
      <trans-unit id="misc-on" datatype="html">
        <source>on</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">278</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">100</context>
        </context-group>
        <target state="needs-translation">on</target>
      </trans-unit>
      <trans-unit id="misc-off" datatype="html">
        <source>off</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">280</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">101</context>
        </context-group>
        <target state="needs-translation">off</target>
      </trans-unit>
      <trans-unit id="EmailDomainNotAllowed" datatype="html">
        <source>This domain is not allowed, please contact our CS</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">284</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">103</context>
        </context-group>
        <target state="needs-translation">This domain is not allowed, please contact our CS</target>
      </trans-unit>
      <trans-unit id="EmailNotVerified" datatype="html">
        <source>or</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">286</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">104</context>
        </context-group>
        <target state="needs-translation">or</target>
      </trans-unit>
      <trans-unit id="CredentialIsWrong" datatype="html">
        <source>Your email or password is wrong</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">288</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">105</context>
        </context-group>
        <target state="needs-translation">Your email or password is wrong</target>
      </trans-unit>
      <trans-unit id="EmailExisted" datatype="html">
        <source>You have signed up before, please sign in</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">290</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">106</context>
        </context-group>
        <target state="needs-translation">You have signed up before, please sign in</target>
      </trans-unit>
      <trans-unit id="WrongVerificationCode" datatype="html">
        <source>The verification code is wrong</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">292</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">111</context>
        </context-group>
        <target state="needs-translation">The verification code is wrong</target>
      </trans-unit>
      <trans-unit id="UserNotSignedUpYet" datatype="html">
        <source>You have not signed up yet</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">294</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">112</context>
        </context-group>
        <target state="needs-translation">You have not signed up yet</target>
      </trans-unit>
      <trans-unit id="ConfirmedPasswordNotMatch" datatype="html">
        <source>The second inputted password does not match the first</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">296</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">113</context>
        </context-group>
        <target state="needs-translation">The second inputted password does not match the first</target>
      </trans-unit>
      <trans-unit id="ServiceExpired" datatype="html">
        <source>Your service is expired, please reactivate it</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">298</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">107</context>
        </context-group>
        <target state="needs-translation">Your service is expired, please reactivate it</target>
      </trans-unit>
      <trans-unit id="ReactivatingService" datatype="html">
        <source>We are reactivating your service</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">300</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">110</context>
        </context-group>
        <target state="needs-translation">We are reactivating your service</target>
      </trans-unit>
      <trans-unit id="ReloadingService" datatype="html">
        <source>We are reloading your services</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">302</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">108</context>
        </context-group>
        <target state="needs-translation">We are reloading your services</target>
      </trans-unit>
      <trans-unit id="FailedToLoadInvoice" datatype="html">
        <source>Failed to load the invoice, make sure you have signed in.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">304</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">114</context>
        </context-group>
        <target state="needs-translation">Failed to load the invoice, make sure you have signed in.</target>
      </trans-unit>
      <trans-unit id="FailedToPayInvoice" datatype="html">
        <source>Payment failed, please try again</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">306</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">109</context>
        </context-group>
        <target state="needs-translation">Payment failed, please try again</target>
      </trans-unit>
      <trans-unit id="InsufficientFunds" datatype="html">
        <source>You do not have enough funds</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">308</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">116</context>
        </context-group>
        <target state="translated">您沒有足夠的積分</target>
      </trans-unit>
      <trans-unit id="WalletNotFound" datatype="html">
        <source>The wallet address cannot be found</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">310</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">115</context>
        </context-group>
        <target state="translated">錢包地址不正確</target>
      </trans-unit>
      <trans-unit id="invite" datatype="html">
        <source>Invite</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">312</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">120</context>
        </context-group>
        <target state="translated">邀請好友</target>
      </trans-unit>
      <trans-unit id="DowngradeInfo" datatype="html">
        <source>We do not support downgrade at the moment, if you really need it, please click on
          the icon in the lower right corner to contact customer service</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">315</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">135</context>
        </context-group>
        <target state="translated">我們暫不支持降級，如果您真的需要，請點擊右下角圖標聯繫客服</target>
      </trans-unit>
      <trans-unit id="NoNeedToUpgrade" datatype="html">
        <source>No need to upgrade</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">317</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">136</context>
        </context-group>
        <target state="translated">您當前處於此級別，無需處理</target>
      </trans-unit>
      <trans-unit id="SuccessfulDowngrade" datatype="html">
        <source>Successful downgrade!</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">319</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">137</context>
        </context-group>
        <target state="translated">降級成功</target>
      </trans-unit>
      <trans-unit id="SuccessfulUpgrade" datatype="html">
        <source>Successful upgrade!</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">321</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">138</context>
        </context-group>
        <target state="translated">降級成功！</target>
      </trans-unit>
      <trans-unit id="InvoiceCreated" datatype="html">
        <source>The invoice has been created successfully, and it is being redirected to the invoice
          details page...</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">324</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">139</context>
        </context-group>
        <target state="translated">訂單創建成功，正在跳轉到訂單詳情頁...</target>
      </trans-unit>
      <trans-unit id="ExistUnpaidInvoices" datatype="html">
        <source>You have unpaid bills, please update after payment!</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">326</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">140</context>
        </context-group>
        <target state="translated">訂單創建成功，正在跳轉到訂單詳情頁...</target>
      </trans-unit>
      <trans-unit id="UpdateCycleError" datatype="html">
        <source>Billing cycle update failed</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">328</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">141</context>
        </context-group>
        <target state="translated">更新賬單週期失敗</target>
      </trans-unit>
      <trans-unit id="UpdateCycleSuccess" datatype="html">
        <source>Billing cycle update successfully</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">330</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">142</context>
        </context-group>
        <target state="translated">更新賬單週期成功</target>
      </trans-unit>
      <trans-unit id="BilledMonthly" datatype="html">
        <source>Billed Monthly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">332</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">143</context>
        </context-group>
        <target state="translated">每月續費壹次</target>
      </trans-unit>
      <trans-unit id="BilledQuarterly" datatype="html">
        <source>Billed Quarterly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">334</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">144</context>
        </context-group>
        <target state="translated">每季度續費壹次</target>
      </trans-unit>
      <trans-unit id="BilledSemiAnnually" datatype="html">
        <source>Billed Semi-Annually</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">336</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">145</context>
        </context-group>
        <target state="translated">每半年續費壹次</target>
      </trans-unit>
      <trans-unit id="BilledAnnually" datatype="html">
        <source>Billed Annually</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">338</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">146</context>
        </context-group>
        <target state="translated">每年續費壹次</target>
      </trans-unit>
      <trans-unit id="BilledBiennially" datatype="html">
        <source>Billed Biennially</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">340</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">147</context>
        </context-group>
        <target state="translated">每两年續費壹次</target>
      </trans-unit>
      <trans-unit id="Processing" datatype="html">
        <source>Processing, please wait...</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">342</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">148</context>
        </context-group>
        <target state="translated">正在處理，請稍等...</target>
      </trans-unit>
      <trans-unit id="NoServiceTip" datatype="html">
        <source>You don't have the service yet, you can change the plan after purchasing</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">344</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">149</context>
        </context-group>
        <target state="translated">你還沒有服務，請先購買一個吧～</target>
      </trans-unit>
      <trans-unit id="TurnOnVpn" datatype="html">
        <source>VPN is enabled</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">346</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">150</context>
        </context-group>
        <target state="translated">VPN 已啟用</target>
      </trans-unit>
      <trans-unit id="TurnOffVpn" datatype="html">
        <source>VPN is off</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">348</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">151</context>
        </context-group>
        <target state="translated">VPN 已關閉</target>
      </trans-unit>
      <trans-unit id="SwitchTo" datatype="html">
        <source>Switch to</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">350</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">152</context>
        </context-group>
        <target state="translated">切換到 </target>
      </trans-unit>
      <trans-unit id="TokenExpired" datatype="html">
        <source>Session expired, please login again</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">352</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">153</context>
        </context-group>
        <target state="translated">會話失效，請重新登入</target>
      </trans-unit>
      <trans-unit id="InvoiceExpired" datatype="html">
        <source>Your invoice has expired and has been regenerated for you, please pay as soon as
          possible</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">354</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">154</context>
        </context-group>
        <target state="needs-translation">Your invoice has expired and has been regenerated for you,
          please pay as soon as possible</target>
      </trans-unit>
      <trans-unit id="EmailIllegalCharacters" datatype="html">
        <source>Email name contains illegal characters</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">356</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">157</context>
        </context-group>
        <target state="needs-translation">Email name contains illegal characters</target>
      </trans-unit>
      <trans-unit id="PasswordIllegalCharacters" datatype="html">
        <source>Password contains illegal characters</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">358</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">158</context>
        </context-group>
        <target state="needs-translation">Password contains illegal characters</target>
      </trans-unit>
      <trans-unit id="EmailFormatError" datatype="html">
        <source>Email format error</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">360</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">159</context>
        </context-group>
        <target state="needs-translation">Email format error</target>
      </trans-unit>
      <trans-unit id="EmailLongError" datatype="html">
        <source>Email address exceeds 40 characters</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">362</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">160</context>
        </context-group>
        <target state="needs-translation">Email address exceeds 40 characters</target>
      </trans-unit>
      <trans-unit id="PasswordLongError" datatype="html">
        <source>Password length exceeds 20 characters</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">364</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">161</context>
        </context-group>
        <target state="needs-translation">Password length exceeds 20 characters</target>
      </trans-unit>
      <trans-unit id="PasswordShortError" datatype="html">
        <source>Password length is less than 8 characters</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">366</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">162</context>
        </context-group>
        <target state="needs-translation">Password length is less than 8 characters</target>
      </trans-unit>
      <trans-unit id="EmailcheckError" datatype="html">
        <source>Email verification failed, please try again</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.service.ts</context>
          <context context-type="linenumber">368</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">163</context>
        </context-group>
        <target state="needs-translation">Email verification failed, please try again</target>
      </trans-unit>
      <trans-unit id="9e8a4f13bc2200d7e106fc9b8b6d0fb1950f3ba0" datatype="html">
        <source>Renew Here</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/billing-index/billing-index.component.html</context>
          <context context-type="linenumber">147</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/plans/plans.component.html</context>
          <context context-type="linenumber">123</context>
        </context-group>
        <target state="translated">去續費</target>
      </trans-unit>
      <trans-unit id="47bbc861efa59ba4135e6aa8f63213420e3f3b91" datatype="html">
        <source>Subscription</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/billing-index/billing-index.component.html</context>
          <context context-type="linenumber">152</context>
        </context-group>
        <target state="translated">訂閱</target>
      </trans-unit>
      <trans-unit id="f147d0f7f965cccee2e77294cba8e1b88021fa08" datatype="html">
        <source>Upgrade</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/billing-index/billing-index.component.html</context>
          <context context-type="linenumber">154</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/plans/plans.component.html</context>
          <context context-type="linenumber">140</context>
        </context-group>
        <target state="translated">升級</target>
      </trans-unit>
      <trans-unit id="3ac27e20756ddb5b3ed0f1d59a264e7cd625a606" datatype="html">
        <source>Billing Interval</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/billing-index/billing-index.component.html</context>
          <context context-type="linenumber">157</context>
        </context-group>
        <target state="translated">賬單週期</target>
      </trans-unit>
      <trans-unit id="047f50bc5b5d17b5bec0196355953e1a5c590ddb" datatype="html">
        <source>Update</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/billing-index/billing-index.component.html</context>
          <context context-type="linenumber">159</context>
        </context-group>
        <target state="translated">更新</target>
      </trans-unit>
      <trans-unit id="cd6bccbcb7b198b4023ae867f6d5a633912ec791" datatype="html">
        <source> Invoices </source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/billing-index/billing-index.component.html</context>
          <context context-type="linenumber">175,177</context>
        </context-group>
        <target state="needs-translation"> Invoices </target>
      </trans-unit>
      <trans-unit id="6829218544e108e152f5fa72cb79c4ccb82e0d06" datatype="html">
        <source>View</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/billing-index/billing-index.component.html</context>
          <context context-type="linenumber">183</context>
        </context-group>
        <target state="translated">查看</target>
      </trans-unit>
      <trans-unit id="invoices-properties-status" datatype="html">
        <source>Status</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/invoices/invoices.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">30</context>
        </context-group>
        <target state="translated">狀態</target>
      </trans-unit>
      <trans-unit id="invoices-properties-user" datatype="html">
        <source>Invoiced to</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/invoices/invoices.component.html</context>
          <context context-type="linenumber">13</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">31</context>
        </context-group>
        <target state="translated">買方</target>
      </trans-unit>
      <trans-unit id="invoices-properties-description" datatype="html">
        <source>Description</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/invoices/invoices.component.html</context>
          <context context-type="linenumber">17</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">32</context>
        </context-group>
        <target state="translated">描述</target>
      </trans-unit>
      <trans-unit id="invoices-properties-total" datatype="html">
        <source>Total</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/invoices/invoices.component.html</context>
          <context context-type="linenumber">21</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">33</context>
        </context-group>
        <target state="translated">總價</target>
      </trans-unit>
      <trans-unit id="fb054fa53b32638b7d948a948cff7697c681eba6" datatype="html">
        <source>HKD</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/invoices/invoices.component.html</context>
          <context context-type="linenumber">22</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">49</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">51</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">54</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">57</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/plans/plans.component.html</context>
          <context context-type="linenumber">133</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">293</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">297</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">300</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">132</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">136</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">138</context>
        </context-group>
        <target state="translated">港幣</target>
      </trans-unit>
      <trans-unit id="invoices-properties-due" datatype="html">
        <source>Due</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/invoices/invoices.component.html</context>
          <context context-type="linenumber">25</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">34</context>
        </context-group>
        <target state="translated">過期日</target>
      </trans-unit>
      <trans-unit id="invoices-properties-payed-date" datatype="html">
        <source>Paid in</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/invoices/invoices.component.html</context>
          <context context-type="linenumber">29</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">35</context>
        </context-group>
        <target state="translated">付款於</target>
      </trans-unit>
      <trans-unit id="82ac8ec728fb6d36f85eb9f59d343bc6ea01ab38" datatype="html">
        <source>Payment</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/invoices/invoices.component.html</context>
          <context context-type="linenumber">36</context>
        </context-group>
        <target state="translated">付款</target>
      </trans-unit>
      <trans-unit id="49dfd94a29f48a59933b79c56231ea9a1cf198dc" datatype="html">
        <source>Thank you for your payment.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/payment-success/payment-success.component.html</context>
          <context context-type="linenumber">5</context>
        </context-group>
        <target state="translated">谢谢您的付款</target>
      </trans-unit>
      <trans-unit id="2c662315923a127c3cc5febb940390e375d7e868" datatype="html">
        <source>Go to services</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/payment-success/payment-success.component.html</context>
          <context context-type="linenumber">6</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/versions/redirect/redirect.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
        <target state="translated">前往服務頁面</target>
      </trans-unit>
      <trans-unit id="25cfc68d8aa0cf99985d976bc5cfbd6e903432ac" datatype="html">
        <source>Billed <x id="INTERPOLATION"
            equiv-text="{{appService.translate(service?.billingcycle)}}" /></source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">56</context>
        </context-group>
        <target state="translated">按<x id="INTERPOLATION"
            equiv-text="{{appService.translate(service?.billingcycle)}}" />續費</target>
      </trans-unit>
      <trans-unit id="52b56d5859b56a3a1bfe56ce67dfd5e93b21e960" datatype="html">
        <source>Saved</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">57</context>
        </context-group>
        <target state="translated">節省</target>
      </trans-unit>
      <trans-unit id="cb82805a3c47b347d40a20af242880a266b8c0c2" datatype="html">
        <source>Have a Promo Code?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">61</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">143</context>
        </context-group>
        <target state="translated">您有優惠碼？</target>
      </trans-unit>
      <trans-unit id="4e0a5accd873662ba1df02ae022f90134d166824" datatype="html">
        <source>This promotion code is wrong, please contact CS.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">63</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">145</context>
        </context-group>
        <target state="translated">這個優惠碼不正確，親聯系客服。</target>
      </trans-unit>
      <trans-unit id="ac4d2e190f0cd2aa60d91be274710c3228a96e55" datatype="html">
        <source>By continuing the payment , you agree to our</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">65</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">308</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">148</context>
        </context-group>
        <target state="translated">繼續付款意味著您已經同意</target>
      </trans-unit>
      <trans-unit id="aa4f4b7c81ae9cabfcebc2173f31e3f4bf08d833" datatype="html">
        <source>Terms of Service</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">65</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">327</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">309</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">149</context>
        </context-group>
        <target state="translated">服務條款</target>
      </trans-unit>
      <trans-unit id="c6b3e99da22bb5c7a1e369aea06c790b82f68d94" datatype="html">
        <source> and </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">65</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">327</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">309</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">149</context>
        </context-group>
        <target state="translated"> 和 </target>
      </trans-unit>
      <trans-unit id="b8d10cd55fae4e4ad4f87d28e18251694f159bf7" datatype="html">
        <source>Privacy Policy</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">65</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">327</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">310</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">150</context>
        </context-group>
        <target state="translated">隱私政策</target>
      </trans-unit>
      <trans-unit id="fae9208356a7ce1b7c79f6d92750c44780b68f6e" datatype="html">
        <source>Deduction By Balance</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">68</context>
        </context-group>
        <target state="needs-translation">Deduction By Balance</target>
      </trans-unit>
      <trans-unit id="006863296bf85f5d0e70cd65cf7941f42501ae92" datatype="html">
        <source>Deduction</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">70</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/deduction-dialog.html</context>
          <context context-type="linenumber">41</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/deduction-dialog.html</context>
          <context context-type="linenumber">63</context>
        </context-group>
        <target state="needs-translation">Deduction</target>
      </trans-unit>
      <trans-unit id="7812bede1dc695934c854942928f33c0f34a0a13" datatype="html">
        <source>Pay quickly by</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">72</context>
        </context-group>
        <target state="translated">快速購買</target>
      </trans-unit>
      <trans-unit id="72d1017b7becbd9f94b44708129d2d92bbb52f70" datatype="html">
        <source>AliPay</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">74</context>
        </context-group>
        <target state="translated">支付寶</target>
      </trans-unit>
      <trans-unit id="472fb1409fd6f67fc66e364c0ce2e0fa11efdd8c" datatype="html">
        <source>AliPay/Wechat</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">75</context>
        </context-group>
        <target state="needs-translation">AliPay/Wechat</target>
      </trans-unit>
      <trans-unit id="7e69426bd97a606d8ae6026762858e6e7c86a1fd" datatype="html">
        <source>Balance</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">76</context>
        </context-group>
        <target state="needs-translation">Balance</target>
      </trans-unit>
      <trans-unit id="99d240b4c5587da8810fb95b0f03b09051d52e1a" datatype="html">
        <source>Pay by card</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">80</context>
        </context-group>
        <target state="translated">使用信用卡</target>
      </trans-unit>
      <trans-unit id="53d20cc6953546db5373b1ae5a686aa655a715bb" datatype="html">
        <source>Pay Now</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">83</context>
        </context-group>
        <target state="translated">現在購買</target>
      </trans-unit>
      <trans-unit id="c64a7a30545e887b604cd83dfe5b730ee926991b" datatype="html">
        <source>Receiving notification for the first time to get 5HKD balance</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/fcm/fcm.component.html</context>
          <context context-type="linenumber">2</context>
        </context-group>
        <target state="needs-translation">Receiving notification for the first time to get 5HKD
          balance</target>
      </trans-unit>
      <trans-unit id="04097c29ec9898e6c6e1c62b6b68114005b68898" datatype="html">
        <source>To celebrate the launch of the push message function, users who enable the
          notification function for the first time can get 5HKD balance as a reward.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/fcm/fcm.component.html</context>
          <context context-type="linenumber">4</context>
        </context-group>
        <target state="needs-translation">To celebrate the launch of the push message function,
          users who enable the notification function for the first time can get 5HKD balance as a
          reward.</target>
      </trans-unit>
      <trans-unit id="db890cc68a64428b758d02a89902e0047581037c" datatype="html">
        <source>Afraid of being disturbed? Don&apos;t worry, we will only send you important
          information, you can turn it off in the settings at any time</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/fcm/fcm.component.html</context>
          <context context-type="linenumber">5</context>
        </context-group>
        <target state="needs-translation">Afraid of being disturbed? Don't worry, we will only send
          you important information, you can turn it off in the settings at any time</target>
      </trans-unit>
      <trans-unit id="9f215d548dde98dcd53416d9687d915866161fd3" datatype="html">
        <source>Enable Notification</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/fcm/fcm.component.html</context>
          <context context-type="linenumber">7</context>
        </context-group>
        <target state="needs-translation">Enable Notification</target>
      </trans-unit>
      <trans-unit id="9e218937d652dfea032e29c23b48a591ce085686" datatype="html">
        <source>log in</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/fcm/fcm.component.html</context>
          <context context-type="linenumber">8</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/telegram/telegram.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
        <target state="needs-translation">log in</target>
      </trans-unit>
      <trans-unit id="4c78c0da1bcd65bc0598c4d9474bde165de27d7a" datatype="html">
        <source>Apps</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">191</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">331</context>
        </context-group>
        <target state="translated">APPs</target>
      </trans-unit>
      <trans-unit id="f55870a43ffc113a6ab187ab4cd84a9b37f03ae9" datatype="html">
        <source>The Only VPN You Ever Need</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">218</context>
        </context-group>
        <target state="translated">您唯壹需要的加速器</target>
      </trans-unit>
      <trans-unit id="d624003df4e267ca3d7dc9b5de830d36de2b6feb" datatype="html">
        <source>FlashVPN is a simple to use, fast and stable VPN service that runs all of your
          devices. Flash gas pedal guards your network security and privacy.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">220</context>
        </context-group>
        <target state="needs-translation">FlashVPN is a simple to use, fast and stable VPN service
          that runs all of your devices. Flash gas pedal guards your network security and privacy.</target>
      </trans-unit>
      <trans-unit id="dee15caad710143ef3b6c5721800826cb38849ae" datatype="html">
        <source>Try Our Free Trial</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">221</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">264</context>
        </context-group>
        <target state="translated">獲取免費試用</target>
      </trans-unit>
      <trans-unit id="7cc69674fab3a6ddbd2536061366440dbfb06a7c" datatype="html">
        <source>Cross Platform APPs</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">231</context>
        </context-group>
        <target state="translated">跨平臺APPs</target>
      </trans-unit>
      <trans-unit id="c2b3e93ba7c9ab80beb89b662a3651c716124f51" datatype="html">
        <source>We provide iOS, Android, Windows, macOS APPs, they all feel the same.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">232</context>
        </context-group>
        <target state="translated">我們提供蘋果，安卓，微軟，macOS以及瀏覽器APPs，他們擁有壹樣的頁面。</target>
      </trans-unit>
      <trans-unit id="61f3f120dc59f910434dd583fb67bb008b79f2ea" datatype="html">
        <source>Works in Browser</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">236</context>
        </context-group>
        <target state="translated">支持瀏覽器</target>
      </trans-unit>
      <trans-unit id="d6e76ce15f3d296e13e8ab9aadbad9547997510a" datatype="html">
        <source>Our Chrome/Edge extension connect directly to our servers, which is much faster and
          stable.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">237</context>
        </context-group>
        <target state="translated">我們的瀏覽器擴展直連服務器，因此更快更穩。</target>
      </trans-unit>
      <trans-unit id="7738c94bbe6ef4dddbba37c764faaee043825010" datatype="html">
        <source>Absolutely No Logs</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">241</context>
        </context-group>
        <target state="translated">無痕瀏覽</target>
      </trans-unit>
      <trans-unit id="06841f4a6ac3793a8e28236a7ed126d8b2d36bf1" datatype="html">
        <source>Your browsing history is nothing but liability to us, so why bother to save. You are
          safe with us.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">242</context>
        </context-group>
        <target state="translated">對我們來說，您的上網記錄只是壹種負擔，所以我們不會做任何形式的存儲。</target>
      </trans-unit>
      <trans-unit id="b9515ed4d976c2b0a12a8885a515d6914f508fb2" datatype="html">
        <source>Too Many Failed VPN?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">252</context>
        </context-group>
        <target state="translated">太多加速器都不行？</target>
      </trans-unit>
      <trans-unit id="174288646fe9390a769deade0b224ebcb54ba15c" datatype="html">
        <source>With FlashVPN, you won&apos;t look again.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">253</context>
        </context-group>
        <target state="translated">Flash加速器是您的唯一助手</target>
      </trans-unit>
      <trans-unit id="fafc4eb23bbf1caa4bec638219206121fd4ea224" datatype="html">
        <source>Holistic Experience</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">259</context>
        </context-group>
        <target state="translated">壹致的體驗</target>
      </trans-unit>
      <trans-unit id="b5c90d44302b2d50dad7b27bf278c52cd2ae2841" datatype="html">
        <source>All apps shares the same simple and intuitive interfaces.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">260</context>
        </context-group>
        <target state="translated">所有app擁有壹樣簡單易用的頁面。</target>
      </trans-unit>
      <trans-unit id="c026ca7a40c74e7a6cea7125d2efea2c35eb1b54" datatype="html">
        <source>A single account can be used on all devices.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">263</context>
        </context-group>
        <target state="translated">只需要壹個賬戶就可以在所有設備上使用。</target>
      </trans-unit>
      <trans-unit id="3daf8bc63bc186f881e33ca747fef640e776c8cb" datatype="html">
        <source>Fast and Stable</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">274</context>
        </context-group>
        <target state="translated">快速而且穩定</target>
      </trans-unit>
      <trans-unit id="36ddab684943f0acbf99ca38c1db99b3448ce739" datatype="html">
        <source>All servers are monitored, idle servers will never appear.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">275</context>
        </context-group>
        <target state="translated">所有服務器都即時監控，無效服務器不會出現影響您。</target>
      </trans-unit>
      <trans-unit id="65f8c474dcb27012576611caf6c5b096fff38081" datatype="html">
        <source>Works very well within China thanks to our advanced technology.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">278</context>
        </context-group>
        <target state="translated">得益於我們的先進技術，在中國大陸也工作非常好。</target>
      </trans-unit>
      <trans-unit id="9d0aaf0938ace01b80f8136d5a44c06cc44c0b96" datatype="html">
        <source>Input your email</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">281</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">164</context>
        </context-group>
        <target state="translated">輸入郵箱</target>
      </trans-unit>
      <trans-unit id="802222cb4754d74846b18f651b1f94e21576185d" datatype="html">
        <source>Get Started</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">283</context>
        </context-group>
        <target state="translated">試試吧</target>
      </trans-unit>
      <trans-unit id="55ea25bca34587d835cff3cbbb601a6b1985f3f0" datatype="html">
        <source>We aim to provide the best networking facility tools to help customers to reach
          their desired content.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">325</context>
        </context-group>
        <target state="translated">我們旨在提供最好的網絡工具以幫助客戶獲取他們想要的資訊</target>
      </trans-unit>
      <trans-unit id="dc60677d5a906e69f38a5cf9da7f2eb03931bea0" datatype="html">
        <source>Links</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">330</context>
        </context-group>
        <target state="translated">連接</target>
      </trans-unit>
      <trans-unit id="b8c9dd63e634e627f7903b1306383771c16ba83c" datatype="html">
        <source>Help &amp; Contact</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">337</context>
        </context-group>
        <target state="translated">幫助&amp;聯系</target>
      </trans-unit>
      <trans-unit id="3877b2d0a2e49783819fbb66d9499d2e7077b398" datatype="html">
        <source>Current Plan</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/plans/plans.component.html</context>
          <context context-type="linenumber">139</context>
        </context-group>
        <target state="translated">目前方案</target>
      </trans-unit>
      <trans-unit id="302ecc4d1a8882ccc8a4ec9404d6ba57a3bdef74" datatype="html">
        <source>Downgrade</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/plans/plans.component.html</context>
          <context context-type="linenumber">141</context>
        </context-group>
        <target state="translated">降級</target>
      </trans-unit>
      <trans-unit id="9ff23880c0122b8a96542805cb15393b546f4ddf" datatype="html">
        <source>Protect your internet right now</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">260</context>
        </context-group>
        <target state="translated">立即保护您的网络</target>
      </trans-unit>
      <trans-unit id="037bf1fb53810a95da5093c79898f168d7ffadfe" datatype="html">
        <source>Reach your content in the speed of flash light</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">272</context>
        </context-group>
        <target state="translated">以闪光的速度触达您的内容</target>
      </trans-unit>
      <trans-unit id="1244d01d2d42721bf8751d01173904216fbbdaa4" datatype="html">
        <source>Secure your internet without sacrificing your identity</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">273</context>
        </context-group>
        <target state="translated">保护您的身份和个人信息</target>
      </trans-unit>
      <trans-unit id="7f9b8a6216fb31ed627c3683a8afabb5fb59d259" datatype="html">
        <source>Enjoy the same intuitive experience and supports across all the platforms</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">274</context>
        </context-group>
        <target state="translated">在所有平台享受同样的直观体验</target>
      </trans-unit>
      <trans-unit id="00af816bb3eceb679ec5152add644f381ddaecd9" datatype="html">
        <source>Billed Monthly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">280</context>
        </context-group>
        <target state="translated">每月續費壹次</target>
      </trans-unit>
      <trans-unit id="48380d99a59adeb3c26de386abc0d356b5b21720" datatype="html">
        <source>Billed Quarterly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">281</context>
        </context-group>
        <target state="translated">每季度續費壹次</target>
      </trans-unit>
      <trans-unit id="470a058eabf164a06da496c005846e589e59c5d5" datatype="html">
        <source>Billed Semi-Annually</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">282</context>
        </context-group>
        <target state="translated">每半年續費壹次</target>
      </trans-unit>
      <trans-unit id="67e97417667a66059b3a0b9ae1c4e248f2603e33" datatype="html">
        <source>Billed Annually</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">283</context>
        </context-group>
        <target state="translated">每年續費壹次</target>
      </trans-unit>
      <trans-unit id="6596867bed0c9ef63858f956d254a1c346a45040" datatype="html">
        <source>Billed Biennially</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">284</context>
        </context-group>
        <target state="translated">每两年續費壹次</target>
      </trans-unit>
      <trans-unit id="52c9a103b812f258bcddc3d90a6e3f46871d25fe" datatype="html">
        <source>Save</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">290</context>
        </context-group>
        <target state="translated">儲存</target>
      </trans-unit>
      <trans-unit id="f14641ebe74d0a343fe715afd9644e2c8e94eb23" datatype="html">
        <source>Yearly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">297</context>
        </context-group>
        <target state="translated">年 </target>
      </trans-unit>
      <trans-unit id="ba4fa9ae6b1182174c65f7adbdb1f06793fc7d86" datatype="html">
        <source>Continue to Payment</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">303</context>
        </context-group>
        <target state="translated">继续付款</target>
      </trans-unit>
      <trans-unit id="39f2c136eeeec8c2bef5a29095ff19554a591b65" datatype="html">
        <source>Flash Points</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">131</context>
        </context-group>
        <target state="translated">Flash 積分</target>
      </trans-unit>
      <trans-unit id="3708577be44b199f465deebefb3524e2d1708a42" datatype="html">
        <source>You can redeem your Flash Points for data, or send and receive among friends</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">146</context>
        </context-group>
        <target state="translated">您可以兌換您的Flash積分以獲取數據，或者在朋友之間發送和接收。</target>
      </trans-unit>
      <trans-unit id="6e52eca8629e83248c592f04b72d8ba8bc6bea8d" datatype="html">
        <source>Redeem</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">158</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/rewards/transactions/transactions.component.html</context>
          <context context-type="linenumber">43</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/redeem-dialog.html</context>
          <context context-type="linenumber">77</context>
        </context-group>
        <target state="translated">兌換</target>
      </trans-unit>
      <trans-unit id="2c5ff8fa9c9aaec93f97e37c9a0edcd797194573" datatype="html">
        <source>Send</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">169</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/send-dialog.html</context>
          <context context-type="linenumber">37</context>
        </context-group>
        <target state="translated">發送</target>
      </trans-unit>
      <trans-unit id="880e1e6270e3c0632f88a2e3a926f0574f1aeab1" datatype="html">
        <source>Receive</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">180</context>
        </context-group>
        <target state="translated">接收</target>
      </trans-unit>
      <trans-unit id="01f5cd69c0a07df5b98356d3f0041d107c8d49f0" datatype="html">
        <source>GET MORE POINTS</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">184</context>
        </context-group>
        <target state="translated">獲取更多積分</target>
      </trans-unit>
      <trans-unit id="bfb4e64d4aeef83bd95aa902367a82644a894859" datatype="html">
        <source>Invite friends and earn point</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">196</context>
        </context-group>
        <target state="translated">邀請朋友，並賺取積分</target>
      </trans-unit>
      <trans-unit id="033909da098565aeefebbaef17686e4aad6c2621" datatype="html">
        <source>Copy Link and Share</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">200</context>
        </context-group>
        <target state="translated">復制鏈接並分享</target>
      </trans-unit>
      <trans-unit id="sharing-text" datatype="html">
        <source>FlashVPN - up to 30 days free trial, sign up here: </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.ts</context>
          <context context-type="linenumber">61</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">121</context>
        </context-group>
        <target state="needs-translation">FlashVPN - up to 30 days free trial, sign up here: </target>
      </trans-unit>
      <trans-unit id="sharing-url-copied" datatype="html">
        <source>You have copied your sharing link, now invite your friends and earn flash points</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.ts</context>
          <context context-type="linenumber">65</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">122</context>
        </context-group>
        <target state="needs-translation">You have copied your sharing link, now invite your friends
          and earn flash points</target>
      </trans-unit>
      <trans-unit id="2f933b826a570836cab04f683970a2d22068458c" datatype="html">
        <source>Date</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/rewards/transactions/transactions.component.html</context>
          <context context-type="linenumber">27</context>
        </context-group>
        <target state="translated">日期</target>
      </trans-unit>
      <trans-unit id="dfc2fb58e2a04ed944a4bd80f0a2087775134068" datatype="html">
        <source>Amount</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/rewards/transactions/transactions.component.html</context>
          <context context-type="linenumber">33</context>
        </context-group>
        <target state="translated">數量</target>
      </trans-unit>
      <trans-unit id="56b6659f48e01e94d1ed4058c39eff10a90e9e1d" datatype="html">
        <source>Context</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/rewards/transactions/transactions.component.html</context>
          <context context-type="linenumber">39</context>
        </context-group>
        <target state="translated">上下文</target>
      </trans-unit>
      <trans-unit id="798f7720c371eda6d7931bc590b771a02e2881bb" datatype="html">
        <source>Transfer</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/rewards/transactions/transactions.component.html</context>
          <context context-type="linenumber">42</context>
        </context-group>
        <target state="translated">轉帳</target>
      </trans-unit>
      <trans-unit id="551c850d45e9ddb46a7090ac77f2f8f590fccf3c" datatype="html">
        <source>Welcome back</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/noservice/noservice.component.html</context>
          <context context-type="linenumber">8</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/versions/redirect/redirect.component.html</context>
          <context context-type="linenumber">2</context>
        </context-group>
        <target state="translated">歡迎回來</target>
      </trans-unit>
      <trans-unit id="9dbf98ceea36de92c62c23f14fa1b5aa59318605" datatype="html">
        <source>You have not got any services, checkout our recent deals</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/noservice/noservice.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
        <target state="translated">您還沒有服務，查看最新優惠</target>
      </trans-unit>
      <trans-unit id="58684361b36215d7fa66e03627979e4ea7ba04af" datatype="html">
        <source>Buy Products</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/noservice/noservice.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
        <target state="translated">購買產品</target>
      </trans-unit>
      <trans-unit id="92ea2d04b908efbe694f6b6ede43779cf6ac9a3f" datatype="html">
        <source>Verify your email address to get a trial </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/noservice/noservice.component.html</context>
          <context context-type="linenumber">16</context>
        </context-group>
        <target state="translated">驗證電子郵件地址可獲得試用 </target>
      </trans-unit>
      <trans-unit id="e485bc1b7d3a18ad215b3317b2cf06e3e83db398" datatype="html">
        <source>支付方式</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/services/payment-methods/payment-methods.component.html</context>
          <context context-type="linenumber">55</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/services/register-buy/register-buy.component.html</context>
          <context context-type="linenumber">74</context>
        </context-group>
        <target state="needs-translation">支付方式</target>
      </trans-unit>
      <trans-unit id="a2b2886c46a8ea0489a6f530194e027a63f0258d" datatype="html">
        <source>Please enter your account number</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/services/register-buy/register-buy.component.html</context>
          <context context-type="linenumber">34</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password/forget-password.component.html</context>
          <context context-type="linenumber">262</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signin/signin.component.html</context>
          <context context-type="linenumber">303</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signup/signup.component.html</context>
          <context context-type="linenumber">304</context>
        </context-group>
        <target state="needs-translation">Please enter your account number</target>
      </trans-unit>
      <trans-unit id="df4da340d016d0d3c0884be8ddc025a723c3fb34" datatype="html">
        <source>Enter your password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/services/register-buy/register-buy.component.html</context>
          <context context-type="linenumber">47</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signin/signin.component.html</context>
          <context context-type="linenumber">315</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signup/signup.component.html</context>
          <context context-type="linenumber">316</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">169</context>
        </context-group>
        <target state="translated">輸入您的密碼</target>
      </trans-unit>
      <trans-unit id="29881a45dafbe5aa05cd9d0441a4c0c2fb06df92" datatype="html">
        <source>Account</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/settings/setting-base/setting-base.component.html</context>
          <context context-type="linenumber">14</context>
        </context-group>
        <target state="translated">帳戶</target>
      </trans-unit>
      <trans-unit id="5522ea711b2d9e14a90ea13b3732891a98895de7" datatype="html">
        <source>Manage your account settings</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/settings/setting-base/setting-base.component.html</context>
          <context context-type="linenumber">15</context>
        </context-group>
        <target state="translated">管理您的帳戶設置</target>
      </trans-unit>
      <trans-unit id="34557152cff7bc7db72a2a8706794d7fba4bac86" datatype="html">
        <source>Reset Password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/settings/setting-base/setting-base.component.html</context>
          <context context-type="linenumber">17</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password/forget-password.component.html</context>
          <context context-type="linenumber">251</context>
        </context-group>
        <target state="translated">重置密碼</target>
      </trans-unit>
      <trans-unit id="3d53f64033c4b76fdc1076ba15955d913209866c" datatype="html">
        <source>General</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/settings/setting-base/setting-base.component.html</context>
          <context context-type="linenumber">21</context>
        </context-group>
        <target state="translated">一般設定</target>
      </trans-unit>
      <trans-unit id="873eb643ab328902b4b092f352f51d8e73616ab2" datatype="html">
        <source>Manage general setting of app</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/settings/setting-base/setting-base.component.html</context>
          <context context-type="linenumber">22</context>
        </context-group>
        <target state="translated">管理應用程序的常規設置</target>
      </trans-unit>
      <trans-unit id="be7b955d49865244d6b686e819a4bbcc44011dbb" datatype="html">
        <source>Check Updates</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/settings/setting-base/setting-base.component.html</context>
          <context context-type="linenumber">24</context>
        </context-group>
        <target state="translated">檢查更新</target>
      </trans-unit>
      <trans-unit id="004b222ff9ef9dd4771b777950ca1d0e4cd4348a" datatype="html">
        <source>About</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/settings/setting-base/setting-base.component.html</context>
          <context context-type="linenumber">30</context>
        </context-group>
        <target state="translated">關於</target>
      </trans-unit>
      <trans-unit id="f91ee08bd2f6d755f6456f8b59c5893b7ef5a5c5" datatype="html">
        <source>FlashVPN is published by FlashVPN Network. We aim to provide the best networking
          facility tools to help customers to reach their desired content.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/settings/setting-base/setting-base.component.html</context>
          <context context-type="linenumber">31</context>
        </context-group>
        <target state="needs-translation">FlashVPN is published by FlashVPN Network. We aim to
          provide the best networking facility tools to help customers to reach their desired
          content.</target>
      </trans-unit>
      <trans-unit id="AppIsLatestVersionHint" datatype="html">
        <source>The app is the latest version.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/settings/setting-base/setting-base.component.ts</context>
          <context context-type="linenumber">36</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">117</context>
        </context-group>
        <target state="translated">當前已是最新版本</target>
      </trans-unit>
      <trans-unit id="4b4989998d47c4806db1becb66320cc8aecc0c66" datatype="html">
        <source>Bind telegram for the first time to get 5 HKD balance</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/telegram/telegram.component.html</context>
          <context context-type="linenumber">2</context>
        </context-group>
        <target state="needs-translation">Bind telegram for the first time to get 5 HKD balance</target>
      </trans-unit>
      <trans-unit id="1161f6ae2b9542f6a4e67c372318f9d2d29934db" datatype="html">
        <source>To celebrate the launch of the TG robot, users who bind the TG robot for the first
          time will be given 5 Hong Kong dollars to the account, which can be used for renewal
          bills.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/telegram/telegram.component.html</context>
          <context context-type="linenumber">4</context>
        </context-group>
        <target state="needs-translation">To celebrate the launch of the TG robot, users who bind
          the TG robot for the first time will be given 5 Hong Kong dollars to the account, which
          can be used for renewal bills.</target>
      </trans-unit>
      <trans-unit id="635a28fa3a34ffba57599ba107a50f5c4e701f27" datatype="html">
        <source>In the tg robot, you can check traffic, renew services, and obtain subscription
          addresses.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/telegram/telegram.component.html</context>
          <context context-type="linenumber">5</context>
        </context-group>
        <target state="needs-translation">In the tg robot, you can check traffic, renew services,
          and obtain subscription addresses.</target>
      </trans-unit>
      <trans-unit id="1f7311ee6e69c1dc1a23b96078359ca0099983d9" datatype="html">
        <source>1. Get bind command</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/telegram/telegram.component.html</context>
          <context context-type="linenumber">6</context>
        </context-group>
        <target state="needs-translation">1. Get bind command</target>
      </trans-unit>
      <trans-unit id="2782a3be8dd3ab7bc0069858aa5c23f6d7a42038" datatype="html">
        <source>Get bind robot command</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/telegram/telegram.component.html</context>
          <context context-type="linenumber">8</context>
        </context-group>
        <target state="needs-translation">Get bind robot command</target>
      </trans-unit>
      <trans-unit id="7453919d01665044dec43992b9ff7d74eb63731e" datatype="html">
        <source>2. Copy bind command</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/telegram/telegram.component.html</context>
          <context context-type="linenumber">14</context>
        </context-group>
        <target state="needs-translation">2. Copy bind command</target>
      </trans-unit>
      <trans-unit id="4ef01dca77fdd67ff0d0b4015fc25682cc4790af" datatype="html">
        <source>Copy the Command</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/telegram/telegram.component.html</context>
          <context context-type="linenumber">16</context>
        </context-group>
        <target state="needs-translation">Copy the Command</target>
      </trans-unit>
      <trans-unit id="2f6566b38fef5c7ea943410cf2e405363dccde77" datatype="html">
        <source>3. Go to the robot and enter the bind command</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/telegram/telegram.component.html</context>
          <context context-type="linenumber">18</context>
        </context-group>
        <target state="needs-translation">3. Go to the robot and enter the bind command</target>
      </trans-unit>
      <trans-unit id="97fe35bccf7ceb64ff86a7f4bafa1ae28e0c8286" datatype="html">
        <source>Set New Password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password-verify/forget-password-verify.component.html</context>
          <context context-type="linenumber">245</context>
        </context-group>
        <target state="translated">設置新密碼</target>
      </trans-unit>
      <trans-unit id="aff139f6505da1e3716f104b24e229dcbf73649e" datatype="html">
        <source>We have send a confirmation code to your email, please input here to reset your
          password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password-verify/forget-password-verify.component.html</context>
          <context context-type="linenumber">246</context>
        </context-group>
        <target state="translated">我們已經發送驗證碼到您的郵箱，請填入以便設置新密碼</target>
      </trans-unit>
      <trans-unit id="55176abdf386a01aaa9458cbab0bb50c5a61ef6e" datatype="html">
        <source>Please enter verification code</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password-verify/forget-password-verify.component.html</context>
          <context context-type="linenumber">256</context>
        </context-group>
        <target state="needs-translation">Please enter verification code</target>
      </trans-unit>
      <trans-unit id="94144ce0b823dfe775b2efc3d3087ac6634bb7d9" datatype="html">
        <source>Please enter your password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password-verify/forget-password-verify.component.html</context>
          <context context-type="linenumber">268</context>
        </context-group>
        <target state="needs-translation">Please enter your password</target>
      </trans-unit>
      <trans-unit id="b5d4afab3f35ccc8c5f1afc0cb5d832368206141" datatype="html">
        <source>Enter your new password again</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password-verify/forget-password-verify.component.html</context>
          <context context-type="linenumber">283</context>
        </context-group>
        <target state="needs-translation">Enter your new password again</target>
      </trans-unit>
      <trans-unit id="c741e919cae3e4e5d7a6e43da8926bb99b1fd780" datatype="html">
        <source> Reset </source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password-verify/forget-password-verify.component.html</context>
          <context context-type="linenumber">296,298</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password/forget-password.component.html</context>
          <context context-type="linenumber">280,282</context>
        </context-group>
        <target state="needs-translation"> Reset </target>
      </trans-unit>
      <trans-unit id="baeee7b79c18361749b689ce5d3fd097d18dce72" datatype="html">
        <source>Use your email address</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password/forget-password.component.html</context>
          <context context-type="linenumber">252</context>
        </context-group>
        <target state="translated">使用您的電子郵箱</target>
      </trans-unit>
      <trans-unit id="801c464dfaabb8eb2c16f2cc0bf81435030174dd" datatype="html">
        <source>login</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signin/signin.component.html</context>
          <context context-type="linenumber">294</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signup/signup.component.html</context>
          <context context-type="linenumber">295</context>
        </context-group>
        <target state="needs-translation">login</target>
      </trans-unit>
      <trans-unit id="b4622879a14c1964b5525f3f454c5b899b38bc05" datatype="html">
        <source>Forgot password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signin/signin.component.html</context>
          <context context-type="linenumber">321</context>
        </context-group>
        <target state="needs-translation">Forgot password</target>
      </trans-unit>
      <trans-unit id="570fb2829f49e3cd98ab0518be4196d4dbc86f2d" datatype="html">
        <source> Sign In </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signin/signin.component.html</context>
          <context context-type="linenumber">328,330</context>
        </context-group>
        <target state="needs-translation"> Sign In </target>
      </trans-unit>
      <trans-unit id="4224313404c6b9f315d35a0b4fc40667e137aefa" datatype="html">
        <source>Sign Up Completed</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-success/signup-success.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
        <target state="translated">註冊成功</target>
      </trans-unit>
      <trans-unit id="c8571aa36512cb5cd87deeac5d9d7b01d6c5ffab" datatype="html">
        <source>Download our highly integrated apps and you are good to go</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-success/signup-success.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
        <target state="translated">下載我們高度集成的APP，即刻使用</target>
      </trans-unit>
      <trans-unit id="b5eefbf561d2c28417cadff9fee1085470bd9c8c" datatype="html">
        <source>Please sign in to services page to review your data usage</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-success/signup-success.component.html</context>
          <context context-type="linenumber">16</context>
        </context-group>
        <target state="translated">請登錄到服務頁面查看您的數據使用情況</target>
      </trans-unit>
      <trans-unit id="e0996125edbdf7282e0029d7c1b0a6188fbfee7c" datatype="html">
        <source>We have send a confirmation code to <x id="INTERPOLATION" equiv-text="{{email}}" /></source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-verify/signup-verify.component.html</context>
          <context context-type="linenumber">132</context>
        </context-group>
        <target state="needs-translation">We have send a confirmation code to <x id="INTERPOLATION"
            equiv-text="{{email}}" /></target>
      </trans-unit>
      <trans-unit id="a27522c9f4c9f3e842a4527b4f3fb4a04f31f850" datatype="html">
        <source>Please input here to verify your account</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-verify/signup-verify.component.html</context>
          <context context-type="linenumber">133</context>
        </context-group>
        <target state="needs-translation">Please input here to verify your account</target>
      </trans-unit>
      <trans-unit id="ebc8a275a3e9d136b40d92fd4812e79b62c4ddfd" datatype="html">
        <source>Please enter your confirmation code</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-verify/signup-verify.component.html</context>
          <context context-type="linenumber">139</context>
        </context-group>
        <target state="needs-translation">Please enter your confirmation code</target>
      </trans-unit>
      <trans-unit id="856908d4bcb565dc1980f70c2cebbb4120baf699" datatype="html">
        <source>Verify Account</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-verify/signup-verify.component.html</context>
          <context context-type="linenumber">141</context>
        </context-group>
        <target state="needs-translation">Verify Account</target>
      </trans-unit>
      <trans-unit id="b78fa965b0ecd6eee1c41dee436f8774db8ec986" datatype="html">
        <source>You have not received the confirmation email or</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-verify/signup-verify.component.html</context>
          <context context-type="linenumber">146</context>
        </context-group>
        <target state="needs-translation">You have not received the confirmation email or</target>
      </trans-unit>
      <trans-unit id="420ab98fe25256555778058df7a2109a3c3b57ec" datatype="html">
        <source> Please wait </source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-verify/signup-verify.component.html</context>
          <context context-type="linenumber">148</context>
        </context-group>
        <target state="needs-translation"> Please wait </target>
      </trans-unit>
      <trans-unit id="0324aedb291e987da9a09b10d9442143c047843e" datatype="html">
        <source> seconds and try again</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-verify/signup-verify.component.html</context>
          <context context-type="linenumber">150</context>
        </context-group>
        <target state="needs-translation"> seconds and try again</target>
      </trans-unit>
      <trans-unit id="91f2b283378dbaaede20435048907fe33b9b9735" datatype="html">
        <source>Resent Code</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-verify/signup-verify.component.html</context>
          <context context-type="linenumber">152</context>
        </context-group>
        <target state="translated">重發驗證碼</target>
      </trans-unit>
      <trans-unit id="5a59378ecdf23e2d03204d535a57f2844a242dc1" datatype="html">
        <source>Get 3-day trial by signing up with</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signup/signup.component.html</context>
          <context context-type="linenumber">321</context>
        </context-group>
        <target state="needs-translation">Get 3-day trial by signing up with</target>
      </trans-unit>
      <trans-unit id="70fcd31e3ddcc6c560a86d74719343b838a3c7e8" datatype="html">
        <source> Create Account </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signup/signup.component.html</context>
          <context context-type="linenumber">330,332</context>
        </context-group>
        <target state="needs-translation"> Create Account </target>
      </trans-unit>
      <trans-unit id="7f61e3335cf190ad3115185086c707e67eb8ecf3" datatype="html">
        <source>Your balance is :</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/deduction-dialog.html</context>
          <context context-type="linenumber">51</context>
        </context-group>
        <target state="needs-translation">Your balance is :</target>
      </trans-unit>
      <trans-unit id="c12517643154b936cbe5ab7e5e09669d98bc7b51" datatype="html">
        <source>Please enter the balance you want to deduct, please note that whether you pay the
          order or not, the balance will be deducted after deduction and will not be refunded.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/deduction-dialog.html</context>
          <context context-type="linenumber">55</context>
        </context-group>
        <target state="needs-translation">Please enter the balance you want to deduct, please note
          that whether you pay the order or not, the balance will be deducted after deduction and
          will not be refunded.</target>
      </trans-unit>
      <trans-unit id="58edd5006f4cbb3c107d381b904cd2d6e2381695" datatype="html">
        <source>If you want to pay entirely with your balance, please use the balance payment
          method.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/deduction-dialog.html</context>
          <context context-type="linenumber">56</context>
        </context-group>
        <target state="needs-translation">If you want to pay entirely with your balance, please use
          the balance payment method.</target>
      </trans-unit>
      <trans-unit id="9f2a20f32c33c923e0e3554a674903692f39dae4" datatype="html">
        <source>Deductible amount</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/deduction-dialog.html</context>
          <context context-type="linenumber">59</context>
        </context-group>
        <target state="needs-translation">Deductible amount</target>
      </trans-unit>
      <trans-unit id="redeem-finished" datatype="html">
        <source>You successfully redeemed data, go to service and check</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/dialogs.service.ts</context>
          <context context-type="linenumber">200</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">124</context>
        </context-group>
        <target state="needs-translation">You successfully redeemed data, go to service and check</target>
      </trans-unit>
      <trans-unit id="sent-funds-successfully" datatype="html">
        <source>You successfully sent flash points to your friend, go to transaction to check</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/dialogs.service.ts</context>
          <context context-type="linenumber">233</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">125</context>
        </context-group>
        <target state="needs-translation">You successfully sent flash points to your friend, go to
          transaction to check</target>
      </trans-unit>
      <trans-unit id="walletAddressCopied" datatype="html">
        <source>Wallet address is copied, share it with your friend and receive points</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/dialogs.service.ts</context>
          <context context-type="linenumber">277</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">126</context>
        </context-group>
        <target state="translated">錢包地址已復制，與您的朋友分享並接收積分</target>
      </trans-unit>
      <trans-unit id="DeductionAmountIsEmpty" datatype="html">
        <source>Deduction amount is Empty.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/dialogs.service.ts</context>
          <context context-type="linenumber">544</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">129</context>
        </context-group>
        <target state="needs-translation">Deduction amount is Empty.</target>
      </trans-unit>
      <trans-unit id="DeductionAmountCannotBeNegative" datatype="html">
        <source>Deduction amount can not be negative.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/dialogs.service.ts</context>
          <context context-type="linenumber">548</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">132</context>
        </context-group>
        <target state="needs-translation">Deduction amount can not be negative.</target>
      </trans-unit>
      <trans-unit id="DeductionAmountExceedsBalance" datatype="html">
        <source>Deduction amount exceeds Balance.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/dialogs.service.ts</context>
          <context context-type="linenumber">552</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">130</context>
        </context-group>
        <target state="needs-translation">Deduction amount exceeds Balance.</target>
      </trans-unit>
      <trans-unit id="DeductionAmountExceedsTotal" datatype="html">
        <source>Deduction amount exceeds Total.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/dialogs.service.ts</context>
          <context context-type="linenumber">556</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">131</context>
        </context-group>
        <target state="needs-translation">Deduction amount exceeds Total.</target>
      </trans-unit>
      <trans-unit id="c281c9511379a759506f70bea74d08cb9236ab78" datatype="html">
        <source>Payments</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">119</context>
        </context-group>
        <target state="translated">付款</target>
      </trans-unit>
      <trans-unit id="e910af10105bdfcbde21ceeed73b38a92a759838" datatype="html">
        <source>Fill out your email to create an account</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">159</context>
        </context-group>
        <target state="translated">填入郵箱，以創建賬戶</target>
      </trans-unit>
      <trans-unit id="487bee63f2fb232c3ec218a0afe9f4d80790bd05" datatype="html">
        <source>Pick your preferred payment method</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">185</context>
        </context-group>
        <target state="translated">選擇妳偏向的付款方式</target>
      </trans-unit>
      <trans-unit id="a3d2bce41c906bc14990d0ea7bf058bdd70831e7" datatype="html">
        <source>Share your address to receive</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/receive-dialog.html</context>
          <context context-type="linenumber">40</context>
        </context-group>
        <target state="translated">分享您的地址以接收</target>
      </trans-unit>
      <trans-unit id="1979da7460819153e11d2078244645d94291b69c" datatype="html">
        <source>Copy</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/receive-dialog.html</context>
          <context context-type="linenumber">45</context>
        </context-group>
        <target state="translated">複製</target>
      </trans-unit>
      <trans-unit id="55dd716ab97166ea22d93befd486d55a378763b0" datatype="html">
        <source>Redeem points for data*</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/redeem-dialog.html</context>
          <context context-type="linenumber">49</context>
        </context-group>
        <target state="translated">兌換積分以獲取數據*</target>
      </trans-unit>
      <trans-unit id="7fe760f942645c0fccbff49b3e55a0b722a07d06" datatype="html">
        <source>Redeem 3 Pts for 5G data</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/redeem-dialog.html</context>
          <context context-type="linenumber">62</context>
        </context-group>
        <target state="translated">3點積分兌換5G數據</target>
      </trans-unit>
      <trans-unit id="77a191680ee66ee619dcff60c5e74c87141bdca5" datatype="html">
        <source>Redeem 5 Pts for 10G data</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/redeem-dialog.html</context>
          <context context-type="linenumber">73</context>
        </context-group>
        <target state="translated">5點積分兌換10G數據</target>
      </trans-unit>
      <trans-unit id="1764de3a471aeef29090915760f96e192b340535" datatype="html">
        <source>*We will deduct the same amount from your current used data</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/redeem-dialog.html</context>
          <context context-type="linenumber">78</context>
        </context-group>
        <target state="translated">*我們將從您當前已經使用的數據中扣除相同的數額</target>
      </trans-unit>
      <trans-unit id="7a88ca54d3f1e8fa2c534696c1e8e19a97f6ac24" datatype="html">
        <source>Send your points to friends</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/send-dialog.html</context>
          <context context-type="linenumber">22</context>
        </context-group>
        <target state="translated">將您的積分發送給朋友</target>
      </trans-unit>
      <trans-unit id="4c47b69693858b1da6eba8b99b358b0d331312a8" datatype="html">
        <source>address</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/send-dialog.html</context>
          <context context-type="linenumber">28</context>
        </context-group>
        <note priority="1" from="description">address</note>
        <target state="translated">地址</target>
      </trans-unit>
      <trans-unit id="333b476386eddcd514642db71383c93aa977c15e" datatype="html">
        <source>amount</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/send-dialog.html</context>
          <context context-type="linenumber">33</context>
        </context-group>
        <note priority="1" from="description">amount</note>
        <target state="translated">數量</target>
      </trans-unit>
      <trans-unit id="4c53fb6ecbfb005ff729178d32f76b4fccfe2b37" datatype="html">
        <source>Are you sure to update the billing cycle?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/update-confirm-dialog.html</context>
          <context context-type="linenumber">25</context>
        </context-group>
        <target state="translated">您確定要更新賬單週期嗎？</target>
      </trans-unit>
      <trans-unit id="f53f905322fc5d5ed19d18ce121ce369bee58521" datatype="html">
        <source>You will update the billing cycle to </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/update-confirm-dialog.html</context>
          <context context-type="linenumber">27</context>
        </context-group>
        <target state="translated">你確定更新訂單週期為</target>
      </trans-unit>
      <trans-unit id="8d7d399b08caa414576570014fbd45fabd3a6773" datatype="html">
        <source>When you update the billing cycle, unpaid bills will be cancelled, and the billing
          cycle will take effect immediately.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/update-confirm-dialog.html</context>
          <context context-type="linenumber">28</context>
        </context-group>
        <target state="translated">當您更新賬單週期時，未支付的賬單將被取消，新的賬單週期立即生效。</target>
      </trans-unit>
      <trans-unit id="d7b35c384aecd25a516200d6921836374613dfe7" datatype="html">
        <source>Cancel</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/update-confirm-dialog.html</context>
          <context context-type="linenumber">31</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/upgrade-confirm-dialog.html</context>
          <context context-type="linenumber">31</context>
        </context-group>
        <target state="translated">取消</target>
      </trans-unit>
      <trans-unit id="68e710782ccb5398b3acb8844caf0b199da2c3da" datatype="html">
        <source>Confirm</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/update-confirm-dialog.html</context>
          <context context-type="linenumber">32</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/upgrade-confirm-dialog.html</context>
          <context context-type="linenumber">32</context>
        </context-group>
        <target state="translated">確定</target>
      </trans-unit>
      <trans-unit id="ae6cee457adbb55a6ac59472b8a12f12bfce2ce6" datatype="html">
        <source>Ignore this version</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/update-dialog.html</context>
          <context context-type="linenumber">13</context>
        </context-group>
        <target state="translated">忽略该版本</target>
      </trans-unit>
      <trans-unit id="c90df7d1332308866c3bc185871df517efb95c1b" datatype="html">
        <source>Are you sure to upgrade/downgrade the service?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/upgrade-confirm-dialog.html</context>
          <context context-type="linenumber">25</context>
        </context-group>
        <target state="translated">你確定調整套餐嗎？</target>
      </trans-unit>
      <trans-unit id="e681d25206ab1d6b9f4d96315cc17a0801b80bfa" datatype="html">
        <source>You will change to </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/upgrade-confirm-dialog.html</context>
          <context context-type="linenumber">27</context>
        </context-group>
        <target state="translated">你將變更到</target>
      </trans-unit>
      <trans-unit id="c77116e7f886020caeb0874bd444aca1929feef1" datatype="html">
        <source>When you upgrade/downgrade your service, your unpaid bill will be cancelled, and you
          may be required to pay the upgrade bill. If payment is required, your service will be
          upgraded after you pay, otherwise your service will be completed immediately change.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/upgrade-confirm-dialog.html</context>
          <context context-type="linenumber">28</context>
        </context-group>
        <target state="translated">
          當你升級/降級你的服務時，你未支付的賬單將會被取消。可能需要你支付升級賬單，需要支付的情況下，你的服務將在你支付後完成升級，否則你的服務會立即變更。</target>
      </trans-unit>
      <trans-unit id="5b24948466e5dd22b631d0cd5eae25fa4f545e0e" datatype="html">
        <source>You can use our service with the following App</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/windows-export-dialog.html</context>
          <context context-type="linenumber">6</context>
        </context-group>
        <target state="translated">您可以利用的以下应用程序来使用我们的服务</target>
      </trans-unit>
      <trans-unit id="country-us" datatype="html">
        <source>US</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">3</context>
        </context-group>
        <target state="translated">美國</target>
      </trans-unit>
      <trans-unit id="country-russia" datatype="html">
        <source>Russia</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">4</context>
        </context-group>
        <target state="translated">俄國</target>
      </trans-unit>
      <trans-unit id="country-japan" datatype="html">
        <source>Japan</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">5</context>
        </context-group>
        <target state="translated">日本</target>
      </trans-unit>
      <trans-unit id="country-germany" datatype="html">
        <source>Germany</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">6</context>
        </context-group>
        <target state="translated">德國</target>
      </trans-unit>
      <trans-unit id="country-netherlands" datatype="html">
        <source>Netherlands</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">7</context>
        </context-group>
        <target state="translated">荷蘭</target>
      </trans-unit>
      <trans-unit id="content-netflix" datatype="html">
        <source>Netflix</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
        <target state="translated">奈飛</target>
      </trans-unit>
      <trans-unit id="node-feature-netflix" datatype="html">
        <source>Unlock Netflix.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">14</context>
        </context-group>
        <target state="translated">解鎖奈飛。</target>
      </trans-unit>
      <trans-unit id="node-feature-us-IP" datatype="html">
        <source>US IP and unlocks US contents.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">15</context>
        </context-group>
        <target state="translated">美國IP，解鎖美國內容。</target>
      </trans-unit>
      <trans-unit id="node-feature-1000M" datatype="html">
        <source>Up to 1000Mbps.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">16</context>
        </context-group>
        <target state="translated">網速可高達1000Mbps。</target>
      </trans-unit>
      <trans-unit id="node-feature-200M" datatype="html">
        <source>Up to 200Mbps.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">17</context>
        </context-group>
        <target state="translated">網速高達200Mbps。</target>
      </trans-unit>
      <trans-unit id="node-feature-100M" datatype="html">
        <source>Up to 100Mbps.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">18</context>
        </context-group>
        <target state="translated">可高達100Mbps。</target>
      </trans-unit>
      <trans-unit id="users-verify-email" datatype="html">
        <source>Verify Email</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">21</context>
        </context-group>
        <target state="translated">驗證郵箱</target>
      </trans-unit>
      <trans-unit id="users-email-code" datatype="html">
        <source>Thank you for your signing up, please copy the following verification code</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">22</context>
        </context-group>
        <target state="translated">感謝您的註冊，請拷貝下面的驗證碼</target>
      </trans-unit>
      <trans-unit id="users-support-hint" datatype="html">
        <source>Let us know how it goes by simply replying to this email or contact our custom
          service at</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">23</context>
        </context-group>
        <target state="translated">如果您有問題可以回復此郵件獲得客服支持</target>
      </trans-unit>
      <trans-unit id="users-download-app-hint" datatype="html">
        <source>Meanwhile, download our highly integrated APPS</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">24</context>
        </context-group>
        <target state="translated">同時，您可以下載我們自研APPs</target>
      </trans-unit>
      <trans-unit id="users-forget-password" datatype="html">
        <source>Forget Password? Reset Now</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">25</context>
        </context-group>
        <target state="translated">忘記密碼了？現在重置</target>
      </trans-unit>
      <trans-unit id="users-verification-code-hint" datatype="html">
        <source>Please copy the following verification code to where you initiated the request</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">26</context>
        </context-group>
        <target state="translated">請拷貝下面的驗證碼到之前網頁</target>
      </trans-unit>
      <trans-unit id="invoices-unpaid" datatype="html">
        <source>Invoice Unpaid</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">28</context>
        </context-group>
        <target state="translated">未付賬單</target>
      </trans-unit>
      <trans-unit id="invoices-unpaid-hint" datatype="html">
        <source>The following invoice will due very soon, please pay in time to avoid service
          suspension.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">29</context>
        </context-group>
        <target state="translated">下面的賬單即將過期，請及時續費以避免服務暫停</target>
      </trans-unit>
      <trans-unit id="invoices-payment-hint" datatype="html">
        <source>Pay Now</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">36</context>
        </context-group>
        <target state="translated">現在付款</target>
      </trans-unit>
      <trans-unit id="invoices-paid" datatype="html">
        <source>Invoice Paid</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">47</context>
        </context-group>
        <target state="translated">賬單已支付</target>
      </trans-unit>
      <trans-unit id="invoices-payed-hint" datatype="html">
        <source>Thank you for the payment. The following service has been extended</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">48</context>
        </context-group>
        <target state="translated">感謝您的付款，下面的服務已經續費。</target>
      </trans-unit>
      <trans-unit id="service-hint" datatype="html">
        <source>Check The Service</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">49</context>
        </context-group>
        <target state="translated">查看服務</target>
      </trans-unit>
      <trans-unit id="campaigns-lite-title" datatype="html">
        <source>FlashLite - Free VPN Forever</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">51</context>
        </context-group>
        <target state="translated">FlashLite - 永久免費的加速器</target>
      </trans-unit>
      <trans-unit id="campaigns-customer" datatype="html">
        <source>Dear Customer</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">52</context>
        </context-group>
        <target state="translated">親愛的客戶</target>
      </trans-unit>
      <trans-unit id="campaigns-introduction" datatype="html">
        <source>we are proud to introduce you</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">53</context>
        </context-group>
        <target state="translated">我們很開心的給妳介紹</target>
      </trans-unit>
      <trans-unit id="campaigns-lite-feature-free" datatype="html">
        <source>FlashLite is free, guaranteed forever</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">54</context>
        </context-group>
        <target state="translated">不需要購買，保證永久免費</target>
      </trans-unit>
      <trans-unit id="campaigns-lite-feature-sign-up" datatype="html">
        <source>No need to sign up, or whatsoever</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">55</context>
        </context-group>
        <target state="translated">不需要任何形式的註冊</target>
      </trans-unit>
      <trans-unit id="campaigns-lite-feature-privacy" datatype="html">
        <source>Fast and stable, no history recorded</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">56</context>
        </context-group>
        <target state="translated">穩定快速，無瀏覽記錄</target>
      </trans-unit>
      <trans-unit id="campaigns-take-a-look" datatype="html">
        <source>Take A Look</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">57</context>
        </context-group>
        <target state="translated">去看壹看</target>
      </trans-unit>
      <trans-unit id="extension-lang-switching-hint" datatype="html">
        <source>Please reopen the extension to change language</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">84</context>
        </context-group>
        <target state="translated">請重新打開chrome插件以更換語言。</target>
      </trans-unit>
      <trans-unit id="testing-latency-hint" datatype="html">
        <source>Testing latency of all servers</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">85</context>
        </context-group>
        <target state="translated">測試所有服務器的延遲</target>
      </trans-unit>
      <trans-unit id="current-server-not-selected" datatype="html">
        <source>Please select a server first</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">86</context>
        </context-group>
        <target state="translated">請選擇壹個服務器</target>
      </trans-unit>
      <trans-unit id="service-expired-hint" datatype="html">
        <source>Your service is expired, please renew</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">87</context>
        </context-group>
        <target state="translated">您的服務已經過期，請續費</target>
      </trans-unit>
      <trans-unit id="invoices-loading-failure-hint" datatype="html">
        <source>Failed to load the invoice, make sure you have signed in.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">88</context>
        </context-group>
        <target state="translated">加載賬單失敗，請確保您已經登陸。</target>
      </trans-unit>
      <trans-unit id="service-used-up-hint" datatype="html">
        <source>Your package is used up, you can redeem more data by flash points.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">89</context>
        </context-group>
        <target state="translated">您的套餐用量已經用完，您可以試用積分兌換更多流量。</target>
      </trans-unit>
      <trans-unit id="product-spark-description" datatype="html">
        <source>50G data plan each month, reset monthly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">93</context>
        </context-group>
        <target state="translated">50G流量，每月清零重置</target>
      </trans-unit>
      <trans-unit id="product-blaze-description" datatype="html">
        <source>100G data plan each month, reset monthly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">94</context>
        </context-group>
        <target state="translated">100G流量，每月清零重置</target>
      </trans-unit>
      <trans-unit id="product-lightning-description" datatype="html">
        <source>200G data plan each month, reset monthly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">95</context>
        </context-group>
        <target state="translated">200G流量，每月清零重置</target>
      </trans-unit>
      <trans-unit id="insufficient-funds" datatype="html">
        <source>insufficient-funds</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">123</context>
        </context-group>
        <target state="translated">餘額不足</target>
      </trans-unit>
      <trans-unit id="7799b5a568e3f0dbe933864a527ce9e53a46d092" datatype="html">
        <source>We detected that you are redirected from our own app</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/versions/redirect/redirect.component.html</context>
          <context context-type="linenumber">3</context>
        </context-group>
        <target state="translated">我們檢測到您從我們的APP跳轉到網頁</target>
      </trans-unit>
      <trans-unit id="61585a525da1d0ec9f3aa477c24ecf27473db1c4" datatype="html">
        <source>Open in <x id="INTERPOLATION" equiv-text="{{app}}" /></source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/versions/redirect/redirect.component.html</context>
          <context context-type="linenumber">6</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/versions/redirect/redirect.component.html</context>
          <context context-type="linenumber">7</context>
        </context-group>
        <target state="translated">返回<x id="INTERPOLATION" equiv-text="{{app}}" />上使用</target>
      </trans-unit>
      <trans-unit id="128b6f0a99e06be6855c190e6f8ea212fdc08af8" datatype="html">
        <source>FlashVPN set off again!</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/wake/wake.component.html</context>
          <context context-type="linenumber">2</context>
        </context-group>
        <target state="needs-translation">FlashVPN set off again!</target>
      </trans-unit>
      <trans-unit id="cf854844532630fdec220e98eeede2d542822761" datatype="html">
        <source>To get your gift information, please log in first!</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/wake/wake.component.html</context>
          <context context-type="linenumber">5</context>
        </context-group>
        <target state="needs-translation">To get your gift information, please log in first!</target>
      </trans-unit>
      <trans-unit id="a6b4ca18023501c7f33a5cd19f9b8d139b19884e" datatype="html">
        <source>Sign in to get the return package</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/wake/wake.component.html</context>
          <context context-type="linenumber">6</context>
        </context-group>
        <target state="needs-translation">Sign in to get the return package</target>
      </trans-unit>
      <trans-unit id="45f370e8d2a379d45b636bb3691db3fbccf1bd69" datatype="html">
        <source>Get the Return Package</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/wake/wake.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
        <target state="needs-translation">Get the Return Package</target>
      </trans-unit>
      <trans-unit id="578e968204a393e95b515af02639a994498e9eca" datatype="html">
        <source>We have successfully updated the service time for you, please go to the </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/wake/wake.component.html</context>
          <context context-type="linenumber">27</context>
        </context-group>
        <target state="needs-translation">We have successfully updated the service time for you,
          please go to the </target>
      </trans-unit>
      <trans-unit id="fc416c45ba904f619ece04f07f0f6689dcc3faad" datatype="html">
        <source>service page</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/wake/wake.component.html</context>
          <context context-type="linenumber">28</context>
        </context-group>
        <target state="needs-translation">service page</target>
      </trans-unit>
      <trans-unit id="f4f6020f5e63e06b78309605f251bc513aff973a" datatype="html">
        <source> to check.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/wake/wake.component.html</context>
          <context context-type="linenumber">29</context>
        </context-group>
        <target state="needs-translation"> to check.</target>
      </trans-unit>
      <trans-unit id="c705813ab9df73f5508c71769c7818e4b6387941" datatype="html">
        <source>We have successfully recharged the amount for you, you can deduct it when paying, go
          to the </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/wake/wake.component.html</context>
          <context context-type="linenumber">32</context>
        </context-group>
        <target state="needs-translation">We have successfully recharged the amount for you, you can
          deduct it when paying, go to the </target>
      </trans-unit>
      <trans-unit id="7f440e612dc3aaf40f2740f3e3e741c0d317ecd8" datatype="html">
        <source>wallet page</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/wake/wake.component.html</context>
          <context context-type="linenumber">33</context>
        </context-group>
        <target state="needs-translation">wallet page</target>
      </trans-unit>
      <trans-unit id="a9d083f71cebc1a0c9316cf4abc1d608759adcc8" datatype="html">
        <source> to check your balance.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/wake/wake.component.html</context>
          <context context-type="linenumber">34</context>
        </context-group>
        <target state="needs-translation"> to check your balance.</target>
      </trans-unit>
      <trans-unit id="e5ba208761861190930fe9651b613d764dbf927b" datatype="html">
        <source>We have released a new version of APPS, you are welcome to go to the </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/wake/wake.component.html</context>
          <context context-type="linenumber">37</context>
        </context-group>
        <target state="needs-translation">We have released a new version of APPS, you are welcome to
          go to the </target>
      </trans-unit>
      <trans-unit id="98fa38d49ba6efa5bd03741fb9b6e8cca0003937" datatype="html">
        <source>APPS page</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/wake/wake.component.html</context>
          <context context-type="linenumber">38</context>
        </context-group>
        <target state="needs-translation">APPS page</target>
      </trans-unit>
      <trans-unit id="a0d78b5ca5c7d5595d85a6a0cab18b1a5ea61c22" datatype="html">
        <source> to download and experience.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/wake/wake.component.html</context>
          <context context-type="linenumber">39</context>
        </context-group>
        <target state="needs-translation"> to download and experience.</target>
      </trans-unit>
      <trans-unit id="87e79343c5ca3cece8c6fe121261e4bd1909a978" datatype="html">
        <source>Please enter an email</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/welfare/popularity/popularity.component.html</context>
          <context context-type="linenumber">32</context>
        </context-group>
        <target state="needs-translation">Please enter an email</target>
      </trans-unit>
    </body>
  </file>
</xliff>