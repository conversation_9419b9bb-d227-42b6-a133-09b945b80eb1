<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="ng2.template" target-language="ja">
    <body>
      <trans-unit id="ac9ca0cb7cca3231493d9cc9be5bb102f9de52da" datatype="html">
        <source>FlashVPN</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">138</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/welcome/welcome.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">138</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">141</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">270</context>
        </context-group>
        <target state="needs-translation">FlashVPN</target>
      </trans-unit>
      <trans-unit id="2c6df42ae9eb4f9d59bc27179f1c7fd3f9a63a7d" datatype="html">
        <source>Verify Email</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">143</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-verify/signup-verify.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/noservice/noservice.component.html</context>
          <context context-type="linenumber">17</context>
        </context-group>
        <target state="translated">認証メール</target>
      </trans-unit>
      <trans-unit id="419d940613972cc3fae9c8ea0a4306dbf80616e5" datatype="html">
        <source>Services</source>
        <target>サービス</target>
      </trans-unit>
      <trans-unit id="myservices_menu" datatype="html">
        <source>My Services</source>
        <target>マイサービス</target>
      </trans-unit>
      <trans-unit id="7d69fb44bd1c2cf804e4ff82507069c213bdc1b5" datatype="html">
        <source>Billings</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">150</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">146</context>
        </context-group>
        <target state="translated">お会計</target>
      </trans-unit>
      <trans-unit id="ddcef56b981da3cd79e02d7e9d31c693d5dd4b39" datatype="html">
        <source>Plans</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">151</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">147</context>
        </context-group>
        <target state="needs-translation">Plans</target>
      </trans-unit>
      <trans-unit id="121cc5391cd2a5115bc2b3160379ee5b36cd7716" datatype="html">
        <source>Settings</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">153</context>
        </context-group>
        <target state="translated">設定</target>
      </trans-unit>
      <trans-unit id="ba4f24bf9bf3dc4db3d6bc1b8b63339295f0b806" datatype="html">
        <source>Sign In</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">154</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signup/signup.component.html</context>
          <context context-type="linenumber">30</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signin/signin.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signin/signin.component.html</context>
          <context context-type="linenumber">26</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-success/signup-success.component.html</context>
          <context context-type="linenumber">17</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/welcome/welcome.component.html</context>
          <context context-type="linenumber">16</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">143</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">281</context>
        </context-group>
        <target state="translated">サインイン</target>
      </trans-unit>
      <trans-unit id="28629af803ab8b5f6e995b17d486e6a1527e2a96" datatype="html">
        <source>Sign Up</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">155</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signup/signup.component.html</context>
          <context context-type="linenumber">12</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/welcome/welcome.component.html</context>
          <context context-type="linenumber">13</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">142</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">280</context>
        </context-group>
        <target state="translated">サインアップ</target>
      </trans-unit>
      <trans-unit id="f7718234fe467fe7c3ffe5a16e6c01092692be68" datatype="html">
        <source>APPS </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">156</context>
        </context-group>
        <target state="needs-translation">APPS </target>
      </trans-unit>
      <trans-unit id="640eec093c685babaa3265137215e3cc92ad704d" datatype="html">
        <source>Sign Out</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">157</context>
        </context-group>
        <target state="translated">サインアウト</target>
      </trans-unit>
      <trans-unit id="59c98061a68f4e057a4d546d9d9443148751d87f" datatype="html">
        <source>Follow us on</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">160</context>
        </context-group>
        <target state="translated">フォローする</target>
      </trans-unit>
      <trans-unit id="a81d9654130f2d39b3930e9757d75c0dd39411b9" datatype="html">
        <source>Get trial by signing up with the following</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signup/signup.component.html</context>
          <context context-type="linenumber">13</context>
        </context-group>
        <target state="needs-translation">Get trial by signing up with the following</target>
      </trans-unit>
      <trans-unit id="a078ea616db5f9c77c7e153e968b9ee10e70b330" datatype="html">
        <source>Sign up by your email address and get trial</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signup/signup.component.html</context>
          <context context-type="linenumber">16</context>
        </context-group>
        <target state="needs-translation">Sign up by your email address and get trial</target>
      </trans-unit>
      <trans-unit id="9d0aaf0938ace01b80f8136d5a44c06cc44c0b96" datatype="html">
        <source>Input your email</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signup/signup.component.html</context>
          <context context-type="linenumber">19</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signin/signin.component.html</context>
          <context context-type="linenumber">16</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">164</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">228</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password/forget-password.component.html</context>
          <context context-type="linenumber">16</context>
        </context-group>
        <target state="translated">メールアドレスを入力してください</target>
      </trans-unit>
      <trans-unit id="df4da340d016d0d3c0884be8ddc025a723c3fb34" datatype="html">
        <source>Enter your password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signup/signup.component.html</context>
          <context context-type="linenumber">23</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signin/signin.component.html</context>
          <context context-type="linenumber">20</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">169</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password-verify/forget-password-verify.component.html</context>
          <context context-type="linenumber">16</context>
        </context-group>
        <target state="translated">パスワードを入れてください</target>
      </trans-unit>
      <trans-unit id="e19fcf996343543e13789f17b550ab0c08124b5c" datatype="html">
        <source>Create Account</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signup/signup.component.html</context>
          <context context-type="linenumber">29</context>
        </context-group>
        <target state="translated">アカウントを作成</target>
      </trans-unit>
      <trans-unit id="b2873994357094fd873f07553dbf16bc4fb80509" datatype="html">
        <source>Sign in to FlashVPN with</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signin/signin.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
        <target state="needs-translation">Sign in to FlashVPN with</target>
      </trans-unit>
      <trans-unit id="baeee7b79c18361749b689ce5d3fd097d18dce72" datatype="html">
        <source>Use your email address</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signin/signin.component.html</context>
          <context context-type="linenumber">13</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password/forget-password.component.html</context>
          <context context-type="linenumber">13</context>
        </context-group>
        <target state="translated">メールアドレスを利用する</target>
      </trans-unit>
      <trans-unit id="a925f43e295771551546eb2635942954fdc3c467" datatype="html">
        <source>Forget Password?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/users/signin/signin.component.html</context>
          <context context-type="linenumber">27</context>
        </context-group>
        <target state="translated">パスワードを忘れた？</target>
      </trans-unit>
      <trans-unit id="e0996125edbdf7282e0029d7c1b0a6188fbfee7c" datatype="html">
        <source>We have send a confirmation code to <x id="INTERPOLATION" equiv-text="{{email}}" /></source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-verify/signup-verify.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
        <target state="translated"><x id="INTERPOLATION" equiv-text="{{email}}" />に確認コードを送信しました</target>
      </trans-unit>
      <trans-unit id="a27522c9f4c9f3e842a4527b4f3fb4a04f31f850" datatype="html">
        <source>Please input here to verify your account</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-verify/signup-verify.component.html</context>
          <context context-type="linenumber">11</context>
        </context-group>
        <target state="translated">ここにコードを入れて、アカウントを確認してください</target>
      </trans-unit>
      <trans-unit id="98a9c6594830cd07b41f594261474f629243fb00" datatype="html">
        <source>Input verification code</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-verify/signup-verify.component.html</context>
          <context context-type="linenumber">14</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password-verify/forget-password-verify.component.html</context>
          <context context-type="linenumber">13</context>
        </context-group>
        <target state="translated">認証コードを入れてください</target>
      </trans-unit>
      <trans-unit id="27c7bfba7033cc8dbf4caf42b40b6b3c87197838" datatype="html">
        <source>Verify</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-verify/signup-verify.component.html</context>
          <context context-type="linenumber">16</context>
        </context-group>
        <target state="translated">認証</target>
      </trans-unit>
      <trans-unit id="6cb963ff5b108a00bf106b4c48dc7af5cdace410" datatype="html">
        <source>You have not received the confirmation email</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-verify/signup-verify.component.html</context>
          <context context-type="linenumber">18</context>
        </context-group>
        <target state="translated">認証メールが届いてません</target>
      </trans-unit>
      <trans-unit id="91f2b283378dbaaede20435048907fe33b9b9735" datatype="html">
        <source>Resent Code</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-verify/signup-verify.component.html</context>
          <context context-type="linenumber">19</context>
        </context-group>
        <target state="translated">認証コードを送り直す</target>
      </trans-unit>
      <trans-unit id="4224313404c6b9f315d35a0b4fc40667e137aefa" datatype="html">
        <source>Sign Up Completed</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-success/signup-success.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
        <target state="translated">新規登録完了</target>
      </trans-unit>
      <trans-unit id="c8571aa36512cb5cd87deeac5d9d7b01d6c5ffab" datatype="html">
        <source>Download our highly integrated apps and you are good to go</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-success/signup-success.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
        <target state="translated">ダウンロードして使い始めます</target>
      </trans-unit>
      <trans-unit id="b5eefbf561d2c28417cadff9fee1085470bd9c8c" datatype="html">
        <source>Please sign in to services page to review your data usage</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/signup-success/signup-success.component.html</context>
          <context context-type="linenumber">16</context>
        </context-group>
        <target state="translated">データの使用状況を確認するには、サービスページにサインインしてください</target>
      </trans-unit>
      <trans-unit id="0198bcb626a52c4dabdfc7909f6cefcc90ad832b" datatype="html">
        <source>Your service has expired, please delete!</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/servers/servers.component.html</context>
          <context context-type="linenumber">64</context>
        </context-group>
        <target state="needs-translation">Your service has expired, please delete!</target>
      </trans-unit>
      <trans-unit id="826b25211922a1b46436589233cb6f1a163d89b7" datatype="html">
        <source>Delete</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/servers/servers.component.html</context>
          <context context-type="linenumber">66</context>
        </context-group>
        <target state="needs-translation">Delete</target>
      </trans-unit>
      <trans-unit id="a3b3494079498bf437d200c91033b57e6cce30ae" datatype="html">
        <source>Your service has expired, in order to avoid affecting your use, please update in
          time!</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/servers/servers.component.html</context>
          <context context-type="linenumber">70</context>
        </context-group>
        <target state="needs-translation">Your service has expired, in order to avoid affecting your
          use, please update in time!</target>
      </trans-unit>
      <trans-unit id="10219c75410df52f6004f590cb3fbd21f15e8f2d" datatype="html">
        <source>Manage bills</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/servers/servers.component.html</context>
          <context context-type="linenumber">72</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/dashboard/dashboard.component.html</context>
          <context context-type="linenumber">45</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/dashboard/dashboard.component.html</context>
          <context context-type="linenumber">49</context>
        </context-group>
        <target state="needs-translation">Manage bills</target>
      </trans-unit>
      <trans-unit id="aa4da924506fa8a7942007a529cc2ec8d500a093" datatype="html">
        <source>Servers</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/servers/servers.component.html</context>
          <context context-type="linenumber">77</context>
        </context-group>
        <target state="translated">サーバー</target>
      </trans-unit>
      <trans-unit id="c02fddf9f130c38a2b59d4772f93bdddea16e9c2" datatype="html">
        <source>Current Server</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/servers/servers.component.html</context>
          <context context-type="linenumber">135</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/servers/servers.component.html</context>
          <context context-type="linenumber">158</context>
        </context-group>
        <target state="translated">ご利用中のサーバー</target>
      </trans-unit>
      <trans-unit id="37e10df2d9c0c25ef04ac112c9c9a7723e8efae0" datatype="html">
        <source>Mode</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/servers/servers.component.html</context>
          <context context-type="linenumber">139</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/servers/servers.component.html</context>
          <context context-type="linenumber">163</context>
        </context-group>
        <target state="translated">モード</target>
      </trans-unit>
      <trans-unit id="ddae8ce5cbb3dbb7f036804f449768d68fc02221" datatype="html">
        <source>China</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/servers/servers.component.html</context>
          <context context-type="linenumber">141</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/servers/servers.component.html</context>
          <context context-type="linenumber">165</context>
        </context-group>
        <target state="translated">中国</target>
      </trans-unit>
      <trans-unit id="5decb3917d46a9ac6e5813699801becb7c3c1455" datatype="html">
        <source>Global</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/servers/servers.component.html</context>
          <context context-type="linenumber">142</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/servers/servers.component.html</context>
          <context context-type="linenumber">166</context>
        </context-group>
        <target state="translated">グローバル</target>
      </trans-unit>
      <trans-unit id="0aa90642a6d18ff823134d9f357e3df8e5460fdb" datatype="html">
        <source>Traffic</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/servers/servers.component.html</context>
          <context context-type="linenumber">170</context>
        </context-group>
        <target state="translated">容量</target>
      </trans-unit>
      <trans-unit id="00bbeecd57b22dba6d5b1634dc87ac40712c2c68" datatype="html">
        <source>HTTP Port</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/servers/servers.component.html</context>
          <context context-type="linenumber">177</context>
        </context-group>
        <target state="translated">HTTP ポート</target>
      </trans-unit>
      <trans-unit id="a051426411d2055c18554e87f35a6bcbc99f6745" datatype="html">
        <source>Socks5 Port</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/servers/servers.component.html</context>
          <context context-type="linenumber">183</context>
        </context-group>
        <target state="translated">
          Socks5 ポート</target>
      </trans-unit>
      <trans-unit id="fb054fa53b32638b7d948a948cff7697c681eba6" datatype="html">
        <source>HKD</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">49</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">51</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">54</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">57</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">132</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">136</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">138</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">287</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">291</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">294</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/plans/plans.component.html</context>
          <context context-type="linenumber">133</context>
        </context-group>
        <target state="translated">香港ドル</target>
      </trans-unit>
      <trans-unit id="25cfc68d8aa0cf99985d976bc5cfbd6e903432ac" datatype="html">
        <source>Billed <x id="INTERPOLATION"
            equiv-text="{{appService.translate(service?.billingcycle)}}" /></source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">56</context>
        </context-group>
        <target state="translated"><x id="INTERPOLATION"
            equiv-text="{{appService.translate(service?.billingcycle)}}" /> を請求しました</target>
      </trans-unit>
      <trans-unit id="52b56d5859b56a3a1bfe56ce67dfd5e93b21e960" datatype="html">
        <source>Saved</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">57</context>
        </context-group>
        <target state="translated">円を節約できた</target>
      </trans-unit>
      <trans-unit id="cb82805a3c47b347d40a20af242880a266b8c0c2" datatype="html">
        <source>Have a Promo Code?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">61</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">143</context>
        </context-group>
        <target state="translated">クーポンコードお持ちですか</target>
      </trans-unit>
      <trans-unit id="4e0a5accd873662ba1df02ae022f90134d166824" datatype="html">
        <source>This promotion code is wrong, please contact CS.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">63</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">145</context>
        </context-group>
        <target state="translated">クーポンコードが間違いました。
          カスタマーサポートまでご連絡ください</target>
      </trans-unit>
      <trans-unit id="ac4d2e190f0cd2aa60d91be274710c3228a96e55" datatype="html">
        <source>By continuing the payment , you agree to our</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">65</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">148</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">302</context>
        </context-group>
        <target state="translated">支払いを続けることにより、あなたは私たちに同意します</target>
      </trans-unit>
      <trans-unit id="aa4f4b7c81ae9cabfcebc2173f31e3f4bf08d833" datatype="html">
        <source>Terms of Service</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">65</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">149</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">303</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">274</context>
        </context-group>
        <target state="translated">サービス利用規約</target>
      </trans-unit>
      <trans-unit id="c6b3e99da22bb5c7a1e369aea06c790b82f68d94" datatype="html">
        <source> and </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">65</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">149</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">303</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">274</context>
        </context-group>
        <target state="translated">と</target>
      </trans-unit>
      <trans-unit id="b8d10cd55fae4e4ad4f87d28e18251694f159bf7" datatype="html">
        <source>Privacy Policy</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">65</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">150</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">304</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">274</context>
        </context-group>
        <target state="translated">プライバシーポリシー.</target>
      </trans-unit>
      <trans-unit id="7812bede1dc695934c854942928f33c0f34a0a13" datatype="html">
        <source>Pay quickly by</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">68</context>
        </context-group>
        <target state="translated">ですぐに支払う</target>
      </trans-unit>
      <trans-unit id="72d1017b7becbd9f94b44708129d2d92bbb52f70" datatype="html">
        <source>AliPay</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">70</context>
        </context-group>
        <target state="translated">
          AliPay</target>
      </trans-unit>
      <trans-unit id="0f6b64484d8c4ae67b7686974d00329ee6d580b0" datatype="html">
        <source>AliPay(1)</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">71</context>
        </context-group>
        <target state="needs-translation">AliPay(1)</target>
      </trans-unit>
      <trans-unit id="99d240b4c5587da8810fb95b0f03b09051d52e1a" datatype="html">
        <source>Pay by card</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">75</context>
        </context-group>
        <target state="translated">クレジットカード支払い</target>
      </trans-unit>
      <trans-unit id="53d20cc6953546db5373b1ae5a686aa655a715bb" datatype="html">
        <source>Pay Now</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/payment/payment.component.html</context>
          <context context-type="linenumber">78</context>
        </context-group>
        <target state="translated">購入</target>
      </trans-unit>
      <trans-unit id="55dd716ab97166ea22d93befd486d55a378763b0" datatype="html">
        <source>Redeem points for data*</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/redeem-dialog.html</context>
          <context context-type="linenumber">49</context>
        </context-group>
        <target state="translated">ポイントを使える容量に交換する</target>
      </trans-unit>
      <trans-unit id="7fe760f942645c0fccbff49b3e55a0b722a07d06" datatype="html">
        <source>Redeem 3 Pts for 5G data</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/redeem-dialog.html</context>
          <context context-type="linenumber">62</context>
        </context-group>
        <target state="translated">３ポイントを５G容量に交換</target>
      </trans-unit>
      <trans-unit id="77a191680ee66ee619dcff60c5e74c87141bdca5" datatype="html">
        <source>Redeem 5 Pts for 10G data</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/redeem-dialog.html</context>
          <context context-type="linenumber">73</context>
        </context-group>
        <target state="translated">５ポイントを１０G容量に交換</target>
      </trans-unit>
      <trans-unit id="6e52eca8629e83248c592f04b72d8ba8bc6bea8d" datatype="html">
        <source>Redeem</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/redeem-dialog.html</context>
          <context context-type="linenumber">77</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">158</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/rewards/transactions/transactions.component.html</context>
          <context context-type="linenumber">43</context>
        </context-group>
        <target state="translated">交換する</target>
      </trans-unit>
      <trans-unit id="1764de3a471aeef29090915760f96e192b340535" datatype="html">
        <source>*We will deduct the same amount from your current used data</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/redeem-dialog.html</context>
          <context context-type="linenumber">78</context>
        </context-group>
        <target state="translated">現在使用されているデータから同じ金額を差し引きます</target>
      </trans-unit>
      <trans-unit id="7a88ca54d3f1e8fa2c534696c1e8e19a97f6ac24" datatype="html">
        <source>Send your points to friends</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/send-dialog.html</context>
          <context context-type="linenumber">22</context>
        </context-group>
        <target state="translated">ポイントを友達に送る</target>
      </trans-unit>
      <trans-unit id="4c47b69693858b1da6eba8b99b358b0d331312a8" datatype="html">
        <source>address</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/send-dialog.html</context>
          <context context-type="linenumber">28</context>
        </context-group>
        <note priority="1" from="description">address</note>
        <target state="translated">アドレス</target>
      </trans-unit>
      <trans-unit id="333b476386eddcd514642db71383c93aa977c15e" datatype="html">
        <source>amount</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/send-dialog.html</context>
          <context context-type="linenumber">33</context>
        </context-group>
        <note priority="1" from="description">amount</note>
        <target state="translated">金額</target>
      </trans-unit>
      <trans-unit id="2c5ff8fa9c9aaec93f97e37c9a0edcd797194573" datatype="html">
        <source>Send</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/send-dialog.html</context>
          <context context-type="linenumber">37</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">169</context>
        </context-group>
        <target state="translated">送る</target>
      </trans-unit>
      <trans-unit id="a3d2bce41c906bc14990d0ea7bf058bdd70831e7" datatype="html">
        <source>Share your address to receive</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/receive-dialog.html</context>
          <context context-type="linenumber">40</context>
        </context-group>
        <target state="translated">受け取るためにご住所？？を共有する</target>
      </trans-unit>
      <trans-unit id="1979da7460819153e11d2078244645d94291b69c" datatype="html">
        <source>Copy</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/receive-dialog.html</context>
          <context context-type="linenumber">45</context>
        </context-group>
        <target state="translated">コピー</target>
      </trans-unit>
      <trans-unit id="ae6cee457adbb55a6ac59472b8a12f12bfce2ce6" datatype="html">
        <source>Ignore this version</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/update-dialog.html</context>
          <context context-type="linenumber">15</context>
        </context-group>
        <target state="translated">このバージョンを無視</target>
      </trans-unit>
      <trans-unit id="c281c9511379a759506f70bea74d08cb9236ab78" datatype="html">
        <source>Payments</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">119</context>
        </context-group>
        <target state="needs-translation">Payments</target>
      </trans-unit>
      <trans-unit id="e910af10105bdfcbde21ceeed73b38a92a759838" datatype="html">
        <source>Fill out your email to create an account</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">159</context>
        </context-group>
        <target state="translated">メールアドレスを入れて、アカウントを作る</target>
      </trans-unit>
      <trans-unit id="487bee63f2fb232c3ec218a0afe9f4d80790bd05" datatype="html">
        <source>Pick your preferred payment method</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/payments-dialog.html</context>
          <context context-type="linenumber">185</context>
        </context-group>
        <target state="translated">支払い方法を選んでください</target>
      </trans-unit>
      <trans-unit id="5b24948466e5dd22b631d0cd5eae25fa4f545e0e" datatype="html">
        <source>You can use our service with the following App</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/windows-export-dialog.html</context>
          <context context-type="linenumber">6</context>
        </context-group>
        <target state="needs-translation">You can use our service with the following App</target>
      </trans-unit>
      <trans-unit id="4c53fb6ecbfb005ff729178d32f76b4fccfe2b37" datatype="html">
        <source>Are you sure to update the billing cycle?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/update-confirm-dialog.html</context>
          <context context-type="linenumber">25</context>
        </context-group>
        <target state="needs-translation">Are you sure to update the billing cycle?</target>
      </trans-unit>
      <trans-unit id="f53f905322fc5d5ed19d18ce121ce369bee58521" datatype="html">
        <source>You will update the billing cycle to </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/update-confirm-dialog.html</context>
          <context context-type="linenumber">27</context>
        </context-group>
        <target state="needs-translation">You will update the billing cycle to </target>
      </trans-unit>
      <trans-unit id="8d7d399b08caa414576570014fbd45fabd3a6773" datatype="html">
        <source>When you update the billing cycle, unpaid bills will be cancelled, and the billing
          cycle will take effect immediately.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/update-confirm-dialog.html</context>
          <context context-type="linenumber">28</context>
        </context-group>
        <target state="needs-translation">When you update the billing cycle, unpaid bills will be
          cancelled, and the billing cycle will take effect immediately.</target>
      </trans-unit>
      <trans-unit id="d7b35c384aecd25a516200d6921836374613dfe7" datatype="html">
        <source>Cancel</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/update-confirm-dialog.html</context>
          <context context-type="linenumber">31</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/upgrade-confirm-dialog.html</context>
          <context context-type="linenumber">31</context>
        </context-group>
        <target state="needs-translation">Cancel</target>
      </trans-unit>
      <trans-unit id="68e710782ccb5398b3acb8844caf0b199da2c3da" datatype="html">
        <source>Confirm</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/update-confirm-dialog.html</context>
          <context context-type="linenumber">32</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/upgrade-confirm-dialog.html</context>
          <context context-type="linenumber">32</context>
        </context-group>
        <target state="needs-translation">Confirm</target>
      </trans-unit>
      <trans-unit id="c90df7d1332308866c3bc185871df517efb95c1b" datatype="html">
        <source>Are you sure to upgrade/downgrade the service?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/upgrade-confirm-dialog.html</context>
          <context context-type="linenumber">25</context>
        </context-group>
        <target state="needs-translation">Are you sure to upgrade/downgrade the service?</target>
      </trans-unit>
      <trans-unit id="e681d25206ab1d6b9f4d96315cc17a0801b80bfa" datatype="html">
        <source>You will change to </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/upgrade-confirm-dialog.html</context>
          <context context-type="linenumber">27</context>
        </context-group>
        <target state="needs-translation">You will change to </target>
      </trans-unit>
      <trans-unit id="c77116e7f886020caeb0874bd444aca1929feef1" datatype="html">
        <source>When you upgrade/downgrade your service, your unpaid bill will be cancelled, and you
          may be required to pay the upgrade bill. If payment is required, your service will be
          upgraded after you pay, otherwise your service will be completed immediately change.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/dialogs/upgrade-confirm-dialog.html</context>
          <context context-type="linenumber">28</context>
        </context-group>
        <target state="needs-translation">When you upgrade/downgrade your service, your unpaid bill
          will be cancelled, and you may be required to pay the upgrade bill. If payment is
          required, your service will be upgraded after you pay, otherwise your service will be
          completed immediately change.</target>
      </trans-unit>
      <trans-unit id="86f0e2a1daba8f1ce6b6bf1a88db57ecfe8fba70" datatype="html">
        <source>Out of <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;" /><x
            id="INTERPOLATION" equiv-text="{{service.usageCapInGB()}}" />GB <x id="CLOSE_TAG_SPAN"
            ctype="x-span" equiv-text="&lt;/span&gt;" />monthly data, you have used <x
            id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;" /><x id="INTERPOLATION_1"
            equiv-text="{{service.used()}}" />%<x id="CLOSE_TAG_SPAN" ctype="x-span"
            equiv-text="&lt;/span&gt;" /></source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/dashboard/dashboard.component.html</context>
          <context context-type="linenumber">42</context>
        </context-group>
        <target state="translated"><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;" /><x
            id="INTERPOLATION" equiv-text="{{service.usageCapInGB()}}" /> GB <x id="CLOSE_TAG_SPAN"
            ctype="x-span" equiv-text="&lt;/span&gt;" />の月次データのうち、 <x id="START_TAG_SPAN"
            ctype="x-span" equiv-text="&lt;span&gt;" /><x id="INTERPOLATION_1"
            equiv-text="{{service.used()}}" />%<x id="CLOSE_TAG_SPAN" ctype="x-span"
            equiv-text="&lt;/span&gt;" /> を使用しました。</target>
      </trans-unit>
      <trans-unit id="81f2a727d1358e62164a9f5706b375ba22e89ff0" datatype="html">
        <source>It expires in <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;" /><x
            id="INTERPOLATION" equiv-text="{{service.daysTillDue().toFixed(0)}}" /> <x
            id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;" /> days</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/dashboard/dashboard.component.html</context>
          <context context-type="linenumber">44</context>
        </context-group>
        <target state="translated"><x id="INTERPOLATION"
            equiv-text="{{service.daysTillDue().toFixed(0)}}" /><x id="CLOSE_TAG_SPAN"
            ctype="x-span" equiv-text="&lt;/span&gt;" />に有効期限が切れます</target>
      </trans-unit>
      <trans-unit id="6af9db3c59a4dd29c1e89f32c8c489c24722e6b9" datatype="html">
        <source>It has been suspended, please reactivate it.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/dashboard/dashboard.component.html</context>
          <context context-type="linenumber">48</context>
        </context-group>
        <target state="translated">一時停止されています。再開してください。</target>
      </trans-unit>
      <trans-unit id="61585a525da1d0ec9f3aa477c24ecf27473db1c4" datatype="html">
        <source>Open in <x id="INTERPOLATION" equiv-text="{{app}}" /></source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/dashboard/dashboard.component.html</context>
          <context context-type="linenumber">66</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/versions/redirect/redirect.component.html</context>
          <context context-type="linenumber">6</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/versions/redirect/redirect.component.html</context>
          <context context-type="linenumber">7</context>
        </context-group>
        <target state="translated"><x id="INTERPOLATION" equiv-text="{{app}}" />で開く</target>
      </trans-unit>
      <trans-unit id="6af6983df3c5b72180551b79101edb041ef0918e" datatype="html">
        <source>Open in iOS</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/dashboard/dashboard.component.html</context>
          <context context-type="linenumber">75</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/dashboard/dashboard.component.html</context>
          <context context-type="linenumber">112</context>
        </context-group>
        <target state="translated">iOSで開く</target>
      </trans-unit>
      <trans-unit id="36fa5d3cf7f67122f72e480c7350968d18619e6c" datatype="html">
        <source>Open in Android</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/dashboard/dashboard.component.html</context>
          <context context-type="linenumber">79</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/dashboard/dashboard.component.html</context>
          <context context-type="linenumber">116</context>
        </context-group>
        <target state="translated">Androidで開く</target>
      </trans-unit>
      <trans-unit id="362d25d6759b27f95abfc72373c99450eba607bf" datatype="html">
        <source>System Proxy</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/dashboard/dashboard.component.html</context>
          <context context-type="linenumber">86</context>
        </context-group>
        <target state="translated">
          システム プロキシ</target>
      </trans-unit>
      <trans-unit id="25f67787e15f68ce70409f121d66d1268f1c7f61" datatype="html">
        <source>Welcome to</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/welcome/welcome.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
        <target state="translated">ようこそ</target>
      </trans-unit>
      <trans-unit id="0e272491a232c89c99f7b65573af2b3cd6a1e1f9" datatype="html">
        <source>Not our user yet? Get free trial right now</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/welcome/welcome.component.html</context>
          <context context-type="linenumber">12</context>
        </context-group>
        <target state="needs-translation">Not our user yet? Get free trial right now</target>
      </trans-unit>
      <trans-unit id="123280b173ee252fa4a8f0830fba2c03460a9c84" datatype="html">
        <source>If you are an old friend</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/welcome/welcome.component.html</context>
          <context context-type="linenumber">15</context>
        </context-group>
        <target state="translated">既にご購入いただいたことのある方々</target>
      </trans-unit>
      <trans-unit id="04ba475d8e9406b3cde3f564d6400805979a1632" datatype="html">
        <source>Checkout our recent deals</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/welcome/welcome.component.html</context>
          <context context-type="linenumber">18</context>
        </context-group>
        <target state="translated">最新のお得な情報をチェックしてください</target>
      </trans-unit>
      <trans-unit id="58684361b36215d7fa66e03627979e4ea7ba04af" datatype="html">
        <source>Buy Products</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/welcome/welcome.component.html</context>
          <context context-type="linenumber">19</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/noservice/noservice.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
        <target state="translated">商品を購入する</target>
      </trans-unit>
      <trans-unit id="invoices-properties-status" datatype="html">
        <source>Status</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/invoices/invoices.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">30</context>
        </context-group>
        <target state="translated">ステータス</target>
      </trans-unit>
      <trans-unit id="invoices-properties-user" datatype="html">
        <source>Invoiced to</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/invoices/invoices.component.html</context>
          <context context-type="linenumber">13</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">31</context>
        </context-group>
        <target state="translated">
          請求先</target>
      </trans-unit>
      <trans-unit id="invoices-properties-description" datatype="html">
        <source>Description</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/invoices/invoices.component.html</context>
          <context context-type="linenumber">17</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">32</context>
        </context-group>
        <target state="translated">
          説明</target>
      </trans-unit>
      <trans-unit id="invoices-properties-total" datatype="html">
        <source>Total</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/invoices/invoices.component.html</context>
          <context context-type="linenumber">21</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">33</context>
        </context-group>
        <target state="translated">全額</target>
      </trans-unit>
      <trans-unit id="invoices-properties-due" datatype="html">
        <source>Due</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/invoices/invoices.component.html</context>
          <context context-type="linenumber">25</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">34</context>
        </context-group>
        <target state="translated">期限切れの日</target>
      </trans-unit>
      <trans-unit id="invoices-properties-payed-date" datatype="html">
        <source>Paid in</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/invoices/invoices.component.html</context>
          <context context-type="linenumber">29</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">35</context>
        </context-group>
        <target state="translated">ｘｘに支払済み</target>
      </trans-unit>
      <trans-unit id="82ac8ec728fb6d36f85eb9f59d343bc6ea01ab38" datatype="html">
        <source>Payment</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/billings/invoices/invoices.component.html</context>
          <context context-type="linenumber">36</context>
        </context-group>
        <target state="translated">支払い</target>
      </trans-unit>
      <trans-unit id="9ff23880c0122b8a96542805cb15393b546f4ddf" datatype="html">
        <source>Protect your internet right now</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">254</context>
        </context-group>
        <target state="needs-translation">Protect your internet right now</target>
      </trans-unit>
      <trans-unit id="037bf1fb53810a95da5093c79898f168d7ffadfe" datatype="html">
        <source>Reach your content in the speed of flash light</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">266</context>
        </context-group>
        <target state="needs-translation">Reach your content in the speed of flash light</target>
      </trans-unit>
      <trans-unit id="1244d01d2d42721bf8751d01173904216fbbdaa4" datatype="html">
        <source>Secure your internet without sacrificing your identity</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">267</context>
        </context-group>
        <target state="needs-translation">Secure your internet without sacrificing your identity</target>
      </trans-unit>
      <trans-unit id="7f9b8a6216fb31ed627c3683a8afabb5fb59d259" datatype="html">
        <source>Enjoy the same intuitive experience and supports across all the platforms</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">268</context>
        </context-group>
        <target state="needs-translation">Enjoy the same intuitive experience and supports across
          all the platforms</target>
      </trans-unit>
      <trans-unit id="00af816bb3eceb679ec5152add644f381ddaecd9" datatype="html">
        <source>Billed Monthly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">274</context>
        </context-group>
        <target state="translated">毎月請求</target>
      </trans-unit>
      <trans-unit id="48380d99a59adeb3c26de386abc0d356b5b21720" datatype="html">
        <source>Billed Quarterly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">275</context>
        </context-group>
        <target state="translated">四半期ごとに請求</target>
      </trans-unit>
      <trans-unit id="470a058eabf164a06da496c005846e589e59c5d5" datatype="html">
        <source>Billed Semi-Annually</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">276</context>
        </context-group>
        <target state="translated">半年ごとに請求</target>
      </trans-unit>
      <trans-unit id="67e97417667a66059b3a0b9ae1c4e248f2603e33" datatype="html">
        <source>Billed Annually</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">277</context>
        </context-group>
        <target state="translated">毎年請求</target>
      </trans-unit>
      <trans-unit id="6596867bed0c9ef63858f956d254a1c346a45040" datatype="html">
        <source>Billed Biennially</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">278</context>
        </context-group>
        <target state="needs-translation">Billed Biennially</target>
      </trans-unit>
      <trans-unit id="52c9a103b812f258bcddc3d90a6e3f46871d25fe" datatype="html">
        <source>Save</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">284</context>
        </context-group>
        <target state="needs-translation">Save</target>
      </trans-unit>
      <trans-unit id="f14641ebe74d0a343fe715afd9644e2c8e94eb23" datatype="html">
        <source>Yearly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">291</context>
        </context-group>
        <target state="needs-translation">Yearly</target>
      </trans-unit>
      <trans-unit id="ba4fa9ae6b1182174c65f7adbdb1f06793fc7d86" datatype="html">
        <source>Continue to Payment</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/products/products/products.component.html</context>
          <context context-type="linenumber">297</context>
        </context-group>
        <target state="needs-translation">Continue to Payment</target>
      </trans-unit>
      <trans-unit id="48dfb134cb8d324bb45f2f9f3bd7d7afc49bc3f8" datatype="html">
        <source>Products</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">144</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">279</context>
        </context-group>
        <target state="translated">商品</target>
      </trans-unit>
      <trans-unit id="4c78c0da1bcd65bc0598c4d9474bde165de27d7a" datatype="html">
        <source>Apps</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">148</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">278</context>
        </context-group>
        <target state="translated">アプリ</target>
      </trans-unit>
      <trans-unit id="f55870a43ffc113a6ab187ab4cd84a9b37f03ae9" datatype="html">
        <source>The Only VPN You Ever Need</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">165</context>
        </context-group>
        <target state="translated">最も使いやすいVPN</target>
      </trans-unit>
      <trans-unit id="de8573effe6fd72beffe9a75b685a804ddd7b8d3" datatype="html">
        <source>FlashVPN is a simple to use, fast and stable VPN service that runs all of your
          devices. It bypass firewalls and unlocks geo-restricted contents.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">167</context>
        </context-group>
        <target state="needs-translation">FlashVPN is a simple to use, fast and stable VPN service
          that runs all of your devices. It bypass firewalls and unlocks geo-restricted contents.</target>
      </trans-unit>
      <trans-unit id="dee15caad710143ef3b6c5721800826cb38849ae" datatype="html">
        <source>Try Our Free Trial</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">168</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">211</context>
        </context-group>
        <target state="needs-translation">Try Our Free Trial</target>
      </trans-unit>
      <trans-unit id="7cc69674fab3a6ddbd2536061366440dbfb06a7c" datatype="html">
        <source>Cross Platform APPs</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">178</context>
        </context-group>
        <target state="translated">マルチプラットフォーム</target>
      </trans-unit>
      <trans-unit id="c2b3e93ba7c9ab80beb89b662a3651c716124f51" datatype="html">
        <source>We provide iOS, Android, Windows, macOS APPs, they all feel the same.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">179</context>
        </context-group>
        <target state="translated">iOS, Android, Windows, macOS APPsを提供しております。どれも同じです</target>
      </trans-unit>
      <trans-unit id="61f3f120dc59f910434dd583fb67bb008b79f2ea" datatype="html">
        <source>Works in Browser</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">183</context>
        </context-group>
        <target state="translated">ブラウザで動作</target>
      </trans-unit>
      <trans-unit id="d6e76ce15f3d296e13e8ab9aadbad9547997510a" datatype="html">
        <source>Our Chrome/Edge extension connect directly to our servers, which is much faster and
          stable.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">184</context>
        </context-group>
        <target state="translated">Chrome / Edge拡張機能はサーバーに直接接続するため、はるかに高速で安定しています。</target>
      </trans-unit>
      <trans-unit id="7738c94bbe6ef4dddbba37c764faaee043825010" datatype="html">
        <source>Absolutely No Logs</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">188</context>
        </context-group>
        <target state="translated">見た記録は全部消されます</target>
      </trans-unit>
      <trans-unit id="06841f4a6ac3793a8e28236a7ed126d8b2d36bf1" datatype="html">
        <source>Your browsing history is nothing but liability to us, so why bother to save. You are
          safe with us.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">189</context>
        </context-group>
        <target state="translated">登録した個人情報や閲覧履歴など、すべて削除することを保証します</target>
      </trans-unit>
      <trans-unit id="e1e9235dda71dc2d3c534fff9251daaf89d2448d" datatype="html">
        <source>Unlock Contents</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">193</context>
        </context-group>
        <target state="translated">コンテンツのロックを解除</target>
      </trans-unit>
      <trans-unit id="005f52f26f11891abd3f46f9a6fc5e005e35a8f6" datatype="html">
        <source>We have servers all over the world, you can watch Netflix, HBO, AmebaTV anywhere you
          want.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">194</context>
        </context-group>
        <target state="translated">世界中にサーバーを設置されております。いつでもどこにいてもNetflix, HBO, AmebaTV を見ることができます
</target>
      </trans-unit>
      <trans-unit id="b9515ed4d976c2b0a12a8885a515d6914f508fb2" datatype="html">
        <source>Too Many Failed VPN?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">199</context>
        </context-group>
        <target state="translated">過去のＶＰＮが使いづらかった？</target>
      </trans-unit>
      <trans-unit id="174288646fe9390a769deade0b224ebcb54ba15c" datatype="html">
        <source>With FlashVPN, you won&apos;t look again.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">200</context>
        </context-group>
        <target state="needs-translation">With FlashVPN, you won't look again.</target>
      </trans-unit>
      <trans-unit id="fafc4eb23bbf1caa4bec638219206121fd4ea224" datatype="html">
        <source>Holistic Experience</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">206</context>
        </context-group>
        <target state="translated">ホリスティック エクスペリエンス</target>
      </trans-unit>
      <trans-unit id="b5c90d44302b2d50dad7b27bf278c52cd2ae2841" datatype="html">
        <source>All apps shares the same simple and intuitive interfaces.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">207</context>
        </context-group>
        <target state="translated">全てのアプリは同じなページをシェアしていて、使い慣れやすいです</target>
      </trans-unit>
      <trans-unit id="c026ca7a40c74e7a6cea7125d2efea2c35eb1b54" datatype="html">
        <source>A single account can be used on all devices.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">210</context>
        </context-group>
        <target state="translated">一つのアカウントですべでのに使えます</target>
      </trans-unit>
      <trans-unit id="3daf8bc63bc186f881e33ca747fef640e776c8cb" datatype="html">
        <source>Fast and Stable</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">221</context>
        </context-group>
        <target state="translated">高速かつ安定</target>
      </trans-unit>
      <trans-unit id="36ddab684943f0acbf99ca38c1db99b3448ce739" datatype="html">
        <source>All servers are monitored, idle servers will never appear.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">222</context>
        </context-group>
        <target state="translated">すべでのサーバーがスムーズに使っていただけます</target>
      </trans-unit>
      <trans-unit id="65f8c474dcb27012576611caf6c5b096fff38081" datatype="html">
        <source>Works very well within China thanks to our advanced technology.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">225</context>
        </context-group>
        <target state="translated">最新テクノロジーを採用しています。中国においてのご利用も可能</target>
      </trans-unit>
      <trans-unit id="802222cb4754d74846b18f651b1f94e21576185d" datatype="html">
        <source>Get Started</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">230</context>
        </context-group>
        <target state="translated">始めましょう</target>
      </trans-unit>
      <trans-unit id="be28e0433fe8c86a39313df757df4821c2ebb558" datatype="html">
        <source>Live servers - click to play</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">236</context>
        </context-group>
        <target state="translated">オンラインサーバー</target>
      </trans-unit>
      <trans-unit id="55ea25bca34587d835cff3cbbb601a6b1985f3f0" datatype="html">
        <source>We aim to provide the best networking facility tools to help customers to reach
          their desired content.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">272</context>
        </context-group>
        <target state="translated">お客様が入手したい情報を取得するために、私たちは優れているネットワークのツールをご提供させていただきます</target>
      </trans-unit>
      <trans-unit id="dc60677d5a906e69f38a5cf9da7f2eb03931bea0" datatype="html">
        <source>Links</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">277</context>
        </context-group>
        <target state="translated">接続</target>
      </trans-unit>
      <trans-unit id="6ef0619a37140118b9e640c6cc36ee4e9fb4372a" datatype="html">
        <source>Company</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">284</context>
        </context-group>
        <target state="translated">会社</target>
      </trans-unit>
      <trans-unit id="5bf50e3c2cc4baa009da66e7bf86058421aa2e34" datatype="html">
        <source>Partner Program</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">285</context>
        </context-group>
        <target state="translated">パートナープログラム</target>
      </trans-unit>
      <trans-unit id="3d8b3af3c6fac71393bd467b618a924a6961980c" datatype="html">
        <source>FlashLite</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">286</context>
        </context-group>
        <target state="translated">
          FlashLite</target>
      </trans-unit>
      <trans-unit id="397bbf6a5f622a9afc1e527bc748086e1945d73a" datatype="html">
        <source>FlashPro</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">287</context>
        </context-group>
        <target state="translated">FlashPro</target>
      </trans-unit>
      <trans-unit id="b8c9dd63e634e627f7903b1306383771c16ba83c" datatype="html">
        <source>Help &amp; Contact</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">290</context>
        </context-group>
        <target state="translated">ヘルプ &amp; お問い合わせ</target>
      </trans-unit>
      <trans-unit id="69703274543d03e3dcf78103d49d441c84f72252" datatype="html">
        <source>Help Center</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/home/<USER>/landing.component.html</context>
          <context context-type="linenumber">291</context>
        </context-group>
        <target state="translated">ヘルプセンター</target>
      </trans-unit>
      <trans-unit id="34557152cff7bc7db72a2a8706794d7fba4bac86" datatype="html">
        <source>Reset Password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password/forget-password.component.html</context>
          <context context-type="linenumber">12</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/settings/setting-base/setting-base.component.html</context>
          <context context-type="linenumber">17</context>
        </context-group>
        <target state="translated">パスワードをリセット</target>
      </trans-unit>
      <trans-unit id="06c663bf1474713f57551123a46b34318543b67d" datatype="html">
        <source>Reset</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password/forget-password.component.html</context>
          <context context-type="linenumber">19</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password-verify/forget-password-verify.component.html</context>
          <context context-type="linenumber">27</context>
        </context-group>
        <target state="translated">リセット</target>
      </trans-unit>
      <trans-unit id="97fe35bccf7ceb64ff86a7f4bafa1ae28e0c8286" datatype="html">
        <source>Set New Password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password-verify/forget-password-verify.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
        <target state="translated">新しいパスワードを作る</target>
      </trans-unit>
      <trans-unit id="aff139f6505da1e3716f104b24e229dcbf73649e" datatype="html">
        <source>We have send a confirmation code to your email, please input here to reset your
          password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password-verify/forget-password-verify.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
        <target state="translated">認証コードをメールボックスにお送りしました。新しいパスワードを作成してください</target>
      </trans-unit>
      <trans-unit id="2880518d3a5544fa6cd834b1ac80e47978a9c7c4" datatype="html">
        <source>Confirm your password</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/users/forget-password-verify/forget-password-verify.component.html</context>
          <context context-type="linenumber">22</context>
        </context-group>
        <target state="translated">パスワードを確認してください</target>
      </trans-unit>
      <trans-unit id="551c850d45e9ddb46a7090ac77f2f8f590fccf3c" datatype="html">
        <source>Welcome back</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/versions/redirect/redirect.component.html</context>
          <context context-type="linenumber">2</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/noservice/noservice.component.html</context>
          <context context-type="linenumber">8</context>
        </context-group>
        <target state="translated">お帰りなさい</target>
      </trans-unit>
      <trans-unit id="7799b5a568e3f0dbe933864a527ce9e53a46d092" datatype="html">
        <source>We detected that you are redirected from our own app</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/versions/redirect/redirect.component.html</context>
          <context context-type="linenumber">3</context>
        </context-group>
        <target state="translated">私たちのアプリを使ってこちらへ来たことを検知しました</target>
      </trans-unit>
      <trans-unit id="2c662315923a127c3cc5febb940390e375d7e868" datatype="html">
        <source>Go to services</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/versions/redirect/redirect.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/payment-success/payment-success.component.html</context>
          <context context-type="linenumber">6</context>
        </context-group>
        <target state="translated">サービスページへ行く</target>
      </trans-unit>
      <trans-unit id="5b609ea68a76f02b50d7c87dcde392d88e87350c" datatype="html">
        <source>Google Store</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/versions/app-version/app-version.component.html</context>
          <context context-type="linenumber">27</context>
        </context-group>
        <target state="translated">Google Store</target>
      </trans-unit>
      <trans-unit id="f364c410d3b190f0249f269c20fb7221da9679e0" datatype="html">
        <source>Apple Store</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/versions/app-version/app-version.component.html</context>
          <context context-type="linenumber">31</context>
        </context-group>
        <target state="translated">Apple Store</target>
      </trans-unit>
      <trans-unit id="66c8ed6c5d8c6caac9461308d54cc78d9d5e538d" datatype="html">
        <source>Chrome Store</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/versions/app-version/app-version.component.html</context>
          <context context-type="linenumber">51</context>
        </context-group>
        <target state="translated">Chrome Store</target>
      </trans-unit>
      <trans-unit id="9dbf98ceea36de92c62c23f14fa1b5aa59318605" datatype="html">
        <source>You have not got any services, checkout our recent deals</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/noservice/noservice.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
        <target state="translated">まだお買い物しておりません。最新のお得な情報をチェックしてください</target>
      </trans-unit>
      <trans-unit id="92ea2d04b908efbe694f6b6ede43779cf6ac9a3f" datatype="html">
        <source>Verify your email address to get a trial </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/noservice/noservice.component.html</context>
          <context context-type="linenumber">16</context>
        </context-group>
        <target state="needs-translation">Verify your email address to get a trial </target>
      </trans-unit>
      <trans-unit id="country-us" datatype="html">
        <source>US</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">3</context>
        </context-group>
        <target state="translated">米国</target>
      </trans-unit>
      <trans-unit id="country-russia" datatype="html">
        <source>Russia</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">4</context>
        </context-group>
        <target state="translated">ロシア</target>
      </trans-unit>
      <trans-unit id="country-japan" datatype="html">
        <source>Japan</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">5</context>
        </context-group>
        <target state="translated">日本</target>
      </trans-unit>
      <trans-unit id="country-germany" datatype="html">
        <source>Germany</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">6</context>
        </context-group>
        <target state="translated">ドイツ</target>
      </trans-unit>
      <trans-unit id="country-netherlands" datatype="html">
        <source>Netherlands</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">7</context>
        </context-group>
        <target state="translated">オランダ</target>
      </trans-unit>
      <trans-unit id="content-netflix" datatype="html">
        <source>Netflix</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
        <target state="translated">ネットフリックス
</target>
      </trans-unit>
      <trans-unit id="node-feature-netflix" datatype="html">
        <source>Unlock Netflix.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">14</context>
        </context-group>
        <target state="translated">ネットフリックスのブロック解除</target>
      </trans-unit>
      <trans-unit id="node-feature-us-IP" datatype="html">
        <source>US IP and unlocks US contents.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">15</context>
        </context-group>
        <target state="translated">米国コンテンツのロックを解除します。</target>
      </trans-unit>
      <trans-unit id="node-feature-1000M" datatype="html">
        <source>Up to 1000Mbps.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">16</context>
        </context-group>
        <target state="translated">高速接続では最大1000Mbps</target>
      </trans-unit>
      <trans-unit id="node-feature-200M" datatype="html">
        <source>Up to 200Mbps.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">17</context>
        </context-group>
        <target state="translated">高速接続では最大200Ｍbps</target>
      </trans-unit>
      <trans-unit id="node-feature-100M" datatype="html">
        <source>Up to 100Mbps.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">18</context>
        </context-group>
        <target state="translated">最大１００Mbps
</target>
      </trans-unit>
      <trans-unit id="users-verify-email" datatype="html">
        <source>Verify Email</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">21</context>
        </context-group>
        <target state="translated">認証メール</target>
      </trans-unit>
      <trans-unit id="users-email-code" datatype="html">
        <source>Thank you for your signing up, please copy the following verification code</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">22</context>
        </context-group>
        <target state="translated">新規登録してくださってありがとうございます。下記の認証コードをコピーしてください</target>
      </trans-unit>
      <trans-unit id="users-support-hint" datatype="html">
        <source>Let us know how it goes by simply replying to this email or contact our custom
          service at</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">23</context>
        </context-group>
        <target state="translated">ご不明点や質問などございましたら、気軽にこちらのメールにてご連絡ください</target>
      </trans-unit>
      <trans-unit id="users-download-app-hint" datatype="html">
        <source>Meanwhile, download our highly integrated APPS</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">24</context>
        </context-group>
        <target state="translated">高度に統合されたAPPSをダウンロードしてください？？？？</target>
      </trans-unit>
      <trans-unit id="users-forget-password" datatype="html">
        <source>Forget Password? Reset Now</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">25</context>
        </context-group>
        <target state="translated">パスワードを忘れた？リセットしましょう</target>
      </trans-unit>
      <trans-unit id="users-verification-code-hint" datatype="html">
        <source>Please copy the following verification code to where you initiated the request</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">26</context>
        </context-group>
        <target state="translated">リクエストを開始した場所に次の確認コードをコピーしてください</target>
      </trans-unit>
      <trans-unit id="invoices-unpaid" datatype="html">
        <source>Invoice Unpaid</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">28</context>
        </context-group>
        <target state="translated">未払いの請求書</target>
      </trans-unit>
      <trans-unit id="invoices-unpaid-hint" datatype="html">
        <source>The following invoice will due very soon, please pay in time to avoid service
          suspension.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">29</context>
        </context-group>
        <target state="translated">次の請求書はまもなく期限が切れます。サービスの停止を回避するために時間内にお支払いください。</target>
      </trans-unit>
      <trans-unit id="invoices-payment-hint" datatype="html">
        <source>Pay Now</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">36</context>
        </context-group>
        <target state="translated">購入する</target>
      </trans-unit>
      <trans-unit id="invoices-properties-status-paid" datatype="html">
        <source>Paid</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">37</context>
        </context-group>
        <target state="translated">支払う</target>
      </trans-unit>
      <trans-unit id="invoices-properties-status-unpaid" datatype="html">
        <source>Unpaid</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">38</context>
        </context-group>
        <target state="translated">未払い</target>
      </trans-unit>
      <trans-unit id="billing-monthly" datatype="html">
        <source>Monthly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">40</context>
        </context-group>
        <target state="translated">月に</target>
      </trans-unit>
      <trans-unit id="billing-quarterly" datatype="html">
        <source>Quarterly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">41</context>
        </context-group>
        <target state="translated">四半期ごとに</target>
      </trans-unit>
      <trans-unit id="billing-semiannually" datatype="html">
        <source>Semi-Annually</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">42</context>
        </context-group>
        <target state="translated">半年に</target>
      </trans-unit>
      <trans-unit id="billing-annually" datatype="html">
        <source>Annually</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">43</context>
        </context-group>
        <target state="translated">毎年に</target>
      </trans-unit>
      <trans-unit id="billing-biennially" datatype="html">
        <source>Biennially</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">44</context>
        </context-group>
        <target state="needs-translation">Biennially</target>
      </trans-unit>
      <trans-unit id="invoices-paid" datatype="html">
        <source>Invoice Paid</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">47</context>
        </context-group>
        <target state="translated">支払完了</target>
      </trans-unit>
      <trans-unit id="invoices-payed-hint" datatype="html">
        <source>Thank you for the payment. The following service has been extended</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">48</context>
        </context-group>
        <target state="translated">ご購入いただきありがとうございます。サービスは延長されました</target>
      </trans-unit>
      <trans-unit id="service-hint" datatype="html">
        <source>Check The Service</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">49</context>
        </context-group>
        <target state="translated">サービスをチェック</target>
      </trans-unit>
      <trans-unit id="campaigns-lite-title" datatype="html">
        <source>FlashLite - Free VPN Forever</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">51</context>
        </context-group>
        <target state="translated">FlashLite-今までにない無料のVPN </target>
      </trans-unit>
      <trans-unit id="campaigns-customer" datatype="html">
        <source>Dear Customer</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">52</context>
        </context-group>
        <target state="translated">お客様各位</target>
      </trans-unit>
      <trans-unit id="campaigns-introduction" datatype="html">
        <source>we are proud to introduce you</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">53</context>
        </context-group>
        <target state="translated">こちらの商品を紹介させていただけまして光栄です</target>
      </trans-unit>
      <trans-unit id="campaigns-lite-feature-free" datatype="html">
        <source>FlashLite is free, guaranteed forever</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">54</context>
        </context-group>
        <target state="translated">永遠に無料であることを誓います</target>
      </trans-unit>
      <trans-unit id="campaigns-lite-feature-sign-up" datatype="html">
        <source>No need to sign up, or whatsoever</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">55</context>
        </context-group>
        <target state="translated">サインアップする必要なし</target>
      </trans-unit>
      <trans-unit id="campaigns-lite-feature-privacy" datatype="html">
        <source>Fast and stable, no history recorded</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">56</context>
        </context-group>
        <target state="translated">高速で安定、履歴は記録されません</target>
      </trans-unit>
      <trans-unit id="campaigns-take-a-look" datatype="html">
        <source>Take A Look</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">57</context>
        </context-group>
        <target state="translated">見てみる</target>
      </trans-unit>
      <trans-unit id="module-users" datatype="html">
        <source>Users</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">60</context>
        </context-group>
        <target state="translated">ユーザー</target>
      </trans-unit>
      <trans-unit id="module-services" datatype="html">
        <source>Services</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">61</context>
        </context-group>
        <target state="translated">サービス</target>
      </trans-unit>
      <trans-unit id="module-billings" datatype="html">
        <source>Billings</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">62</context>
        </context-group>
        <target state="translated">お会計</target>
      </trans-unit>
      <trans-unit id="module-products" datatype="html">
        <source>Products</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">63</context>
        </context-group>
        <target state="translated">商品</target>
      </trans-unit>
      <trans-unit id="module-invoices" datatype="html">
        <source>Invoices</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">64</context>
        </context-group>
        <target state="translated">
          請求書</target>
      </trans-unit>
      <trans-unit id="module-plans" datatype="html">
        <source>Plans</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">65</context>
        </context-group>
        <target state="needs-translation">Plans</target>
      </trans-unit>
      <trans-unit id="module-settings" datatype="html">
        <source>Settings</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">66</context>
        </context-group>
        <target state="needs-translation">Settings</target>
      </trans-unit>
      <trans-unit id="module-rewards" datatype="html">
        <source>Rewards</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">67</context>
        </context-group>
        <target state="translated">
          報酬</target>
      </trans-unit>
      <trans-unit id="module-transactions" datatype="html">
        <source>Transactions</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">68</context>
        </context-group>
        <target state="translated">取引</target>
      </trans-unit>
      <trans-unit id="chrome-login-hint" datatype="html">
        <source>Login successfully, please reopen our chrome extension again</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">72</context>
        </context-group>
        <target state="translated">ログインに成功しました。Chrome拡張機能をもう一度開いてください</target>
      </trans-unit>
      <trans-unit id="extension-lang-switching-hint" datatype="html">
        <source>Please reopen the extension to change language</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">73</context>
        </context-group>
        <target state="translated">言語を変更するには、拡張機能を再度開いてください</target>
      </trans-unit>
      <trans-unit id="testing-latency-hint" datatype="html">
        <source>Testing latency of all servers</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">74</context>
        </context-group>
        <target state="translated">すべてのサーバーの待ち時間をテストする</target>
      </trans-unit>
      <trans-unit id="current-server-not-selected" datatype="html">
        <source>Please select a server first</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">75</context>
        </context-group>
        <target state="translated">先にサーバーを選択</target>
      </trans-unit>
      <trans-unit id="service-expired-hint" datatype="html">
        <source>Your service is expired, please renew</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">76</context>
        </context-group>
        <target state="translated">期限切れです、更新してください</target>
      </trans-unit>
      <trans-unit id="invoices-loading-failure-hint" datatype="html">
        <source>Failed to load the invoice, make sure you have signed in.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">77</context>
        </context-group>
        <target state="translated">請求書を読み込めませんでした。ログインしていることを確認してください。</target>
      </trans-unit>
      <trans-unit id="service-used-up-hint" datatype="html">
        <source>Your package is used up, you can redeem more data by flash points.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">78</context>
        </context-group>
        <target state="translated">コース終了しました。【溜めたポイントで容量データを引き換えることができます。】
          パッケージが使い果たされました。溜めたポイントで容量データを引き換えることができます。</target>
      </trans-unit>
      <trans-unit id="product-spark-description" datatype="html">
        <source>50G data plan each month, reset monthly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">82</context>
        </context-group>
        <target state="translated">毎月50Gデータプラン</target>
      </trans-unit>
      <trans-unit id="product-blaze-description" datatype="html">
        <source>100G data plan each month, reset monthly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">83</context>
        </context-group>
        <target state="translated">毎月100Gデータプラン</target>
      </trans-unit>
      <trans-unit id="product-lightning-description" datatype="html">
        <source>200G data plan each month, reset monthly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">84</context>
        </context-group>
        <target state="translated">毎月200Gデータプラン</target>
      </trans-unit>
      <trans-unit id="misc-or" datatype="html">
        <source>or</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">88</context>
        </context-group>
        <target state="translated">もしくは</target>
      </trans-unit>
      <trans-unit id="misc-on" datatype="html">
        <source>On</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">89</context>
        </context-group>
        <target state="translated">ON</target>
      </trans-unit>
      <trans-unit id="misc-off" datatype="html">
        <source>Off</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">90</context>
        </context-group>
        <target state="translated">OFF</target>
      </trans-unit>
      <trans-unit id="EmailDomainNotAllowed" datatype="html">
        <source>This domain is not allowed, please contact our CS.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">92</context>
        </context-group>
        <target state="translated">ご使用いただけません。
          カスタマーサポートまでご連絡ください</target>
      </trans-unit>
      <trans-unit id="EmailNotVerified" datatype="html">
        <source>You have not verified your email.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">93</context>
        </context-group>
        <target state="translated">まだメールアドレスの認証されていません。
</target>
      </trans-unit>
      <trans-unit id="CredentialIsWrong" datatype="html">
        <source>CredentialIsWrong</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">94</context>
        </context-group>
        <target state="translated">アカウントもしくはパスワードが間違いました</target>
      </trans-unit>
      <trans-unit id="EmailExisted" datatype="html">
        <source>EmailExisted</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">95</context>
        </context-group>
        <target state="translated">既に新規登録済みでした。
          ログインしてください</target>
      </trans-unit>
      <trans-unit id="ServiceExpired" datatype="html">
        <source>ServiceExpired</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">96</context>
        </context-group>
        <target state="translated">サービス期限切れ</target>
      </trans-unit>
      <trans-unit id="ReloadingService" datatype="html">
        <source>ReloadingService</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">97</context>
        </context-group>
        <target state="translated">読み込み中</target>
      </trans-unit>
      <trans-unit id="FailedToPayInvoice" datatype="html">
        <source>FailedToPayInvoice</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">98</context>
        </context-group>
        <target state="translated">支払いに失敗しました</target>
      </trans-unit>
      <trans-unit id="ReactivatingService" datatype="html">
        <source>ReactivatingService</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">99</context>
        </context-group>
        <target state="translated">サービスを再開する</target>
      </trans-unit>
      <trans-unit id="WrongVerificationCode" datatype="html">
        <source>WrongVerificationCode</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">100</context>
        </context-group>
        <target state="translated">認証コードが間違いました</target>
      </trans-unit>
      <trans-unit id="UserNotSignedUpYet" datatype="html">
        <source>UserNotSignedUpYet</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">101</context>
        </context-group>
        <target state="translated">まだ新規登録していません</target>
      </trans-unit>
      <trans-unit id="ConfirmedPasswordNotMatch" datatype="html">
        <source>ConfirmedPasswordNotMatch</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">102</context>
        </context-group>
        <target state="translated">パスワードが一致しません</target>
      </trans-unit>
      <trans-unit id="FailedToLoadInvoice" datatype="html">
        <source>FailedToLoadInvoice</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">103</context>
        </context-group>
        <target state="translated">
          請求書の読み込みに失敗しました</target>
      </trans-unit>
      <trans-unit id="WalletNotFound" datatype="html">
        <source>The wallet address cannot be found</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">104</context>
        </context-group>
        <target state="translated">ウォレットのアドレスが見つかりません
</target>
      </trans-unit>
      <trans-unit id="InsufficientFunds" datatype="html">
        <source>You do not have enough funds</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">105</context>
        </context-group>
        <target state="translated">ポンーとが足りません。</target>
      </trans-unit>
      <trans-unit id="AppIsLatestVersionHint" datatype="html">
        <source>The app is the latest version.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">106</context>
        </context-group>
        <target state="translated">お使いのバージョンは最新です。</target>
      </trans-unit>
      <trans-unit id="invite" datatype="html">
        <source>Invite</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">109</context>
        </context-group>
        <target state="translated">招待</target>
      </trans-unit>
      <trans-unit id="sharing-text" datatype="html">
        <source>sharing-text</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">110</context>
        </context-group>
        <target state="translated">FlsahVPN - Chrome VPN，iPhone VPN，macOS VPN，Android
          VPN；無料お試しを入手するに、ここにクリークする</target>
      </trans-unit>
      <trans-unit id="sharing-url-copied" datatype="html">
        <source>sharing-url-copied</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">111</context>
        </context-group>
        <target state="translated">共有URLコピーした</target>
      </trans-unit>
      <trans-unit id="insufficient-funds" datatype="html">
        <source>insufficient-funds</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">112</context>
        </context-group>
        <target state="translated">残高不足</target>
      </trans-unit>
      <trans-unit id="redeem-finished" datatype="html">
        <source>redeem-finished</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">113</context>
        </context-group>
        <target state="translated">交換完了</target>
      </trans-unit>
      <trans-unit id="sent-funds-successfully" datatype="html">
        <source>sent-funds-successfully</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">114</context>
        </context-group>
        <target state="translated">送金成功</target>
      </trans-unit>
      <trans-unit id="walletAddressCopied" datatype="html">
        <source>Wallet address is copied, share it with your friend and receive points</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">115</context>
        </context-group>
        <target state="translated">ウォレットアドレスがコピーされました。友達と共有し、ポイントを受け取りましょう</target>
      </trans-unit>
      <trans-unit id="DowngradeInfo" datatype="html">
        <source>We do not support downgrade at the moment, if you really need it, please click on
          the icon in the lower right corner to contact customer service</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">118</context>
        </context-group>
        <target state="needs-translation">We do not support downgrade at the moment, if you really
          need it, please click on the icon in the lower right corner to contact customer service</target>
      </trans-unit>
      <trans-unit id="NoNeedToUpgrade" datatype="html">
        <source>No need to upgrade</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">119</context>
        </context-group>
        <target state="needs-translation">No need to upgrade</target>
      </trans-unit>
      <trans-unit id="SuccessfulDowngrade" datatype="html">
        <source>Successful downgrade!</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">120</context>
        </context-group>
        <target state="needs-translation">Successful downgrade!</target>
      </trans-unit>
      <trans-unit id="SuccessfulUpgrade" datatype="html">
        <source>Successful upgrade!</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">121</context>
        </context-group>
        <target state="needs-translation">Successful upgrade!</target>
      </trans-unit>
      <trans-unit id="InvoiceCreated" datatype="html">
        <source>The invoice has been created successfully, and it is being redirected to the invoice
          details page...</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">122</context>
        </context-group>
        <target state="needs-translation">The invoice has been created successfully, and it is being
          redirected to the invoice details page...</target>
      </trans-unit>
      <trans-unit id="ExistUnpaidInvoices" datatype="html">
        <source>You have unpaid bills, please update after payment!</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">123</context>
        </context-group>
        <target state="needs-translation">You have unpaid bills, please update after payment!</target>
      </trans-unit>
      <trans-unit id="UpdateCycleError" datatype="html">
        <source>Billing cycle update failed</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">124</context>
        </context-group>
        <target state="needs-translation">Billing cycle update failed</target>
      </trans-unit>
      <trans-unit id="UpdateCycleSuccess" datatype="html">
        <source>Billing cycle update successfully</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">125</context>
        </context-group>
        <target state="needs-translation">Billing cycle update successfully</target>
      </trans-unit>
      <trans-unit id="BilledMonthly" datatype="html">
        <source>Billed Monthly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">126</context>
        </context-group>
        <target state="needs-translation">Billed Monthly</target>
      </trans-unit>
      <trans-unit id="BilledQuarterly" datatype="html">
        <source>Billed Quarterly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">127</context>
        </context-group>
        <target state="needs-translation">Billed Quarterly</target>
      </trans-unit>
      <trans-unit id="BilledSemiAnnually" datatype="html">
        <source>Billed Semi-Annually</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">128</context>
        </context-group>
        <target state="needs-translation">Billed Semi-Annually</target>
      </trans-unit>
      <trans-unit id="BilledAnnually" datatype="html">
        <source>Billed Annually</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">129</context>
        </context-group>
        <target state="needs-translation">Billed Annually</target>
      </trans-unit>
      <trans-unit id="BilledBiennially" datatype="html">
        <source>Billed Biennially</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">130</context>
        </context-group>
        <target state="needs-translation">Billed Biennially</target>
      </trans-unit>
      <trans-unit id="Processing" datatype="html">
        <source>Processing, please wait...</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">131</context>
        </context-group>
        <target state="needs-translation">Processing, please wait...</target>
      </trans-unit>
      <trans-unit id="NoServiceTip" datatype="html">
        <source>You don't have the service yet, you can change the plan after purchasing</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">132</context>
        </context-group>
        <target state="needs-translation">You don't have the service yet, you can change the plan
          after purchasing</target>
      </trans-unit>
      <trans-unit id="TurnOnVpn" datatype="html">
        <source>VPN is enabled</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">133</context>
        </context-group>
        <target state="needs-translation">VPN is enabled</target>
      </trans-unit>
      <trans-unit id="TurnOffVpn" datatype="html">
        <source>VPN is off</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">134</context>
        </context-group>
        <target state="needs-translation">VPN is off</target>
      </trans-unit>
      <trans-unit id="SwitchTo" datatype="html">
        <source>Switch to</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">135</context>
        </context-group>
        <target state="needs-translation">Switch to</target>
      </trans-unit>
      <trans-unit id="TokenExpired" datatype="html">
        <source>Session expired, please login again</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">136</context>
        </context-group>
        <target state="needs-translation">Session expired, please login again</target>
      </trans-unit>
      <trans-unit id="InvoiceExpired" datatype="html">
        <source>Your Invoice has expired and has been regenerated for you, please pay as soon as
          possible</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/utils/misc-i18n/misc-i18n.component.html</context>
          <context context-type="linenumber">137</context>
        </context-group>
        <target state="needs-translation">Your Invoice has expired and has been regenerated for you,
          please pay as soon as possible</target>
      </trans-unit>
      <trans-unit id="39f2c136eeeec8c2bef5a29095ff19554a591b65" datatype="html">
        <source>Flash Points</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">131</context>
        </context-group>
        <target state="translated">ポイント</target>
      </trans-unit>
      <trans-unit id="3708577be44b199f465deebefb3524e2d1708a42" datatype="html">
        <source>You can redeem your Flash Points for data, or send and receive among friends</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">146</context>
        </context-group>
        <target state="translated">ポイントをデータと交換したり、友達と送受信したりすることができます</target>
      </trans-unit>
      <trans-unit id="880e1e6270e3c0632f88a2e3a926f0574f1aeab1" datatype="html">
        <source>Receive</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">180</context>
        </context-group>
        <target state="translated">受け取る</target>
      </trans-unit>
      <trans-unit id="01f5cd69c0a07df5b98356d3f0041d107c8d49f0" datatype="html">
        <source>GET MORE POINTS</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">184</context>
        </context-group>
        <target state="translated">もっとポイントを手に入れましょう</target>
      </trans-unit>
      <trans-unit id="bfb4e64d4aeef83bd95aa902367a82644a894859" datatype="html">
        <source>Invite friends and earn point</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">196</context>
        </context-group>
        <target state="translated">友達にシェアすることでポイントをゲットしましょう</target>
      </trans-unit>
      <trans-unit id="7937678e0aae85f423f258a2f454f84e52565f91" datatype="html">
        <source>You&apos;ll receive <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;" /><x
            id="INTERPOLATION" equiv-text="{{inviteCampaign?.points}}" /> Pts<x id="CLOSE_TAG_SPAN"
            ctype="x-span" equiv-text="&lt;/span&gt;" /> when the person you invite signs up.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">198</context>
        </context-group>
        <target state="translated">招待した人がサインアップすると、 <x id="START_TAG_SPAN" ctype="x-span"
            equiv-text="&lt;span&gt;" /><x id="INTERPOLATION"
            equiv-text="{{inviteCampaign?.points}}" /> Pts<x id="CLOSE_TAG_SPAN" ctype="x-span"
            equiv-text="&lt;/span&gt;" /> を受け取ります。</target>
      </trans-unit>
      <trans-unit id="eeb8d30f70ff115b06f4a86ac6269a1cf78a99d0" datatype="html">
        <source>And they'll also get <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;" /><x
            id="INTERPOLATION" equiv-text="{{inviteCampaign?.points}}" /> Pts<x id="CLOSE_TAG_SPAN"
            ctype="x-span" equiv-text="&lt;/span&gt;" /> as well.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">199</context>
        </context-group>
        <target state="translated">友達も<x id="START_TAG_SPAN" ctype="x-span"
            equiv-text="&lt;span&gt;" /><x id="INTERPOLATION"
            equiv-text="{{inviteCampaign?.points}}" />Pts<x id="CLOSE_TAG_SPAN" ctype="x-span"
            equiv-text="&lt;/span&gt;" />を取得します。</target>
      </trans-unit>
      <trans-unit id="033909da098565aeefebbaef17686e4aad6c2621" datatype="html">
        <source>Copy Link and Share</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/rewards/info/info.component.html</context>
          <context context-type="linenumber">200</context>
        </context-group>
        <target state="translated">リンクをコピーして共有</target>
      </trans-unit>
      <trans-unit id="2f933b826a570836cab04f683970a2d22068458c" datatype="html">
        <source>Date</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/rewards/transactions/transactions.component.html</context>
          <context context-type="linenumber">27</context>
        </context-group>
        <target state="translated">データ</target>
      </trans-unit>
      <trans-unit id="dfc2fb58e2a04ed944a4bd80f0a2087775134068" datatype="html">
        <source>Amount</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/rewards/transactions/transactions.component.html</context>
          <context context-type="linenumber">33</context>
        </context-group>
        <target state="translated">金額</target>
      </trans-unit>
      <trans-unit id="56b6659f48e01e94d1ed4058c39eff10a90e9e1d" datatype="html">
        <source>Context</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/rewards/transactions/transactions.component.html</context>
          <context context-type="linenumber">39</context>
        </context-group>
        <target state="translated">コンテキスト</target>
      </trans-unit>
      <trans-unit id="798f7720c371eda6d7931bc590b771a02e2881bb" datatype="html">
        <source>Transfer</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/rewards/transactions/transactions.component.html</context>
          <context context-type="linenumber">42</context>
        </context-group>
        <target state="translated">トランスファー</target>
      </trans-unit>
      <trans-unit id="49dfd94a29f48a59933b79c56231ea9a1cf198dc" datatype="html">
        <source>Thank you for your payment.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/payment-success/payment-success.component.html</context>
          <context context-type="linenumber">5</context>
        </context-group>
        <target state="translated">お支払をありがとうございます</target>
      </trans-unit>
      <trans-unit id="092d497a31b267f903eb3df5f507ad207cd9ee98" datatype="html">
        <source>Out of <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;" /><x
            id="INTERPOLATION" equiv-text="{{service?.usageCapInGB()}}" />GB <x id="CLOSE_TAG_SPAN"
            ctype="x-span" equiv-text="&lt;/span&gt;" />monthly data, you have used <x
            id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;" /><x id="INTERPOLATION_1"
            equiv-text="{{service?.used()}}" />%<x id="CLOSE_TAG_SPAN" ctype="x-span"
            equiv-text="&lt;/span&gt;" /></source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/billing-index/billing-index.component.html</context>
          <context context-type="linenumber">141</context>
        </context-group>
        <target state="needs-translation">Out of <x id="START_TAG_SPAN" ctype="x-span"
            equiv-text="&lt;span&gt;" /><x id="INTERPOLATION"
            equiv-text="{{service?.usageCapInGB()}}" />GB <x id="CLOSE_TAG_SPAN" ctype="x-span"
            equiv-text="&lt;/span&gt;" />monthly data, you have used <x id="START_TAG_SPAN"
            ctype="x-span" equiv-text="&lt;span&gt;" /><x id="INTERPOLATION_1"
            equiv-text="{{service?.used()}}" />%<x id="CLOSE_TAG_SPAN" ctype="x-span"
            equiv-text="&lt;/span&gt;" /></target>
      </trans-unit>
      <trans-unit id="7948d4659b1ba732333541088056481f32f82e76" datatype="html">
        <source>It expires in <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;" /><x
            id="INTERPOLATION" equiv-text="{{service?.daysTillDue()?.toFixed(0)}}" /> <x
            id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;" /> days</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/billing-index/billing-index.component.html</context>
          <context context-type="linenumber">144</context>
        </context-group>
        <target state="needs-translation">It expires in <x id="START_TAG_SPAN" ctype="x-span"
            equiv-text="&lt;span&gt;" /><x id="INTERPOLATION"
            equiv-text="{{service?.daysTillDue()?.toFixed(0)}}" /> <x id="CLOSE_TAG_SPAN"
            ctype="x-span" equiv-text="&lt;/span&gt;" /> days</target>
      </trans-unit>
      <trans-unit id="9e8a4f13bc2200d7e106fc9b8b6d0fb1950f3ba0" datatype="html">
        <source>Renew Here</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/billing-index/billing-index.component.html</context>
          <context context-type="linenumber">146</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/plans/plans.component.html</context>
          <context context-type="linenumber">123</context>
        </context-group>
        <target state="translated">支払う</target>
      </trans-unit>
      <trans-unit id="47bbc861efa59ba4135e6aa8f63213420e3f3b91" datatype="html">
        <source>Subscription</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/billing-index/billing-index.component.html</context>
          <context context-type="linenumber">151</context>
        </context-group>
        <target state="needs-translation">Subscription</target>
      </trans-unit>
      <trans-unit id="f147d0f7f965cccee2e77294cba8e1b88021fa08" datatype="html">
        <source>Upgrade</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/billing-index/billing-index.component.html</context>
          <context context-type="linenumber">153</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/plans/plans.component.html</context>
          <context context-type="linenumber">140</context>
        </context-group>
        <target state="needs-translation">Upgrade</target>
      </trans-unit>
      <trans-unit id="3ac27e20756ddb5b3ed0f1d59a264e7cd625a606" datatype="html">
        <source>Billing Interval</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/billing-index/billing-index.component.html</context>
          <context context-type="linenumber">156</context>
        </context-group>
        <target state="needs-translation">Billing Interval</target>
      </trans-unit>
      <trans-unit id="047f50bc5b5d17b5bec0196355953e1a5c590ddb" datatype="html">
        <source>Update</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/billing-index/billing-index.component.html</context>
          <context context-type="linenumber">158</context>
        </context-group>
        <target state="needs-translation">Update</target>
      </trans-unit>
      <trans-unit id="cd6bccbcb7b198b4023ae867f6d5a633912ec791" datatype="html">
        <source>
          Invoices
        </source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/billing-index/billing-index.component.html</context>
          <context context-type="linenumber">174</context>
        </context-group>
        <target state="needs-translation">
          Invoices
        </target>
      </trans-unit>
      <trans-unit id="6829218544e108e152f5fa72cb79c4ccb82e0d06" datatype="html">
        <source>View</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/billings/billing-index/billing-index.component.html</context>
          <context context-type="linenumber">182</context>
        </context-group>
        <target state="needs-translation">View</target>
      </trans-unit>
      <trans-unit id="29881a45dafbe5aa05cd9d0441a4c0c2fb06df92" datatype="html">
        <source>Account</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/settings/setting-base/setting-base.component.html</context>
          <context context-type="linenumber">14</context>
        </context-group>
        <target state="translated">アカウント</target>
      </trans-unit>
      <trans-unit id="5522ea711b2d9e14a90ea13b3732891a98895de7" datatype="html">
        <source>Manage your account settings</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/settings/setting-base/setting-base.component.html</context>
          <context context-type="linenumber">15</context>
        </context-group>
        <target state="translated">アカウント設定の管理</target>
      </trans-unit>
      <trans-unit id="3d53f64033c4b76fdc1076ba15955d913209866c" datatype="html">
        <source>General</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/settings/setting-base/setting-base.component.html</context>
          <context context-type="linenumber">21</context>
        </context-group>
        <target state="translated">一般</target>
      </trans-unit>
      <trans-unit id="873eb643ab328902b4b092f352f51d8e73616ab2" datatype="html">
        <source>Manage general setting of app</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/settings/setting-base/setting-base.component.html</context>
          <context context-type="linenumber">22</context>
        </context-group>
        <target state="translated">アプリの一般設定を管理します</target>
      </trans-unit>
      <trans-unit id="be7b955d49865244d6b686e819a4bbcc44011dbb" datatype="html">
        <source>Check Updates</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/settings/setting-base/setting-base.component.html</context>
          <context context-type="linenumber">24</context>
        </context-group>
        <target state="translated">更新の確認</target>
      </trans-unit>
      <trans-unit id="004b222ff9ef9dd4771b777950ca1d0e4cd4348a" datatype="html">
        <source>About</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/settings/setting-base/setting-base.component.html</context>
          <context context-type="linenumber">30</context>
        </context-group>
        <target state="translated">概要</target>
      </trans-unit>
      <trans-unit id="aa861a51877fc6a1586936ebd407aa47a272008e" datatype="html">
        <source>FlashVPN is published by Flash Network &amp; Cloud Technology Limited. We aim to
          provide the best networking facility tools to help customers to reach their desired
          content.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/settings/setting-base/setting-base.component.html</context>
          <context context-type="linenumber">31</context>
        </context-group>
        <target state="translated">FlashVPN は Flash Network &amp; Cloud Technology
          Limitedによって公開されています。 お客様がご希望のコンテンツに到達できるよう、最高のネットワーキング機能ツールを提供することを目指しています。</target>
      </trans-unit>
      <trans-unit id="1862a1fc4057202277a0c76c00fb9b81bbfa1b92" datatype="html">
        <source>Out of <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;" /><x
            id="INTERPOLATION" equiv-text="{{service.usageCapInGB()}}" />GB <x id="CLOSE_TAG_SPAN"
            ctype="x-span" equiv-text="&lt;/span&gt;" />monthly data, you have used <x
            id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;" /><x id="INTERPOLATION_1"
            equiv-text="{{service.used()}}" />%<x id="CLOSE_TAG_SPAN" ctype="x-span"
            equiv-text="&lt;/span&gt;" /></source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/plans/plans.component.html</context>
          <context context-type="linenumber">118</context>
        </context-group>
        <target state="needs-translation">Out of <x id="START_TAG_SPAN" ctype="x-span"
            equiv-text="&lt;span&gt;" /><x id="INTERPOLATION"
            equiv-text="{{service.usageCapInGB()}}" />GB <x id="CLOSE_TAG_SPAN" ctype="x-span"
            equiv-text="&lt;/span&gt;" />monthly data, you have used <x id="START_TAG_SPAN"
            ctype="x-span" equiv-text="&lt;span&gt;" /><x id="INTERPOLATION_1"
            equiv-text="{{service.used()}}" />%<x id="CLOSE_TAG_SPAN" ctype="x-span"
            equiv-text="&lt;/span&gt;" /></target>
      </trans-unit>
      <trans-unit id="a765763860d4b98b3406d63d559b92d5617ef5e9" datatype="html">
        <source>It expires in <x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;" /><x
            id="INTERPOLATION" equiv-text="{{service.daysTillDue().toFixed(0)}}" /> <x
            id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;" /> days</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/plans/plans.component.html</context>
          <context context-type="linenumber">121</context>
        </context-group>
        <target state="needs-translation">It expires in <x id="START_TAG_SPAN" ctype="x-span"
            equiv-text="&lt;span&gt;" /><x id="INTERPOLATION"
            equiv-text="{{service.daysTillDue().toFixed(0)}}" /> <x id="CLOSE_TAG_SPAN"
            ctype="x-span" equiv-text="&lt;/span&gt;" /> days</target>
      </trans-unit>
      <trans-unit id="3877b2d0a2e49783819fbb66d9499d2e7077b398" datatype="html">
        <source>Current Plan</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/plans/plans.component.html</context>
          <context context-type="linenumber">139</context>
        </context-group>
        <target state="needs-translation">Current Plan</target>
      </trans-unit>
      <trans-unit id="302ecc4d1a8882ccc8a4ec9404d6ba57a3bdef74" datatype="html">
        <source>Downgrade</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/plans/plans.component.html</context>
          <context context-type="linenumber">141</context>
        </context-group>
        <target state="needs-translation">Downgrade</target>
      </trans-unit>
    </body>
  </file>
</xliff>