<style>

  .form-input{
    width: 100%;
  }
  h2 {
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: 600;
    font-size: 28px;
    line-height: 34px;
    margin-bottom: 12px;
    /* identical to box height */


    color: #000000;


    /* Inside auto layout */

    flex: none;
    order: 0;
    flex-grow: 0;
  }
  h4 {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    margin: 0;
    /* or 150% */


    color: #505050;
  }

  :host ::ng-deep .mat-form-field-underline {
    display: none!important;
  }
  .form-input {
    width: 420px;
    height: 56px;
  }
  @media screen and (max-width: 599px){
    .form-input {
      width: 100%;
    }
  }
  :host ::ng-deep .mat-form-field-wrapper {
    padding-bottom: 0;
    height: 100%;
  }
  :host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
    padding: 0 12px;
    height: 100%;
    align-items: center;
  }
  :host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-infix {
    padding: 0;
    margin: 0;
    border: 0;
    display: flex;
    align-items: center;
  }
  .action-button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 10px;
    gap: 10px;

    padding: 10px 16px;

    background: #FF5E5E;
    border-radius: 2px;
    color: #fff;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;

    /* Inside auto layout */

    flex: none;
    order: 3;
    flex-grow: 0;
    margin: 0 auto;
    display: inline-block;
  }
  h6 {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    /* or 150% */
    color: #000000;
    margin: 0;
  }
  .link {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    color: #FF5E5E;
    text-decoration: underline;
  }
  .box{
    width: 420px;
  }
  .wait{
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    /* or 150% */
    color: #000000;
    margin: 0;
  }
  @media screen and (max-width: 599px){
    .box {
      width: 100%;
    }
  }
</style>

<div class="box">
  <h2 i18n>Verify Email</h2>
  <h4 i18n>We have send a confirmation code to {{email}}</h4>
  <h4 i18n style="margin-bottom: 32px;">Please input here to verify your account</h4>
  <form #form="ngForm" >
    <mat-form-field class="form-input" floatLabel="never" appearance="fill" empty="false" autofilled="false">
      <svg MatPrefix style="margin-right: 20px;" width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M20 2C20 0.9 19.1 0 18 0H2C0.9 0 0 0.9 0 2V14C0 15.1 0.9 16 2 16H18C19.1 16 20 15.1 20 14V2ZM18 2L10 7L2 2H18ZM10 9L2 4V14H18V4L10 9Z" fill="black" fill-opacity="0.54"/>
      </svg>
      <input (keyup.enter)="onSubmit()" matInput i18n-placeholder placeholder="Please enter your confirmation code"  required  [(ngModel)]="code" name="code">
    </mat-form-field>
    <a id="signup-verify-code-submit" href="javascript: void(0)" *ngIf="!submitted" class="action-button" style="margin-top: 32px;margin-bottom: 54px;" (click)="onSubmit()" i18n>Verify Account</a>
    <div *ngIf="submitted" style="margin: 30px 0">
      <app-load-botton></app-load-botton>
    </div>
    <!-- <app-divider></app-divider> -->
    <h6 i18n>You have not received the confirmation email or</h6>
    <div class="wait" *ngIf="time>0">
      <span i18n> Please wait </span>
      <span style="color: #FF5E5E;">{{time}}</span>
      <span i18n> seconds and try again</span>
    </div>
    <a id="signup-verify-resent-code" *ngIf="time===0&&!eSubmitted" href="javascript: void(0)" class="link" (click)="resend()" i18n>Resent Code</a>
    <span *ngIf="eSubmitted" class="link">邮件发送中，请稍等...</span>
    <!-- <mat-progress-bar *ngIf="submitted" mode="indeterminate"></mat-progress-bar> -->
  </form>
</div>
