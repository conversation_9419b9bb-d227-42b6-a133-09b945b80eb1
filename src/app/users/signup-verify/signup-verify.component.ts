import { Component, OnInit, ViewChild } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { ActivatedRoute, Router } from "@angular/router";
import { APIManager } from "@flashvpn-io/web-core";
import { NotificationService, USER_EMAIL_VERIFIED } from "@flashvpn-io/web-core";
import { UsersService } from "@flashvpn-io/web-core";
import { MatSnackBar } from "@angular/material/snack-bar";
import { LoadBottonComponent } from "../../utils/load-botton/load-botton.component";

@Component({
  selector: "app-signup-verify",
  templateUrl: "./signup-verify.component.html",
  styleUrls: ["./signup-verify.component.css"],
})
export class SignupVerifyComponent implements OnInit {
  @ViewChild("loadBottonComponent") loadBottonComponent: LoadBottonComponent;
  code: any;
  email: string;
  submitted: any;
  time = 0;
  eSubmitted = false;
  constructor(
    private http: HttpClient,
    private router: Router,
    private route: ActivatedRoute,
    private notificationService: NotificationService,
    private usersService: UsersService,
    private apiManager: APIManager,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit() {
    this.route.queryParamMap.subscribe(async (params) => {
      this.email = params.get("email") || this.usersService?.user?.email;
    });
  }

  onSubmit() {
    this.submitted = true;
    const email = this.email || this.usersService?.user?.email;
    this.http.get(`${this.apiManager.serverUrl}/api/v1/users/verify/${this.code}/${email}`).subscribe(
      async (data) => {
        await this.usersService.login(data["token"], email);
        this.notificationService.post(USER_EMAIL_VERIFIED, true);
        await this.router.navigate(["center"]);
      },
      (res) => {
        this.submitted = false;
        this.snackBar.open(res?.error?.message, "Okay", {
          duration: 2000,
          verticalPosition: "top",
        });
      }
    );
  }

  resend() {
    if (this.eSubmitted) {
      return;
    }
    this.eSubmitted = true;
    this.apiManager.resendEmail().subscribe(
      (_) => {
        this.snackBar.open("我们已经重新发送了确认邮件 ", "Okay", {
          duration: 2000,
          verticalPosition: "top",
        });
        this.time = 60;
        const timer = setInterval(() => {
          if (this.time > 0) {
            this.time--;
          } else {
            clearInterval(timer);
          }
        }, 1000);
      },
      (res) => {
        this.snackBar.open(res?.error?.message, "Okay", {
          duration: 2000,
          verticalPosition: "top",
        });
      },
      () => (this.eSubmitted = false)
    );
  }
}
