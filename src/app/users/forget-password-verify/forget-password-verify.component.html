<style>

  .h2{
    font-family: 'Li<PERSON> Franklin';
    font-style: normal;
    font-weight: 600;
    font-size: 28px;
    line-height: 34px;
    /* identical to box height */
    text-transform: capitalize;
    color: #000000;
  }
  .h4{
    width: 420px;
    height: 48px;
    font-family: '<PERSON>o';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    /* or 150% */
    color: #505050;
    /* Inside auto layout */
    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;
  }
  @media screen and (max-width: 599px){
    .h4 {
      width: 100%;
    }
  }
  :host ::ng-deep .mat-form-field-underline {
    display: none!important;
  }
  .form-input {
    width: 420px;
    height: 56px;
    margin:0px 0px 32px 0px;
  }
  @media screen and (max-width: 599px){
    .form-input {
      width: 100%;
      height: 56px;
    }
  }
  :host ::ng-deep .mat-form-field-wrapper {
    padding-bottom: 0;
    height: 100%;
  }
  :host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
    padding: 0 12px;
    height: 100%;
    align-items: center;
  }
  :host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-infix {
    padding: 0;
    margin: 0;
    border: 0;
    display: flex;
    align-items: center;
  }
  .action-button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 10px 48.5px;

    background: #FF5E5E;
    border-radius: 2px;
    color: #fff;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;

    /* Inside auto layout */

    flex: none;
    order: 2;
    flex-grow: 0;
  }
  .action-button-disabled{
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 10px 48.5px;

    background: #999999;
    border-radius: 2px;
    color: #fff;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;

    /* Inside auto layout */

    flex: none;
    order: 2;
    flex-grow: 0;
  }
  .input_tips{
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0px 10px;
    gap: 9px;
    width: 420px;
    height: 38px;
    background: rgba(58, 144, 246, 0.2);
    border: 1px solid #3A73F6;
    border-radius: 2px;
    flex: none;
    order: 0;
    flex-grow: 0;
    color: #3A73F6;
  }
  @media screen and (max-width: 599px){
    .input_tips {
      width: 100%;
    }
  }
  .input_tips .mat-icon{
    width: 18px;
    height: 18px;
  }
  .input_tips .material-icons{
    font-size: 18px;
  }
  .fade-in-fwd {
    -webkit-animation: fade-in-fwd 0.3s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
    animation: fade-in-fwd 0.3s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
  }
  @-webkit-keyframes fade-in-fwd {
    0% {
      -webkit-transform: translateZ(-80px);
      transform: translateZ(-80px);
      opacity: 0;
    }
    100% {
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
      opacity: 1;
    }
  }
  @keyframes fade-in-fwd {
    0% {
      -webkit-transform: translateZ(-80px);
      transform: translateZ(-80px);
      opacity: 0;
    }
    100% {
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
      opacity: 1;
    }
  }
  .fade-out-bck {
    -webkit-animation: fade-out-bck 0.3s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
    animation: fade-out-bck 0.3s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
  }
  @-webkit-keyframes fade-out-bck {
    0% {
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
      opacity: 1;
    }
    100% {
      -webkit-transform: translateZ(-80px);
      transform: translateZ(-80px);
      opacity: 0;
    }
  }
  @keyframes fade-out-bck {
    0% {
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
      opacity: 1;
    }
    100% {
      -webkit-transform: translateZ(-80px);
      transform: translateZ(-80px);
      opacity: 0;
    }
  }
  .slide-bottom {
    -webkit-animation: slide-bottom 0.6s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
    animation: slide-bottom 0.6s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
  }
  @-webkit-keyframes slide-bottom {
    0% {
      -webkit-transform: translateY(0);
      transform: translateY(0);
    }
    100% {
      -webkit-transform: translateY(18px);
      transform: translateY(18px);
    }
  }
  @keyframes slide-bottom {
    0% {
      -webkit-transform: translateY(0);
      transform: translateY(0);
    }
    100% {
      -webkit-transform: translateY(18px);
      transform: translateY(18px);
    }
  }
  .slide-top {
    -webkit-animation: slide-top 0.6s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
    animation: slide-top 0.6s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
  }
  @-webkit-keyframes slide-top {
    0% {
      -webkit-transform: translateY(0);
      transform: translateY(0);
    }
    100% {
      -webkit-transform: translateY(-48px);
      transform: translateY(-48px);
    }
  }
  @keyframes slide-top {
    0% {
      -webkit-transform: translateY(0);
      transform: translateY(0);
    }
    100% {
      -webkit-transform: translateY(-48px);
      transform: translateY(-48px);
    }
  }
  .none_tips{
    display: none;
  }
</style>

<div>
  <h2 class="h2" i18n>Set New Password</h2>
  <h4 class="h4" i18n>We have send a confirmation code to your email, please input here to reset your password</h4>
  <div id="tipsInfo" class="none_tips"><mat-icon>error</mat-icon>{{tipsInfo}}</div>
  <form #form="ngForm" id="forgetPasswordForm">
    <!-- <mat-form-field class="form-input">
      <input matInput i18n-placeholder placeholder="Input verification code"  required  [(ngModel)]="code" name="code">
    </mat-form-field> -->
    <mat-form-field class="form-input" floatLabel="never" appearance="fill" empty="false" autofilled="false">
      <svg MatPrefix style="margin-right: 15px;" width="20" height="19" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M4 21H21C22.95 21 24.5 19.45 24.5 17.5V6.5C24.5 4.55 22.95 3 21 3H4C2.05 3 0.5 4.55 0.5 6.5V17.5C0.5 19.45 2.05 21 4 21ZM2.5 6.5C2.5 5.65 3.15 5 4 5H21C21.85 5 22.5 5.65 22.5 6.5V17.5C22.5 18.35 21.85 19 21 19H4C3.15 19 2.5 18.35 2.5 17.5V6.5ZM6.60019 14H6.85019C7.90019 14 8.7502 13.15 8.7502 12.1V11.85C8.7502 10.8 7.90019 9.94995 6.85019 9.94995H6.60019C5.5502 9.94995 4.7002 10.8 4.7002 11.85V12.1C4.7002 13.15 5.5502 14 6.60019 14ZM10.5002 12C10.5002 13.1 11.4002 14 12.5002 14C13.6002 14 14.5002 13.1 14.5002 12C14.5002 10.9 13.6002 9.99995 12.5002 9.99995C11.4002 9.99995 10.5002 10.9 10.5002 12ZM16.2502 12C16.2502 13.1 17.1502 14 18.2502 14C19.3502 14 20.2502 13.1 20.2502 12C20.2502 10.9 19.3502 9.99995 18.2502 9.99995C17.1502 9.99995 16.2502 10.9 16.2502 12Z" fill="#696969"/>
      </svg>
      <input matInput i18n-placeholder placeholder="Please enter verification code"  [(ngModel)]="code" name="code" >
    </mat-form-field>
    <!-- <mat-form-field class="form-input">
      <input matInput i18n-placeholder placeholder="Enter your password" [type]="hide ? 'password' : 'text'" required  [(ngModel)]="password" name="password">
      <a mat-icon-button matSuffix (click)="hide = !hide" [attr.aria-label]="'Hide password'" [attr.aria-pressed]="hide">
        <mat-icon>{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
      </a>
    </mat-form-field> -->
    <mat-form-field class="form-input" floatLabel="never" appearance="fill" empty="false" autofilled="false">
      <svg MatPrefix style="margin-right: 20px;"  width="16" height="22" viewBox="0 0 16 22" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M13 7.5H14C15.1 7.5 16 8.4 16 9.5V19.5C16 20.6 15.1 21.5 14 21.5H2C0.9 21.5 0 20.6 0 19.5V9.5C0 8.4 0.9 7.5 2 7.5H3V5.5C3 2.74 5.24 0.5 8 0.5C10.76 0.5 13 2.74 13 5.5V7.5ZM8 2.5C6.34 2.5 5 3.84 5 5.5V7.5H11V5.5C11 3.84 9.66 2.5 8 2.5ZM2 19.5V9.5H14V19.5H2ZM10 14.5C10 15.6 9.1 16.5 8 16.5C6.9 16.5 6 15.6 6 14.5C6 13.4 6.9 12.5 8 12.5C9.1 12.5 10 13.4 10 14.5Z" fill="black" fill-opacity="0.54"/>
      </svg>
      <input id="passwordInput1" (input)="passwordChange(1)" (blur)="passwordGetFocus(1)" matInput i18n-placeholder placeholder="Please enter your password" [type]="hide ? 'password' : 'text'" [(ngModel)]="password" name="password">
      <a id="forget-password-hide-password" mat-icon-button matSuffix style="margin-left: 20px;display: flex;align-items: center;justify-content: center;" (click)="hide = !hide" [attr.aria-label]="'Hide password'" [attr.aria-pressed]="hide">
        <mat-icon>{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
      </a>
    </mat-form-field>
    <!-- <mat-form-field class="form-input">
      <input matInput i18n-placeholder placeholder="Confirm your password" [type]="hide ? 'password' : 'text'" required  [(ngModel)]="confirmedPassword" name="password">
      <a mat-icon-button matSuffix (click)="hide = !hide" [attr.aria-label]="'Hide password'" [attr.aria-pressed]="hide">
        <mat-icon>{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
      </a>
    </mat-form-field> -->
    <mat-form-field class="form-input" floatLabel="never" appearance="fill" empty="false" autofilled="false">
      <svg MatPrefix style="margin-right: 20px;"  width="16" height="22" viewBox="0 0 16 22" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M13 7.5H14C15.1 7.5 16 8.4 16 9.5V19.5C16 20.6 15.1 21.5 14 21.5H2C0.9 21.5 0 20.6 0 19.5V9.5C0 8.4 0.9 7.5 2 7.5H3V5.5C3 2.74 5.24 0.5 8 0.5C10.76 0.5 13 2.74 13 5.5V7.5ZM8 2.5C6.34 2.5 5 3.84 5 5.5V7.5H11V5.5C11 3.84 9.66 2.5 8 2.5ZM2 19.5V9.5H14V19.5H2ZM10 14.5C10 15.6 9.1 16.5 8 16.5C6.9 16.5 6 15.6 6 14.5C6 13.4 6.9 12.5 8 12.5C9.1 12.5 10 13.4 10 14.5Z" fill="black" fill-opacity="0.54"/>
      </svg>
      <input id="passwordInput2" (input)="passwordChange(2)" (blur)="passwordGetFocus(2)" matInput i18n-placeholder placeholder="Enter your new password again" [type]="hide ? 'password' : 'text'" [(ngModel)]="confirmedPassword" name="password">
      <a id="forget-password-hide-password-confirm" mat-icon-button matSuffix style="margin-left: 20px;display: flex;align-items: center;justify-content: center;" (click)="hide = !hide" [attr.aria-label]="'Hide password'" [attr.aria-pressed]="hide">
        <mat-icon>{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
      </a>
    </mat-form-field>
<!--    <div fxLayout="row" >-->
<!--      <a [class]="code&&password&&confirmedPassword?'action-button':'action-button-disabled'" (click)="onSubmit()" i18n>Reset</a>-->
<!--      &lt;!&ndash; <a style="text-decoration-line: underline;" class="h3Size" routerLink="/users/signin" i18n>Sign In</a> &ndash;&gt;-->
<!--    </div>-->
    <div class="botton_box">
      <button d="forget-password-verify-reset-loading" *ngIf="submitted" mat-fab color="warn" class="bounce-in-fwd" >
        <mat-spinner diameter="30"></mat-spinner>
      </button>
      <button id="forget-password-verify-reset" style="padding: 0 60px;" *ngIf="!submitted" mat-raised-button disabled="{{!(code&&password&&confirmedPassword)}}" color="warn" (click)="onSubmit()" i18n>
        Reset
      </button>
    </div>
    <!-- <h3 class="action-button" (click)="onSubmit()" i18n>Reset</h3>
    <mat-progress-bar *ngIf="submitted" mode="indeterminate"></mat-progress-bar> -->
  </form>
</div>
