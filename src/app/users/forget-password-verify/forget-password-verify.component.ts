import { Component, OnInit } from "@angular/core";
import { environment } from "../../../environments/environment";
import { HttpClient } from "@angular/common/http";
import { Router } from "@angular/router";
import { AppService } from "src/app/app.service";

@Component({
  selector: "app-forget-password-verify",
  templateUrl: "./forget-password-verify.component.html",
  styleUrls: ["./forget-password-verify.component.css"],
})
export class ForgetPasswordVerifyComponent implements OnInit {
  code: any;
  submitted: any;
  hide: any;
  password: any;
  confirmedPassword: any;
  password1Error = false;
  password2Error = false;
  password1ErrorInfo: any;
  password2ErrorInfo: any;
  tipsInfo: any;

  constructor(private http: HttpClient, private router: Router, private appService: AppService) {}

  ngOnInit() {}

  checkPassword(index) {
    let password = null;
    if (index === 1) {
      password = this.password;
    } else if (index === 2) {
      password = this.confirmedPassword;
    }
    const errorInfo = {
      isError: false,
      errorTips: null,
    };
    const reg = /(^\s+)|(\s+$)|\s+|\++/g;
    const isReg = reg.test(password);
    const reg2 = /[\u4e00-\u9fa5]+/g;
    const isReg2 = reg2.test(password);
    if (isReg || isReg2) {
      errorInfo.isError = true;
      errorInfo.errorTips = this.appService.translate("PasswordIllegalCharacters");
      return errorInfo;
    }
    if (password && password.length > 20) {
      errorInfo.isError = true;
      errorInfo.errorTips = this.appService.translate("PasswordLongError");
      return errorInfo;
    }
    return errorInfo;
  }

  setPasswordError(error, tips, index) {
    if (index === 1) {
      if (error) {
        document.getElementById("passwordInput1").style.color = "#FF5E5E";
        this.password1ErrorInfo = tips;
        this.tipsInfo = tips;
        document.getElementById("tipsInfo").className = "input_tips fade-in-fwd";
        document.getElementById("forgetPasswordForm").className = "slide-bottom";
      } else if (this.password1Error && !error) {
        if (this.password2Error) {
          this.password1ErrorInfo = null;
          this.tipsInfo = this.password2ErrorInfo;
          document.getElementById("passwordInput1").style.color = "";
        } else {
          document.getElementById("tipsInfo").className = "input_tips fade-out-bck";
          document.getElementById("forgetPasswordForm").className = "slide-top";
          document.getElementById("passwordInput1").style.color = "";
          this.password1ErrorInfo = null;
          this.tipsInfo = null;
        }
      }
      this.password1Error = error;
    } else if (index === 2) {
      if (error) {
        document.getElementById("passwordInput2").style.color = "#FF5E5E";
        this.password2ErrorInfo = tips;
        this.tipsInfo = tips;
        document.getElementById("tipsInfo").className = "input_tips fade-in-fwd";
        document.getElementById("forgetPasswordForm").className = "slide-bottom";
      } else if (this.password2Error && !error) {
        if (this.password1Error) {
          this.password2ErrorInfo = null;
          this.tipsInfo = this.password1ErrorInfo;
          document.getElementById("passwordInput2").style.color = "";
        } else {
          document.getElementById("tipsInfo").className = "input_tips fade-out-bck";
          document.getElementById("forgetPasswordForm").className = "slide-top";
          document.getElementById("passwordInput2").style.color = "";
          this.password2ErrorInfo = null;
          this.tipsInfo = null;
        }
      }
      this.password2Error = error;
    }
  }

  passwordChange(index) {
    const errorInfo = this.checkPassword(index);
    this.setPasswordError(errorInfo.isError, errorInfo.errorTips, index);
  }

  passwordGetFocus(index) {
    const errorInfo = this.checkPassword(index);
    let password = null;
    if (index === 1) {
      password = this.password;
    } else if (index === 2) {
      password = this.confirmedPassword;
    }
    if (password && password.length < 8) {
      errorInfo.isError = true;
      errorInfo.errorTips = this.appService.translate("PasswordShortError");
    }
    this.setPasswordError(errorInfo.isError, errorInfo.errorTips, index);
  }

  onSubmit() {
    if (this.code && this.password && this.confirmedPassword && !this.password1Error && !this.password2Error) {
      this.submitted = true;
      const logInUrl =
        environment.apiUrl !== undefined
          ? environment.apiUrl + `/api/v1/users/forgetpassword/verify/${this.code}`
          : `/api/v1/users/forgetpassword/verify/${this.code}`;
      this.http.post<string>(logInUrl, { password: this.password, confirmedPassword: this.confirmedPassword }).subscribe(
        async (user) => {
          this.appService.snackUp("密码重置完成");
          await this.router.navigate(["users/signin"]);
        },
        (res) => {
          this.submitted = false;
          this.appService.snackUp(res?.error?.message);
        }
      );
    }
  }
}
