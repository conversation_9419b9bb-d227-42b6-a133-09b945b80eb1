import { AfterViewInit, Component, OnInit, ViewChild } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { ActivatedRoute, Router } from "@angular/router";
import { AppService } from "src/app/app.service";
import { MatSnackBar } from "@angular/material/snack-bar";
import { Title } from "@angular/platform-browser";
import { APIManager } from "@flashvpn-io/web-core";
import { UsersService } from "@flashvpn-io/web-core";
import { Signup } from "@flashvpn-io/web-core";
import { User } from "@flashvpn-io/web-core";
import { OauthType } from "@flashvpn-io/web-core";
import { OauthFormComponent } from "../../component/oauth-form.component";

const GOOGLE_OAUTH_URL = "/api/v1/users/oauth/google";

@Component({
  selector: "app-signup",
  templateUrl: "./signup.component.html",
  styleUrls: ["./signup.component.css"],
})
export class SignupComponent implements OnInit, AfterViewInit {
  @ViewChild("oauthFormComponent") oauthFormComponent: OauthFormComponent;

  constructor(
    private http: HttpClient,
    private router: Router,
    public appService: AppService,
    private apiManager: APIManager,
    private title: Title,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    private usersService: UsersService
  ) { }

  protected readonly OauthType = OauthType;

  submitted = false;

  inviterCode: string = null;

  ngOnInit() { }

  ngAfterViewInit() {
    this.route.queryParamMap.subscribe(async (params) => {
      if (params.get("email")) {
        this.oauthFormComponent.setEmail(null, params.get("email"));
      }
      this.inviterCode = params.get("inviterCode");
    });
  }

  emailcheck() {
    if (
      this.oauthFormComponent.email &&
      this.oauthFormComponent.password &&
      !this.oauthFormComponent.emailError &&
      !this.oauthFormComponent.passwordError
    ) {
      this.submitted = true;
      this.apiManager.emailcheck(this.oauthFormComponent.email).subscribe(
        (isOk) => {
          if (!isOk) {
            this.onSubmit();
          } else {
            this.snackBar.open(this.appService.translate("EmailExisted"), "Okay", {
              duration: 2000,
              verticalPosition: "top",
            });
            this.submitted = false;
          }
        },
        (res) => {
          this.submitted = false;
          this.snackBar.open(this.appService.translate("EmailcheckError"), "Okay", {
            duration: 2000,
            verticalPosition: "top",
          });
        }
      );
    }
  }

  async onSubmit() {
    // if (this.email && this.password && !this.emailError && !this.passwordError) {
    // if (this.submitted === true) {
    //   return ;
    // }
    // this.submitted = true;
    // if (this.email === undefined || this.password === undefined) {
    //   this.snackBar.open('EmailOrPasswordIsEmpty', 'Okay', {
    //     duration: 2000,
    //     verticalPosition: 'top'
    //   });
    //   this.submitted = false;
    //   return;
    // }
    const utmSource = this.route.snapshot.queryParams.utm_source || localStorage.getItem("utmSource") || undefined;
    const utmMedium = this.route.snapshot.queryParams.utm_medium || localStorage.getItem("utmMedium") || undefined;
    const utmCampaign = this.route.snapshot.queryParams.utm_campaign || localStorage.getItem("utmCampaign") || undefined;
    const utmTerm = this.route.snapshot.queryParams.utm_term || localStorage.getItem("utmTerm") || undefined;
    const utmContent = this.route.snapshot.queryParams.utm_content || localStorage.getItem("utmContent") || undefined;
    const registerInfo: Signup = {
      email: this.oauthFormComponent.email,
      password: this.oauthFormComponent.password,
      inviterCode: this.inviterCode || undefined,
      utmSource,
      utmMedium,
      utmCampaign,
      utmTerm,
      utmContent,
    };
    this.apiManager.createUser(registerInfo).subscribe(
      async (user) => {
        localStorage.setItem("refresh_token", (user as any)?.refreshToken);
        !this.appService.isExtension() && this.setChatwootDefaultUserInfo(user as User);
        if ((user as any).emailVerified === 1) {
          await this.usersService.loginByUser(user as User);
          // this.router.navigate(["services", "dashboard"]);
          this.router.navigate(["center"]);
        } else {
          this.router.navigate(["users", "signup-verify"], { queryParams: { email: this.oauthFormComponent.email } });
        }
      },
      (res) => {
        this.submitted = false;
        if (res.error.error === "EmailNotVerified") {
          this.snackBar.open("您尚未验证您的电子邮件", "Okay", {
            duration: 2000,
            verticalPosition: "top",
          });
          this.usersService.removeUtm();
          this.router.navigate(["users", "signup-verify"], { queryParams: { email: this.oauthFormComponent.email } });
        } else {
          this.snackBar.open(this.appService.translate(res.error.error), "Okay", {
            duration: 2000,
            verticalPosition: "top",
          });
        }
      }
    );
    // }
  }

  setChatwootDefaultUserInfo(user: User) {
    // @ts-ignore
    window.$chatwoot.setUser(user.id, {
      name: user.email, // Name of the user
      email: user.email, // Email of the user
      country_code: user.locale, // Two letters country code
    });
  }
}
