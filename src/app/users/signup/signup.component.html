<style>
  h4 {
    text-align: center;
    font-family: "Roboto";
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 19px;
    /* identical to box height */
    color: #505050;
  }

  .box {
    width: 420px;
  }

  @media screen and (max-width: 599px) {
    .box {
      width: 100%;
    }
  }
</style>

<div class="box">
  <app-user-tabs [activatedType]="OauthType.SIGNUP"></app-user-tabs>
  <app-oauth-form #oauthFormComponent [oAuthType]="OauthType.SIGNUP" (onSubmit)="onSubmit()"></app-oauth-form>
  <div class="botton-box">
    <button *ngIf="submitted" mat-fab color="warn" class="bounce-in-fwd">
      <mat-spinner diameter="30"></mat-spinner>
    </button>
    <button id="signup-create-account" *ngIf="!submitted"
      [class]="(oauthFormComponent.isDisabled$ | async) ? 'mat-btn mat-btn-disable' : 'mat-btn'"
      [disabled]="oauthFormComponent.isDisabled$ | async" color="warn" (click)="emailcheck()" i18n>
      创建账户
    </button>
  </div>
  <app-divider></app-divider>
  <app-oauth></app-oauth>
</div>