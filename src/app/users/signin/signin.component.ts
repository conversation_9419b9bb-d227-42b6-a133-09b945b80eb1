import { Component, OnInit, ViewChild } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { UsersService } from "@flashvpn-io/web-core";
import { AppService } from "src/app/app.service";
import { MatSnackBar } from "@angular/material/snack-bar";
import { APIManager } from "@flashvpn-io/web-core";
import { User } from "@flashvpn-io/web-core";
import { OauthType } from "@flashvpn-io/web-core";
import { OauthFormComponent } from "../../component/oauth-form.component";

@Component({
  selector: "app-signin",
  templateUrl: "./signin.component.html",
  styleUrls: ["./signin.component.css"],
})
export class SigninComponent implements OnInit {
  @ViewChild("oauthFormComponent") oauthFormComponent: OauthFormComponent;

  protected readonly OauthType = OauthType;

  submitted: any;
  redirect: string;
  queryParams: object;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private usersService: UsersService,
    private snackBar: MatSnackBar,
    public appService: AppService,
    private apiManager: APIManager
  ) { }

  async ngOnInit() {
    this.route.queryParams.subscribe((queryParams) => {
      const path = queryParams?.redirect || queryParams?.url;
      this.redirect = path;
      this.queryParams = {
        ...queryParams,
        redirect: undefined,
      };
      this.handleLoginRedirect(path).then();
    });
  }

  async handleLoginRedirect(redirect?: string) {
    if (await this.usersService.isLoggedIn()) {
      const path = redirect ?? "center";
      await this.router.navigate([path]);
    }
  }

  setChatwootDefaultUserInfo(user: User) {
    // @ts-ignore
    window.$chatwoot.setUser(user.id, {
      name: user.email, // Name of the user
      email: user.email, // Email of the user
      country_code: user.locale, // Two letters country code
    });
  }

  onSubmit() {
    if (this.oauthFormComponent.email && this.oauthFormComponent.password && !this.oauthFormComponent.emailError) {
      this.submitted = true;
      this.apiManager.login(this.oauthFormComponent.email, this.oauthFormComponent.password).subscribe(
        async (user) => {
          localStorage.setItem("refresh_token", (user as any)?.refreshToken);
          localStorage.setItem("token", (user as any)?.token);
          !this.appService.isExtension() && this.setChatwootDefaultUserInfo(user as User);
          await this.usersService.loginByUser(user as User);
          // only jump back to app when there is a service
          if (this.redirect) {
            const url = decodeURI(this.redirect);
            await this.router.navigate([url], {
              queryParams: this.queryParams,
            });
          } else {
            const apps = await this.appService.isFromApp();
            if (apps.length > 0) {
              await this.router.navigate(["users", "redirect"]);
            } else {
              await this.router.navigate(["center"]);
            }
          }
        },
        async (res) => {
          this.submitted = false;
          if (res.error.error === "CredentialIsWrong") {
            this.oauthFormComponent.setEmailError(true, this.appService.translate(res.error.error));
            this.oauthFormComponent.setPasswordError(true, this.appService.translate(res.error.error));
          } else if (res.error.error === "EmailNotVerified") {
            this.snackBar.open("电子邮件未验证, 请重新注册", "Okay", {
              duration: 2000,
              verticalPosition: "top",
            });
            this.usersService.removeUtm();
            await this.router.navigate(["users", "signup"]);
          } else {
            this.snackBar.open(this.appService.translate(res.error.error), "Okay", {
              duration: 2000,
              verticalPosition: "top",
            });
          }
        }
      );
    }
  }
}
