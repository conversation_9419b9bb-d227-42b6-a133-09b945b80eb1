<style>
  h4 {
    text-align: center;
    font-family: "Roboto";
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 19px;
    /* identical to box height */
    color: #505050;
  }

  .box {
    width: 420px;
  }

  @media screen and (max-width: 599px) {
    .box {
      width: 100%;
    }
  }
</style>

<div class="box">
  <app-user-tabs [activatedType]="OauthType.SIGNIN"></app-user-tabs>
  <app-oauth-form #oauthFormComponent [oAuthType]="OauthType.SIGNIN" (onSubmit)="onSubmit()"></app-oauth-form>
  <br />
  <div class="botton-box">
    <button id="sign-in-sign-in-button-loading" *ngIf="submitted" mat-fab color="warn" class="bounce-in-fwd">
      <mat-spinner diameter="30"></mat-spinner>
    </button>
    <button id="sign-in-sign-in-button" *ngIf="!submitted"
      [class]="(oauthFormComponent.isDisabled$ | async) ? 'mat-btn mat-btn-disable' : 'mat-btn'"
      [disabled]="oauthFormComponent.isDisabled$ | async" color="warn" (click)="onSubmit()" i18n>
      登录
    </button>
  </div>
  <app-divider></app-divider>
  <app-oauth></app-oauth>
</div>