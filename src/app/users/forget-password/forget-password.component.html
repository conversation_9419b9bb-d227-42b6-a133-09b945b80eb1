<style>
  .h2{
    font-family: <PERSON><PERSON>;
    font-size: 28px;
    font-weight: 600;
    line-height: 34px;
    letter-spacing: 0em;
    margin-bottom: 12px;
  }
  .h4{
    font-family: Roboto;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    letter-spacing: 0em;
    color: #505050;
    margin-bottom: 32px;
  }
  :host ::ng-deep .mat-form-field-underline {
    display: none!important;
  }
  .form-input {
    width: 420px;
    height: 56px;
    margin:0px 0px 32px 0px;
  }
  @media screen and (max-width: 599px){
    .form-input {
      width: 100%;
      height: 56px;
    }
  }
  :host ::ng-deep .mat-form-field-wrapper {
    padding-bottom: 0;
    height: 100%;
  }
  :host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
    padding: 0 12px;
    height: 100%;
    align-items: center;
  }
  :host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-infix {
    padding: 0;
    margin: 0;
    border: 0;
    display: flex;
    align-items: center;
  }
  .action-button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 10px 48.5px;

    background: #FF5E5E;
    border-radius: 2px;
    color: #fff;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;

    /* Inside auto layout */

    flex: none;
    order: 2;
    flex-grow: 0;
  }
  .action-button-disabled{
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 10px 48.5px;

    background: #999999;
    border-radius: 2px;
    color: #fff;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;

    /* Inside auto layout */

    flex: none;
    order: 2;
    flex-grow: 0;
  }
  .input_tips{
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0px 10px;
    gap: 9px;
    width: 420px;
    height: 38px;
    background: rgba(58, 144, 246, 0.2);
    border: 1px solid #3A73F6;
    border-radius: 2px;
    flex: none;
    order: 0;
    flex-grow: 0;
    color: #3A73F6;
  }
  @media screen and (max-width: 599px){
    .input_tips {
      width: 100%;
    }
  }
  .input_tips .mat-icon{
    width: 18px;
    height: 18px;
  }
  .input_tips .material-icons{
    font-size: 18px;
  }
  .email-list{
    position: absolute;
    width: 420px;
    font-size: 14px;
    background-color: #ffff;
    z-index: 9;
    border-radius: 8px;
    background: #ffff;
    box-shadow:  11px 11px 22px #9b9b9b,
                -11px -11px 22px #ffffff;
    margin-top: -15px;
  }
  @media screen and (max-width: 599px){
    .email-list {
      width: 340px;
    }
  }
  .email-list .mat-list-item{
    height: 32px;
    font-size: 14px;
  }
  .fade-in-fwd {
    -webkit-animation: fade-in-fwd 0.3s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
            animation: fade-in-fwd 0.3s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
  }
  @-webkit-keyframes fade-in-fwd {
    0% {
      -webkit-transform: translateZ(-80px);
              transform: translateZ(-80px);
      opacity: 0;
    }
    100% {
      -webkit-transform: translateZ(0);
              transform: translateZ(0);
      opacity: 1;
    }
  }
  @keyframes fade-in-fwd {
    0% {
      -webkit-transform: translateZ(-80px);
              transform: translateZ(-80px);
      opacity: 0;
    }
    100% {
      -webkit-transform: translateZ(0);
              transform: translateZ(0);
      opacity: 1;
    }
  }
  .fade-out-bck {
    -webkit-animation: fade-out-bck 0.3s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
            animation: fade-out-bck 0.3s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
  }
  @-webkit-keyframes fade-out-bck {
    0% {
      -webkit-transform: translateZ(0);
              transform: translateZ(0);
      opacity: 1;
    }
    100% {
      -webkit-transform: translateZ(-80px);
              transform: translateZ(-80px);
      opacity: 0;
    }
  }
  @keyframes fade-out-bck {
    0% {
      -webkit-transform: translateZ(0);
              transform: translateZ(0);
      opacity: 1;
    }
    100% {
      -webkit-transform: translateZ(-80px);
              transform: translateZ(-80px);
      opacity: 0;
    }
  }
  .slide-bottom {
    -webkit-animation: slide-bottom 0.6s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
            animation: slide-bottom 0.6s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
  }
  @-webkit-keyframes slide-bottom {
    0% {
      -webkit-transform: translateY(0);
              transform: translateY(0);
    }
    100% {
      -webkit-transform: translateY(18px);
              transform: translateY(18px);
    }
  }
  @keyframes slide-bottom {
    0% {
      -webkit-transform: translateY(0);
              transform: translateY(0);
    }
    100% {
      -webkit-transform: translateY(18px);
              transform: translateY(18px);
    }
  }
  .slide-top {
    -webkit-animation: slide-top 0.6s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
            animation: slide-top 0.6s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
  }
  @-webkit-keyframes slide-top {
    0% {
      -webkit-transform: translateY(0);
              transform: translateY(0);
    }
    100% {
      -webkit-transform: translateY(-48px);
              transform: translateY(-48px);
    }
  }
  @keyframes slide-top {
    0% {
      -webkit-transform: translateY(0);
              transform: translateY(0);
    }
    100% {
      -webkit-transform: translateY(-48px);
              transform: translateY(-48px);
    }
  }
  .none_tips{
    display: none;
  }
</style>

<div fxFill>
  <h2 i18n class="h2" >Reset Password</h2>
  <h4 i18n class="h4" >Use your email address</h4>
  <div id="tipsInfo" class="none_tips"><mat-icon>error</mat-icon>{{tipsInfo}}</div>
  <form #form="ngForm" id="forgetPasswordForm" >
    <!-- <mat-form-field class="form-input">
      <input matInput i18n-placeholder placeholder="Input your email"  required  [(ngModel)]="email" name="email">
    </mat-form-field> -->
    <mat-form-field class="form-input" floatLabel="never" appearance="fill" empty="false" autofilled="false">
      <svg MatPrefix style="margin-right: 20px;" width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M20 2C20 0.9 19.1 0 18 0H2C0.9 0 0 0.9 0 2V14C0 15.1 0.9 16 2 16H18C19.1 16 20 15.1 20 14V2ZM18 2L10 7L2 2H18ZM10 9L2 4V14H18V4L10 9Z" fill="black" fill-opacity="0.54"/>
      </svg>
      <input id="emailInput" matInput i18n-placeholder placeholder="Please enter your account number" (input)="sendNewValue()" (focus)='emailGetFocus(true)' (blur)="emailGetFocus(false)" [(ngModel)]="email" name="email">
    </mat-form-field>
    <mat-nav-list class="email-list" *ngIf="emailListShow" >
      <a href="javascript:void(0)" mat-list-item *ngFor="let item of emailTips;" (mousedown)="setEmail($event,item)">
        <span mat-line>{{email.indexOf('@')>-1?email.substring(0,email.indexOf('@')+1)+item:email+'@'+item}}</span>
      </a>
    </mat-nav-list>
    <!-- <div fxLayout="row" fxLayoutAlign="space-between">
      <a class="action-button" (click)="onSubmit()" i18n>Reset</a>
    </div> -->
<!--    <div fxLayout="row" >-->
<!--      <a [class]="email&&!emailError?'action-button':'action-button-disabled'" (click)="onSubmit()" i18n>Reset</a>-->
<!--      &lt;!&ndash; <a style="text-decoration-line: underline;" class="h3Size" routerLink="/users/signin" i18n>Sign In</a> &ndash;&gt;-->
<!--    </div>-->
    <div class="botton_box">
      <button id="forget-password-reset-loading" *ngIf="submitted" mat-fab color="warn" class="bounce-in-fwd" >
        <mat-spinner diameter="30"></mat-spinner>
      </button>
      <button id="forget-password-reset" style="padding: 0 60px;" *ngIf="!submitted" mat-raised-button disabled="{{!(email&&!emailError)}}" color="warn" (click)="onSubmit()" i18n>
        Reset
      </button>
    </div>
    <!-- <mat-progress-bar *ngIf="submitted" mode="indeterminate"></mat-progress-bar> -->
  </form>
</div>
