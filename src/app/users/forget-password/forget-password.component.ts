import { Component, OnInit } from "@angular/core";
import { environment } from "../../../environments/environment";
import { HttpClient } from "@angular/common/http";
import { Router } from "@angular/router";
import { MatSnackBar } from "@angular/material/snack-bar";
import { AppService } from "src/app/app.service";
import { emailList } from "../../../constants/email-list.constants";

@Component({
  selector: "app-forget-password",
  templateUrl: "./forget-password.component.html",
  styleUrls: ["./forget-password.component.css"],
})
export class ForgetPasswordComponent implements OnInit {
  email: any;
  submitted: any;
  emailError = false;
  tipsInfo: any;
  emailListShow = false;
  emailTips: any;

  constructor(
    private http: HttpClient,
    private router: Router,
    private snackBar: MatSnackBar,
    public appService: AppService
  ) {}

  ngOnInit() {}

  emailPrompt() {
    if (this.email && this.email.indexOf("@") > -1) {
      this.emailTips = emailList.filter((emailStr) => {
        return emailStr.indexOf(this.email.substring(this.email.indexOf("@") + 1, this.email.length)) > -1;
      });
    } else {
      this.emailTips = emailList;
    }
    this.emailListShow = this.email && this.emailTips && this.emailTips.length > 0 && !this.emailError ? true : false;
  }

  sendNewValue() {
    const reg = /(^\s+)|(\s+$)|\s+|\++/g;
    this.setEmailError(reg.test(this.email), this.appService.translate("EmailIllegalCharacters"));
    this.emailPrompt();
  }

  emailGetFocus(value) {
    if (value) {
      this.emailPrompt();
    } else {
      if (this.email && !this.emailError) {
        this.emailListShow = false;
        const reg = /^\w+((.\w+)|(-\w+))@[A-Za-z0-9]+((.|-)[A-Za-z0-9]+).[A-Za-z0-9]+$/;
        this.setEmailError(!reg.test(this.email), this.appService.translate("EmailFormatError"));
      }
    }
  }

  setEmailError(error, tips) {
    if (error) {
      document.getElementById("emailInput").style.color = "#FF5E5E";
      this.tipsInfo = tips;
      document.getElementById("tipsInfo").className = "input_tips fade-in-fwd";
      document.getElementById("forgetPasswordForm").className = "slide-bottom";
    } else if (this.emailError && !error) {
      document.getElementById("tipsInfo").className = "input_tips fade-out-bck";
      document.getElementById("forgetPasswordForm").className = "slide-top";
      document.getElementById("emailInput").style.color = "";
      this.tipsInfo = null;
    }
    this.emailError = error;
  }

  setEmail(event, item) {
    event.preventDefault();
    this.email = this.email.indexOf("@") > -1 ? this.email.substring(0, this.email.indexOf("@") + 1) + item : this.email + "@" + item;
    this.emailListShow = false;
  }

  onSubmit() {
    if (this.email && !this.emailError) {
      this.submitted = true;
      const logInUrl = environment.apiUrl !== undefined ? environment.apiUrl + "/api/v1/users/forgetpassword" : "/api/v1/users/forgetpassword";
      this.http.post<string>(logInUrl, { email: this.email }).subscribe(
        async (user) => {
          await this.router.navigate(["users/forget-password-verify"]);
        },
        (res) => {
          this.submitted = false;
          this.snackBar.open(res?.error?.message, "Okay", {
            duration: 2000,
            verticalPosition: "top",
          });
        }
      );
    }
  }
}
