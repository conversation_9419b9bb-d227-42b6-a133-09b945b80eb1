<div class="serve-layout">
    <div class="serviceTitle">服务概览</div>
    <div class="notification-bar" *ngIf="showResetNotification">
        <div class="notification-content">
            <span>您的流量已用完，如果需要高速流量，可以提前重置，重置说明见<a href="https://help.flashvpn.io/zh-CN/articles/1234567"
                    target="_blank">此链接</a>。</span>
            <button mat-raised-button color="primary" (click)="resetService()" [disabled]="loading">重置流量</button>
        </div>
    </div>
    <div class="serve">
        <app-service-tile class="appServiceTile"></app-service-tile>
        <div class="serveItem couponsBox"
            *ngIf="(rewardService.availableCoupons$ | async)?.length > 0; else waitCollectionVoucherTemplate">
            <app-coupons-countdown></app-coupons-countdown>
        </div>
        <ng-template #waitCollectionVoucherTemplate>
            <div class="serveItem couponsBox" (click)="goToWelfare()">
                <app-wait-collection></app-wait-collection>
            </div>
        </ng-template>
        <div class="serveItem inviteBox">
            <div class="inviteItemBox">
                <div class="invite">
                    <div class="inviteItem">
                        <div class="subTitle">账户余额</div>
                        <div class="title">$ {{ (walletService.wallet$ | async)?.balance }}</div>
                    </div>
                </div>
                <div>
                    <button *ngIf="!submitted" mat-raised-button color="warn"
                        (click)="router.navigate(['/recharge'])">充值</button>
                </div>
            </div>
            <div class="inviteItemBox">
                <div class="invite">
                    <div class="inviteItem">
                        <div class="subTitle">邀请奖励</div>
                        <div class="title">${{ (rewardService.inviteRewards$ | async)?.award || 0 }}</div>
                    </div>
                    <div class="inviteItem">
                        <div class="subTitle">累计邀请好友</div>
                        <div class="title">${{ (rewardService.inviteRewards$ | async)?.invitees || 0 }}人</div>
                    </div>
                </div>
                <div>
                    <button *ngIf="!submitted" mat-raised-button color="warn"
                        (click)="router.navigate(['/invite-record'])">查看邀请记录</button>
                </div>
            </div>
        </div>
    </div>
    <div class="serviceTitle">客户端安装指南</div>
    <div class="appVersionBox">
        <app-app-version></app-app-version>
    </div>
    <div class="serviceTitle">历史记录</div>
    <div class="showDesktop"><invoice-history></invoice-history></div>
    <div class="showMobile"><invoice-history-scroll></invoice-history-scroll></div>
</div>