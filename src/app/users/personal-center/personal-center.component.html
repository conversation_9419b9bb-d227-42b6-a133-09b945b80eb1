<div class="serve-layout">
    <div class="serviceTitle">服务概览</div>
    <div class="serve">
        <app-service-tile class="appServiceTile"></app-service-tile>
        <div class="serveItem couponsBox"
            *ngIf="(rewardService.availableCoupons$ | async)?.length > 0; else waitCollectionVoucherTemplate">
            <app-coupons-countdown></app-coupons-countdown>
        </div>
        <ng-template #waitCollectionVoucherTemplate>
            <div class="serveItem couponsBox" (click)="goToWelfare()">
                <app-wait-collection></app-wait-collection>
            </div>
        </ng-template>
        <div class="serveItem inviteBox">
            <div class="inviteItemBox">
                <div class="invite">
                    <div class="inviteItem">
                        <div class="subTitle">账户余额</div>
                        <div class="title">$ {{ (walletService.wallet$ | async)?.balance }}</div>
                    </div>
                </div>
                <div>
                    <button *ngIf="!submitted" mat-raised-button color="warn"
                        (click)="router.navigate(['/recharge'])">充值</button>
                </div>
            </div>
            <div class="inviteItemBox">
                <div class="invite">
                    <div class="inviteItem">
                        <div class="subTitle">邀请奖励</div>
                        <div class="title">${{ (rewardService.inviteRewards$ | async)?.award || 0 }}</div>
                    </div>
                    <div class="inviteItem">
                        <div class="subTitle">累计邀请好友</div>
                        <div class="title">${{ (rewardService.inviteRewards$ | async)?.invitees || 0 }}人</div>
                    </div>
                </div>
                <div>
                    <button *ngIf="!submitted" mat-raised-button color="warn"
                        (click)="router.navigate(['/invite-record'])">查看邀请记录</button>
                </div>
            </div>
        </div>
    </div>
    <div class="serviceTitle">客户端安装指南</div>
    <div class="appVersionBox">
        <!-- <app-app-version></app-app-version> -->
    </div>
    <div class="serviceTitle">历史记录</div>
    <div class="showDesktop"><invoice-history></invoice-history></div>
    <div class="showMobile"><invoice-history-scroll></invoice-history-scroll></div>
    <div class="serviceTitle">设备管理</div>
    <div class="serveItem deviceManagementBox">
        <div class="deviceManagementItemBox">
            <div class="deviceManagement">
                <div class="deviceManagementItem">
                    <div class="subTitle">设备管理</div>
                    <div class="title">管理您的登录设备</div>
                </div>
            </div>
            <div>
                <button *ngIf="!submitted" mat-raised-button color="warn"
                    (click)="router.navigate(['/devices'])">查看设备</button>
            </div>
        </div>
    </div>
    <div class="serviceTitle">流量日志</div>
    <div class="serveItem trafficLogBox">
        <app-traffic-log></app-traffic-log>
    </div>
</div>