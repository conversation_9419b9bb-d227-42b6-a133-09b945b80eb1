import { Observable } from 'rxjs';
import { Injectable } from '@angular/core';

@Injectable()
export class WebsocketService {
  ws: WebSocket;

  createObservableSocket(url: string): Observable<any> {
    this.ws = new WebSocket(url);
    return new Observable(
      observer => {
        this.ws.onmessage = event => observer.next(event.data);
        this.ws.onerror = error => observer.error(error);
        this.ws.onclose = event => observer.complete();
      }
    );
  }
}
