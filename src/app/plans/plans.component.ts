import { Component, OnInit } from '@angular/core';
import { APIManager } from "@flashvpn-io/web-core";
import { AppService } from '../app.service';
import { Router } from '@angular/router';
import {DialogsService} from '../utils/dialogs/dialogs.service';
import { Invoice } from "@flashvpn-io/web-core";
import { Service } from "@flashvpn-io/web-core";
import { Product } from "@flashvpn-io/web-core";
import { ServiceService } from "@flashvpn-io/web-core";

@Component({
  selector: 'app-plans',
  templateUrl: './plans.component.html',
  styleUrls: ['./plans.component.css']
})
export class PlansComponent implements OnInit {
  actived = 0;
  public invoice: Invoice;
  public service: Service;
  public products: Product[];
  public serviceDays = 0;
  public loading = true;

  constructor(
    public appService: AppService,
    private serviceService: ServiceService,
    private apiManager: APIManager,
    public dialogService: DialogsService,
    private router: Router,
  ) { }

  async ngOnInit() {
    this.service = await this.serviceService.currentService$.getValue();
    if (!this.service) {
      this.appService.snackUp(this.appService.translate('NoServiceTip'));
      this.router.navigate(['products']);
    } else {
      await this.fetchProducts();
      await this.loadData();
    }
  }

  async loadData() {
    this.loading = true;
    this.service = Service.fromData(await this.apiManager.fetchServiceDetail(this.service).toPromise());
    this.change(this.service.productId);
    this.serviceDays = this.service.daysTillDue();
    this.loading = false;
  }

  async fetchLatestInvoice() {
    this.apiManager.fetchLatestInvoice(this.service).subscribe((invoice: Invoice) => {
      if (this.service.status === 'Suspended') {
        this.apiManager.regenerateInvoice(invoice).subscribe((newInvoice: Invoice) => {
          this.router.navigateByUrl(`invoices?id=${newInvoice.id}`);
        });
      } else {
        this.router.navigateByUrl(`invoices?id=${invoice.id}`);
      }
    });
  }

  async fetchProducts() {
    this.apiManager.fetchProducts().subscribe(async value => {
      this.products = value.map(p => Product.fromData(p)).sort((a, b) => a.id - b.id);
    });
  }

  change(num: number) {
    this.actived = num;
  }

  upgrade(product: Product) {
    if (product.id === this.service.productId) {
      this.tips();
    }
    this.dialogService.openDialog('upgrade-confirm-dialog', {
      product,
    });

    this.dialogService.dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.apiManager.upgradeService(this.service.id, {
          productId: product.id
        }).subscribe((res: any) => {
          if (res.result === 'success') {
            if (res.type === 'downgrade') {
              if (res.invoiceid === 0) {
                this.appService.snackUp(this.appService.translate('SuccessfulDowngrade'));
                this.loadData();
              } else {
                this.appService.snackUp(this.appService.translate('InvoiceCreated'));
                this.router.navigate(['invoices'], {
                  queryParams: {
                    id: res.invoiceid,
                  }
                });
              }
            } else {
              if (res.invoiceid === 0) {
                this.appService.snackUp(this.appService.translate('SuccessfulUpgrade'));
                this.loadData();
              } else {
                this.appService.snackUp(this.appService.translate('InvoiceCreated'));
                this.router.navigate(['invoices'], {
                  queryParams: {
                    id: res.invoiceid,
                  }
                });
              }
            }
          } else {
            this.appService.snackUp(res.msg);
          }
        });
      }
    });
  }

  matchData(description) {
    return description.match(/\d+G/g).pop();
  }

  contactCS() {
    this.appService.snackUp(this.appService.translate('DowngradeInfo'));
  }

  tips() {
    this.appService.snackUp(this.appService.translate('NoNeedToUpgrade'));
  }

}
