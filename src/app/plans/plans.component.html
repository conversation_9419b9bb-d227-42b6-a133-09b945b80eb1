<style>
  .plans {
    color: #000;
    font-size: 12px;
    line-height: 15px;
  }

  li {
    list-style: none;
  }

  ul {
    margin: 0;
    padding: 0;
  }

  .list {
    height: 481px;
  }

  .item {
    /* height: 481px; */
    height: 100%;
    border: 1px dashed #DADADA;
    padding: 10px;
    width: 305px;
  }

  .item.active {
    border: 1px solid #FF5E5E;
  }

  .h6 {
    font-size: 12px;
    line-height: 15px;
    color: #000000;
    font-weight: 500;
  }

  .price {
    margin-bottom: 21px;
  }

  .price-num {
    font-weight: 500;
    font-size: 24px;
    line-height: 28px;
  }

  .month {}

  .detail-list {
    margin-bottom: 26px;
  }

  .detail-list li::before {
    content: '-';
    display: inline-block;
    margin-right: 10px;
  }

  .red {
    color: #FF5E5E;
  }

  /* btn */
  .btn {
    border-radius: 0;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    padding: 8px 20px;
    color: #fff;

  }

  .black {
    background: #000;
  }

  .btn-red {
    background: #FF5E5E;
  }

  .gray {
    background: #C4C4C4;
  }

  .divide {
    width: 1px;
    height: 80%;
    border-right: 1px dashed #DADADA;
  }

  @media screen and (max-width: 599px) {
    .list {
      flex-direction: column !important;
      height: auto;
    }

    .item {
      height: 481px;
    }

    .divide {
      display: none;
    }
  }

</style>
<div fxFill class="plans">
<!--  <div>-->
<!--    <p style="width: 330px;word-wrap:break-word;">Your current service is set to <span class="red">50 HKD</span> per-->
<!--      month, and it will be renewed on <span class="red">{{service.nextduedate}}</span></p>-->
<!--  </div>-->
  <mat-progress-bar *ngIf="loading" mode="indeterminate"></mat-progress-bar>
  <div *ngIf="!loading">
    <div *ngIf="service && service.usage">Out of <span class="highlighted">{{service.usageCapInGB()}}GB
    </span>monthly data, you have used <span class="highlighted">{{service.used()}}%</span></div>
    <div style="margin-right: 10px;margin-bottom: 20px;" fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="start center">
      <div *ngIf="serviceDays > 0">It expires in <span
        class="highlighted">{{service.daysTillDue().toFixed(0)}} </span> days</div>
      <span class="action-button" (click)="fetchLatestInvoice()" i18n>Renew Here</span>
    </div>
    <section>
      <ul fxLayout="row" fxLayoutAlign="space-around center" fxLayoutGap="10px" class="list">
        <li (click)="change(product.id)" *ngFor="let product of products" class="item" fxLayout="column" fxLayoutAlign="space-between start" [ngClass]="{active: actived == product.id, notactived: actived !== product.id}">
          <div>
            <h6>
              {{product.name}}
            </h6>
            <div class="price">
              <span class="price-num">{{product.pricing.HKD[service.billingcycle]}}<span i18n>HKD</span></span>
              <span class="month">&nbsp;/&nbsp;{{appService.translate(service.billingcycle)}}</span>
            </div>
            <div [innerHTML]="product.description"></div>
          </div>
          <div>
            <button id="plans-current-plan" *ngIf="product.id === service.productId" class="btn gray" (click)="tips()" mat-raised-button i18n>Current Plan</button>
            <button id="plans-upgrade" *ngIf="product.id > service.productId" class="btn btn-red" (click)="upgrade(product)" mat-raised-button i18n>Upgrade</button>
            <button id="plans-downgrade" *ngIf="product.id < service.productId" class="btn black" (click)="upgrade(product)" mat-raised-button i18n>Downgrade</button>
          </div>
        </li>
      </ul>
    </section>
  </div>
</div>
