import { ApplicationRef, Component, Input, OnInit, ViewChild } from "@angular/core";
import { APIManager } from "@flashvpn-io/web-core";
import { UsersService } from "@flashvpn-io/web-core";
import { AsyncSubject } from "rxjs";
import { HttpClient } from "@angular/common/http";
import { AppService } from "src/app/app.service";
import { AFTER_ORDER_CREATED, AFTER_USER_LOGIN, NotificationService } from "@flashvpn-io/web-core";
import { ActivatedRoute, ParamMap, Router } from "@angular/router";
import { PaymentComponent } from "../../billings/payment/payment.component";
import { DialogsService } from "src/app/utils/dialogs/dialogs.service";
import { Product } from "@flashvpn-io/web-core";
import { Invoice } from "@flashvpn-io/web-core";
import { Service } from "@flashvpn-io/web-core";

@Component({
  selector: "app-products",
  templateUrl: "./products.component.html",
  styleUrls: ["./products.component.css"],
})
export class ProductsComponent implements OnInit {
  public products: Product[];
  public billingCycle = "monthly";
  productSelected?: Product;
  invoice?: Invoice;
  promoCode?: string;
  public loading = true;

  @ViewChild("paymentComponent") paymentComponent: PaymentComponent;

  // fakeService to pass on to invoice component
  fakeService: Service = new Service("--", null, "--", this.billingCycle, null, null, {}, null);
  email: any;
  hide: any;
  password: any;
  submitted: any;

  constructor(
    public apiManager: APIManager,
    public usersService: UsersService,
    public http: HttpClient,
    public appService: AppService,
    public appRef: ApplicationRef,
    public route: ActivatedRoute,
    public router: Router,
    public notification: NotificationService,
    public dialogsService: DialogsService
  ) {}

  async ngOnInit() {
    this.apiManager.fetchProducts().subscribe((value) => {
      this.products = value.map((p) => Product.fromData(p)).sort((a, b) => a.id - b.id);
      this.loading = false;
      this.selectProduct(this.products[0]);
    });

    this.route.queryParamMap.subscribe(async (params: ParamMap) => {
      const pc = params.get("promoCode");
      if (!!pc) {
        this.promoCode = pc;
        this.appRef.tick();
      }
    });
  }
  selectProduct(product: Product) {
    this.productSelected = product;
    this.fakeService.billingcycle = this.billingCycle;
    this.fakeService.name = product.name;
  }
  openPayments(product: Product) {
    this.selectProduct(product);
    this.dialogsService.openDialog("payments", {
      product: this.productSelected,
      billingCycle: this.billingCycle,
      promoCode: this.promoCode,
    });
  }
}
