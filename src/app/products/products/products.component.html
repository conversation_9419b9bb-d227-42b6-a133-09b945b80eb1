<style>
  .stepNumber {
    width: 20px;
    height: 20px;
    color: white;
    border-radius: 10px;
    background: #FF5E5E;
  }

  .productBox {
    width: 300px;
    height: 150px;
    padding: 20px;

  }

  .productToBuyBox {
    border: 1px solid #FF5E5E;
  }

  .form-input {
    margin-right: 10px;
    min-width: 320px;
  }

  #productStep1 {
    padding-bottom: 10px;
  }

  #productStep2 {

    padding-bottom: 10px;
  }




  h2 {
    font-weight: 500;
    font-size: 36px;
    line-height: 44px;
  }

  .days {
    position: absolute;
    top: 0;
    right: 0;
  }

  .days-left {
    background: #FF5E5E;
    border: 1px solid #FF5E5E;
    width: 70px;
    height: 70px;
  }

  .days-left span {
    font-weight: 500;
    font-size: 24px;
    line-height: 29px;
    text-align: center;
    color: #fff;
    display: block;
  }

  .days-right {
    border: 1px solid #FF5E5E;
    height: 70px;
    padding-left: 10px;
    width: 162px;
  }

  .days-right span {
    font-weight: 500;
    font-size: 18px;
    line-height: 22px;
    margin-top: 6px;
    display: block;
  }

  .products {
    color: #000;
    font-size: 12px;
    line-height: 15px;
  }

  ul {
    margin: 0;
    padding: 0;
  }

  li {
    list-style: none;
  }

  .product-intro li {
    margin-bottom: 5px;
  }

  .product-intro li::before {
    content: '-';
    margin-right: 10px;
  }

  .select {}

  .promoCode {
    margin-right: 70px;
  }

  :host ::ng-deep .select .mat-form-field-infix {
    width: 154px;
  }

  :host ::ng-deep .select .mat-form-field-infix .mat-form-field-label span {
    font-size: 14px;
    color: #333;
  }

  :host ::ng-deep .mat-select {
    width: 150px;
    font-weight: 500;
  }

  :host ::ng-deep .mat-select-value-text {
    font-weight: 500;
    font-size: 18px;
    line-height: 22px;
    color: #000000;
  }

  :host ::ng-deep .mat-select-arrow {
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid;
    margin: 0 4px;
  }

  .product-list {}

  .product-item {
    position: relative;
    width: 320px;
    height: 363px;
    padding: 20px;
  }


  .product-item::after {
    content: '';
    height: 80%;
    border: 1px dashed #DADADA;
    position: absolute;
    top: 10%;
    right: 0;
  }
  .productToBuyBox.product-item::after {
    border: none;
  }
  .product-item .save {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 94, 94, 0.3);
    padding: 5px 10px;
    font-weight: 500;
    font-size: 12px;
    line-height: 15px;
    color: #FF5E5E;

  }

  .product-item h6 {
    margin: 0;
    padding: 0;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    margin-bottom: 20px;
  }

  .price {
    margin-bottom: 21px;
  }

  .price-num {
    font-weight: 500;
    font-size: 24px;
    line-height: 28px;
  }

  .total-price {
    margin-bottom: 18px;
  }
  .total-price-divide {
    margin: 0 8px;
  }

  .list-icon-item li::before {
    content: '-';
    display: inline-block;
    margin-right: 10px;
  }

  .red {
    color: #FF5E5E;
  ;
  }
  .red-bg {
    background: #FF5E5E;
  }

  .btn {
    border-radius: 0;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    padding: 8px 20px;
    color: #fff;
  }
  .billingSelect{
    width: 30%;
  }
  @media screen and (max-width: 599px) {
    h2 {
      width: 320px;
    }
    .days {
      position: static;
      justify-content: flex-start !important;
      margin-bottom: 20px;
    }
    .product-intro {
      width: 320px;
    }
    .select {
      justify-content: flex-start!important;
    }
    .promoCode {
      margin-right: 40px;
    }
    .product-list {
      flex-direction: column!important;
    }
    .product-item {
      margin-bottom: 20px;
    }
    .product-item::after {
      display: none;
    }
    .billingSelect{
      width: 100%;
    }
  }

</style>
<div style="position: relative;" class="products">
  <h2 i18n>Protect your internet right now</h2>
  <div class="days" fxLayout="row" fxLayoutAlign="center">
<!--    <div class="days-left" fxLayout="column" fxLayoutAlign="center center">-->
<!--      <span>7</span>-->
<!--      <span i18n>Days</span>-->
<!--    </div>-->
<!--    <div class="days-right" fxLayout="column" fxLayoutAlign="center start">-->
<!--      <span i18n>MONEY-BACK <br> 100% guarantee </span>-->
<!--    </div>-->
  </div>
  <div>
    <ul class="product-intro">
      <li i18n>Reach your content in the speed of flash light</li>
      <li i18n>Secure your internet without sacrificing your identity</li>
      <li i18n>Enjoy the same intuitive experience and supports across all the platforms</li>
    </ul>
  </div>
  <mat-progress-bar *ngIf="loading" mode="indeterminate"></mat-progress-bar>
  <div *ngIf="!loading" class="select" fxLayout="row" fxLayoutAlign="start center" style="margin: 30px 0;">
    <mat-select #billingSelect class="billingSelect" [(ngModel)]="billingCycle">
      <mat-option value="monthly" i18n>Billed Monthly</mat-option>
      <mat-option value="quarterly" i18n>Billed Quarterly</mat-option>
      <mat-option value="semiannually" i18n>Billed Semi-Annually</mat-option>
      <mat-option value="annually" i18n>Billed Annually</mat-option>
      <mat-option value="biennially" i18n>Billed Biennially</mat-option>
    </mat-select>
  </div>
  <div *ngIf="!loading">
    <ul class="product-list" fxLayout="row" fxLayoutAlign="space-around center" fxLayoutGap="10px">
      <li *ngFor="let product of products" class="product-item" fxLayout="column" fxLayoutAlign=" start" (click)="selectProduct(product)" [class.productToBuyBox]="productSelected==product">
        <div class="save"> <span i18n>Save</span> {{product.calculateSaved(billingCycle).saved.toFixed(0)}}%</div>
        <h6>{{product.name}}</h6>
        <div class="price">
          <span class="h2Size">{{product.calculateSaved(billingCycle).price.toFixed(2)}}<span class="h2Size" i18n>HKD</span></span><span>&nbsp;/&nbsp; {{appService.translate(billingCycle)}}</span>
        </div>
        <div class="total-price">
          <span>{{product.calculateSaved('annually').price.toFixed(2)}}<span
            i18n>HKD</span> / <span i18n>Yearly</span></span>
          <span class="total-price-divide">|</span>
          <span class="red" style="text-decoration: line-through">{{product.calculateSaved(billingCycle).pricePaidByMonth.toFixed(2)}}<span
            i18n>HKD</span> </span>
        </div>
        <div [innerHTML]="product.description"></div>
        <a id="products-continue-to-payment" style="margin-top: auto;" class="btn red-bg" mat-raised-button (click)="openPayments(product);$event.stopPropagation();"><span i18n>Continue to Payment</span></a>
      </li>
    </ul>
  </div>
  <div>
    <div style="margin-top: 10px"><span i18n>By continuing the payment , you agree to our</span><a id="products-terms-of-service"
      href="static/tos.html" target="_blank" i18n>Terms of Service</a> <span i18n> and </span> <a id="products-privacy-policy"
      href="static/privacy.html" target="_blank" i18n>Privacy Policy</a>.</div>
  </div>
</div>



<!-- <div>
  <div>
    <div id="productStep1">
      <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5px">
        <div fxLayout="row" fxLayoutAlign="center center" class="stepNumber">
          <div>1</div>
        </div>
        <h3 i18n>Choose a product fits your daily usage</h3>
      </div>
      <div fxLayout.gt-sm="row">
        <div class="productBox" [class.productToBuyBox]="productSelected==product" *ngFor="let product of products"
          (click)="selectProduct(product)">
          <div style="padding-bottom: 20px">{{product.name}}</div>
          <div style="padding-bottom: 20px"><span
              class="h2Size">{{product.calculateSaved(billingCycle).price.toFixed(2)}}</span><span class="h2Size"
              i18n>HKD</span><span> / {{appService.translate(billingCycle)}}</span></div>
          <div>{{product.description}}</div>
          <div style="padding-bottom: 10px"><span>Save</span><span
              style="color: #FF5E5E;">{{product.calculateSaved(billingCycle).saved.toFixed(0)}}%</span> <span
              i18n="if you pay monthly">if you pay {{appService.translate(billingCycle)}}</span></div>
          <mat-select style="width: 50%; font-weight: 500; " [(value)]="billingCycle"
            (valueChange)="selectProduct(product)" #billingSelect>
            <mat-option value="monthly" i18n>Billed Monthly</mat-option>
            <mat-option value="quarterly" i18n>Billed Quarterly</mat-option>
            <mat-option value="semiannually" i18n>Billed Semi-Annually</mat-option>
            <mat-option value="annually" i18n>Billed Annually</mat-option>
          </mat-select>
        </div>
      </div>

    </div>
    <div *ngIf="!usersService.user" id="productStep2">
      <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5px">
        <div fxLayout="row" fxLayoutAlign="center center" class="stepNumber">
          <div>2</div>
        </div>
        <h3 i18n>Fill out your email to create an account</h3>
      </div>
      <div>
        <form #form="ngForm">
          <mat-form-field class="form-input">
            <input matInput i18n-placeholder="" placeholder="Input your email" required [(ngModel)]="email"
              name="email">
          </mat-form-field>

          <mat-form-field class="form-input">
            <input matInput i18n-placeholder placeholder="Enter your password" [type]="hide ? 'password' : 'text'"
              required [(ngModel)]="password" name="password">
            <a mat-icon-button matSuffix (click)="hide = !hide" [attr.aria-label]="'Hide password'"
              [attr.aria-pressed]="hide">
              <mat-icon>{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
            </a>
          </mat-form-field>
        </form>

      </div>
    </div>
    <div id="productStep3">
      <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5px">
        <div fxLayout="row" fxLayoutAlign="center center" class="stepNumber">
          <div>{{usersService.user ? 2 : 3}}</div>
        </div>
        <h3 i18n>Pick your preferred payment method</h3>
      </div>
      <div>
        <app-payment #paymentComponent [service]="fakeService" [product]="productSelected" [invoice]="invoice"
          [preSubmit]="placeOrder.bind(this)" [promoCode]="promoCode"></app-payment>
      </div>
    </div>
  </div>
</div> -->
