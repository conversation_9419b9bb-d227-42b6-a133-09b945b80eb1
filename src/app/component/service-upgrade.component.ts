import { Component, Input, OnInit } from "@angular/core";
import { defaultBillingCycles, ServiceService } from "@flashvpn-io/web-core";
import { BuyType, DeductionType, RouterBuyType } from "@flashvpn-io/web-core";
import { Router } from "@angular/router";
import { APIManager } from "@flashvpn-io/web-core";
import { DialogsService } from "../utils/dialogs/dialogs.service";

@Component({
  selector: "app-service-upgrade",
  template: `
    <div class="serveItem">
      <div *ngIf="isUpgradeable() && !noService()" class="mobile">
        <div class="frameParent">
          <div class="parent">
            <div class="div">当前服务</div>
            <div class="g">{{ (serviceService.currentService$ | async)?.name }}-{{ productFlow[(serviceService.currentService$ | async)?.productId] }}/月</div>
          </div>
          <div class="div1">
            <div class="div2" (click)="upgradeService()">升级套餐</div>
          </div>
        </div>
        <div class="group">
          <div class="div3">服务说明</div>
          <div class="frameGroup">
            <div class="iconParent">
              <img class="icon" alt="" src="assets/images/arrow.svg" />
              <div class="g5mbps">前{{productFlow[(serviceService.currentService$ | async)?.productId]}}/月不限速，超过限速{{(serviceService.currentService$ | async)?.speedLimitByProductId()}}Mbps</div>
            </div>
            <div class="iconParent">
              <img class="icon" alt="" src="assets/images/arrow.svg" />
              <div class="div4">限速后只能使用部分节点</div>
            </div>
            <div class="iconParent">
              <img class="icon" alt="" src="assets/images/arrow.svg" />
              <div class="div4">同时链接2台设备</div>
            </div>
            <div class="iconParent">
              <img class="icon" alt="" src="assets/images/arrow.svg" />
              <div class="div4">流量每{{getServiceBillingCycle()}}重置</div>
            </div>
          </div>
        </div>
      </div>
      <div *ngIf="isUpgradeable() && !noService()" class="pc">
        <div class="pc-mobile">
          <div class="pc-frameParent">
            <div class="pc-parent">
              <div class="pc-div">当前服务</div>
              <div class="pc-g">{{ (serviceService.currentService$ | async)?.name }}-{{ productFlow[(serviceService.currentService$ | async)?.productId] }}/月</div>
            </div>
            <div class="pc-div1" (click)="upgradeService()">
              <div class="pc-div2">升级套餐</div>
            </div>
          </div>
          <div class="pc-group">
            <div class="pc-div3">服务说明</div>
            <div class="pc-frameGroup">
              <div class="pc-iconParent">
                <img class="pc-icon" alt="" src="assets/images/arrow.svg" />
                <div class="pc-g5mbps">前{{productFlow[(serviceService.currentService$ | async)?.productId]}}不限速，超过限速{{(serviceService.currentService$ | async)?.speedLimitByProductId()}}Mbps</div>
              </div>
              <div class="pc-iconGroup">
                <img class="pc-icon" alt="" src="assets/images/arrow.svg" />
                <div class="pc-div4">限速后只能使用部分节点</div>
              </div>
              <div class="pc-iconContainer">
                <img class="pc-icon" alt="" src="assets/images/arrow.svg" />
                <div class="pc-div4">同时链接{{deviceLimit}}台设备</div>
              </div>
              <div class="pc-frameDiv">
                <img class="pc-icon" alt="" src="assets/images/arrow.svg" />
                <div class="pc-div4">流量每月重置</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .serveItem {
        height: 100%;
        box-shadow: 0px 0px 10px 0px rgba(255, 94, 94, 0.1);
        position: relative;
      }
        .mobile {
          width: 100%;
          position: relative;
          border-radius: 0px 0px 2px 2px;
          background-color: #fff;
          box-sizing: border-box;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: flex-start;
          padding: 24px 24px 28px;
          gap: 24px;
          cursor: pointer;
          text-align: left;
          font-size: 12px;
          color: #808080;
          font-family: 'PingFang SC';
        }

      .frameParent {
          align-self: stretch;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
        }

      .pc-g {
        width: 137px;
        position: relative;
        font-size: 14px;
        line-height: 14px;
        font-weight: 600;
        color: #000;
        display: flex;
        align-items: center;
      }

      .g {
        margin-top: 4px;
        font-size: 14px;
        line-height: 14px;
        font-weight: 600;
        color: #000;
      }

      .iconParent {
        display: flex;
        flex-direction: row;
      }

      .pc-parent {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        gap: 6px;
      }

      .pc-div2 {
        position: relative;
        line-height: 20px;
      }
            .div1 {
          border-radius: 2px;
          border: 1px solid #ff5e5e;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          padding: 6px 12px;
          font-size: 14px;
          color: #ff5e5e;
        }

       .div2 {
          position: relative;
          line-height: 20px;
        }

      .pc-div1 {
        border-radius: 2px;
        border: 1px solid #ff5e5e;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        padding: 6px 12px;
        cursor: pointer;
        font-size: 14px;
        color: #ff5e5e;
      }

      .pc-frameParent {
        align-self: stretch;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
      }

      .pc-div3 {
        position: absolute;
        top: 0px;
        left: 0px;
        line-height: 100%;
        font-weight: 600;
      }

      .pc-icon {
        position: absolute;
        top: 0px;
        left: 0px;
        width: 14px;
        height: 14px;
      }

      .pc-g5mbps {
        position: absolute;
        top: 2px;
        left: 16px;
        line-height: 100%;
        width: max-content;
      }

      .pc-iconParent {
        position: absolute;
        top: 0px;
        left: 0px;
        width: 186px;
        height: 14px;
      }

      .iconParent, .iconGroup, .iconContainer, .frameDiv {
        display: flex;
        flex-direction: row;
      }

      .pc-div4 {
        position: absolute;
        top: 1px;
        left: 16px;
        line-height: 100%;
      }

      .pc-iconGroup {
        position: absolute;
        top: 18px;
        left: 0px;
        width: 148px;
        height: 14px;
      }

      .pc-iconContainer {
        position: absolute;
        top: 36px;
        left: 0px;
        width: 108px;
        height: 14px;
      }

      .pc-frameDiv {
        position: absolute;
        top: 54px;
        left: 0px;
        width: 88px;
        height: 14px;
      }

      .pc-frameGroup {
        position: absolute;
        top: 20px;
        left: 0px;
        width: 186px;
        height: 69px;
      }

      .pc-group {
        width: 186px;
        position: relative;
        height: 93px;
      }

      .pc-mobile {
        flex: 1;
        width: 100%;
        position: relative;
        border-radius: 0px 0px 2px 2px;
        background-color: #fff;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        padding: 24px 24px 28px;
        gap: 24px;
        text-align: left;
        font-size: 12px;
        color: #808080;
        font-family: 'PingFang SC';
      }
      @media (min-width: 767px) {
        .mobile {
          display: none
        }
        .pc {
          display: flex;
          min-height: 225px;
        }
      }

      @media (max-width: 767px) {
        .pc {
          display: none;
        }
        .mobile {
          display: flex;
        }
      }
    `,
  ],
})
export class ServiceUpgradeComponent {

  loading: boolean = false;
  upgradeable: boolean = false;
  deviceLimit: number = 5;
  productFlow = {
    1: "50G",
    2: "100G",
    3: "200G",
  };

  constructor(public router: Router, public serviceService: ServiceService, public apiManager: APIManager, public dialogsService: DialogsService) {
    switch (this.serviceService.currentService$.value?.productId) {
      case 1:
        this.deviceLimit = 5
        break;
      case 2:
        this.deviceLimit = 8
        break;
      case 3:
        this.deviceLimit = 10
        break;
    }
  }

  noService() {
    const currentService = this.serviceService.currentService$.value;
    if (currentService) {
      return currentService.isDue();
    }
    return true;
  }

  upgradeService() {
    this.router.navigate(['/upgrade']);
  }

  getServiceBillingCycle() {
    const currentBillingCycle = defaultBillingCycles.find(cycle => cycle.id === this.serviceService.currentService$.value?.billingcycle);
    if (currentBillingCycle) {
      return currentBillingCycle.unit;
    }
  }

  isUpgradeable() {
    this.upgradeable = this.serviceService.currentService$.value?.productId !== 3;
    return this.upgradeable;
  }
}
