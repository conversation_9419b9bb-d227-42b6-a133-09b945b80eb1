import { Component, Input, Output, EventEmitter } from "@angular/core";
import { FormControl, Validators } from "@angular/forms";
import { WalletsService } from "@flashvpn-io/web-core";

@Component({
  selector: "app-select-balance",
  template: `
    <div class="deduction-box">
      <div class="deduction-top-box">
        <p>
          当前可用余额：HK\${{ (walletsService.wallet$ | async)?.balance }}，本次最多抵扣 HK\${{
            deductionMaxAmount
          }}（每笔订单现金支付不得低于8.00港币）
        </p>
        <p>订单提交后，你抵扣的余额将会立即扣除，如订单最终未支付成功，您抵扣的余额将会在次日返还</p>
      </div>
      <div class="deduction-bottom-box">
        <mat-form-field appearance="outline" class="example-full-width">
          <mat-label>输入抵扣金额（港币）</mat-label>
          <input
            id="deduction-balance"
            name="deduction-balance"
            matInput
            type="number"
            i18n-placeholder
            (change)="valueChange($event)"
            [formControl]="enabled ? deductionError : disabledControl"
            required />
          <mat-error *ngIf="deductionError.invalid">{{ getErrorMessage() }}</mat-error>
        </mat-form-field>
      </div>
    </div>
  `,
  styles: [
    `
      .deduction-box {
        font-family: Arial, sans-serif;
        margin-bottom: 10px;
      }
      .deduction-top-box p {
        margin: 5px 0;
        color: #666;
        font-size: 0.9em;
      }
      .deduction-bottom-box {
        margin-top: 10px;
      }
      .example-full-width {
        width: 384px;
      }
      @media screen and (max-width: 599px) {
        .example-full-width {
          width: 100%;
        }
      }
    `,
  ],
})
export class SelectBalanceComponent {
  @Input() enabled: boolean = true;
  @Input() set price(value: number) {
    if (this._price !== value) {
      this._price = value;
      this.updateDeductionMaxAmount();
    }
  }
  private _price: number = 0;

  @Output() deductionAmountChange = new EventEmitter<number>();

  deductionAmount: number = 0;
  deductionError = new FormControl("", [Validators.required, Validators.min(0)]);
  disabledControl = new FormControl({ value: "", disabled: true });

  constructor(public walletsService: WalletsService) { }

  ngOnInit() {
    this.updateDeductionMaxAmount();
  }

  private updateDeductionMaxAmount() {
    this.deductionError.clearValidators();
    this.deductionError.setValidators([Validators.required, Validators.min(1), Validators.max(this.deductionMaxAmount)]);
  }

  get deductionMaxAmount() {
    if (this._price - 8 < 0 || !this.walletsService.wallet$.value) {
      return 0;
    }
    return Math.min(this.walletsService.wallet$.value.balance, this._price - 8);
  }
  valueChange($event: any) {
    if (this.deductionError.valid) {
      this.deductionAmount = $event.target.value;
      this.deductionAmountChange.emit($event.target.value);
    }
  }
  getErrorMessage() {
    if (this.deductionError.hasError("required")) {
      return "请输入需要抵扣的金额";
    } else if (this.deductionError.hasError("min")) {
      return `抵扣金额不能少于${this.deductionError?.getError("min")?.min}港币`;
    } else if (this.deductionError.hasError("max")) {
      return `抵扣金额不能大于${this.deductionError?.getError("max")?.max}港币`;
    }
    return "";
  }
}
