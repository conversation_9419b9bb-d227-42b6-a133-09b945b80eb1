import { Component, EventEmitter, Output } from "@angular/core";
@Component({
  selector: "app-already-registered",
  template: `
    <div class="already-registered">
      <img src="assets/images/warn.svg" alt="Warn" />
      <div class="content">
        <div class="content-text-bold">无法购买服服务</div>
        <div class="content-text">您的账户已经注册过了, 请登录后直接购买</div>
      </div>
      <div class="foot">
        <button mat-stroked-button color="warn" (click)="onOk.emit()">前往登录</button>
      </div>
    </div>
  `,
  styles: [
    `
      .already-registered {
        width: 100%;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 10px;
      }
      .content {
        width: 100%;
        margin: 15px 30px;
        display: flex;
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }
      .content-text-bold {
        font-size: 16px;
        font-weight: 500;
        line-height: 22.4px;
        text-align: center;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
      }
      .content-text {
        font-size: 16px;
        font-weight: 400;
        line-height: 32px;
        text-align: center;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
      }
      .foot {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
      }
    `,
  ],
})
export class AlreadyRegisteredComponent {
  @Output() onOk = new EventEmitter();

  constructor() {}
}
