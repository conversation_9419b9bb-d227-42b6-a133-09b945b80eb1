import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { IVoucher, VoucherType } from './voucher.component';
import * as qrcode from 'qrcode';
import { QrDialogService } from '../utils/dialogs/qr-dialog.component';
import { config } from 'process';
import { AppService } from '../app.service';

@Component({
  selector: 'app-versions-info',
  template: `
    <div class="frame">
          <div class="div">
            <div class="div-2">
              <div class="text-wrapper">自研客戶端</div>
              <div class="rectangle"></div>
            </div>
            <div class="frame-wrapper">
              <div class="div-3">
                <div class="div-4">
                  <div class="div-wrapper">
                    <div class="div-5">
                      <div class="start-icon"><img class="img" src="assets/images/start-icon.svg" /></div>
                      <div class="text-wrapper-2">FlashVPN {{platform}}</div>
                    </div>
                  </div>
                  <div class="div-6">
                    <button class="button" *ngIf="isValidIP(0)">
                      <a class="label" target="_blank" href="http://{{deliveryIPs[0]}}:9992/how-to-use-flashvpn-on-{{platform.toLowerCase()}}">操作指南 1&gt;</a>
                    </button>
                    <button class="button" *ngIf="isValidIP(1)">
                      <a class="label" target="_blank" href="http://{{deliveryIPs[1]}}:9992/how-to-use-flashvpn-on-{{platform.toLowerCase()}}">操作指南 2&gt;</a>
                    </button>
                  </div>
                </div>
                <div class="div-wrapper-2">
                  <p class="p">
                    {{describe}} <a *ngIf="storeUrl" class="label" target="_blank" href="{{storeUrl}}"> 应用商店</a>
                  </p>
                </div>
                <div class="div-7">
                  <div class="div-6">
                    <div class="text-wrapper-3">当前版本:</div>
                    <div class="text-wrapper-4" *ngIf="allVersionArr">
                      <span *ngIf="allVersionArr.short_version">v{{allVersionArr.short_version}}</span>
                      <span *ngIf="!allVersionArr.short_version">v{{allVersionArr.version || '-'}}</span>
                    </div>
                    <div class="text-wrapper-4" *ngIf="!allVersionArr">v-</div>
                  </div>
                  <div class="div-6">
                    <div class="text-wrapper-3">更新日期:</div>
                    <div class="text-wrapper-4">
                      {{allVersionArr?.uploaded_at_time || '-'}}
                    </div>
                  </div>
                  <div class="div-6">
                    <div class="text-wrapper-5">更新日志:</div>
                  </div>
                  <div class="div-6">
                    <div class="text-wrapper-6">{{allVersionArr?.release_notes || '-'}}</div>
                  </div>
                </div>
                <div class="frame-wrapper-2">
                  <div class="div-8">
                    <div class="div-wrapper-3" *ngIf="allVersionArr?.download_url">
                      <a class="text-wrapper-7" *ngIf="allVersionArr?.platform === 'ios'" target="_blank" href="{{allVersionArr.download_url}}">下载地址 1</a>
                      <a class="text-wrapper-7" *ngIf="allVersionArr?.platform !== 'ios'" target="_blank" href="https://{{deliveryDownloadDomains[0]}}{{allVersionArr.download_url}}">下载地址 1</a>
                    </div>
                    <div class="div-wrapper-3" *ngIf="allVersionArr?.download_url">
                      <a class="text-wrapper-7" target="_blank" href="https://{{deliveryDownloadDomains[1]}}{{allVersionArr.download_url}}">下载地址 2</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="div" *ngIf="thirdPartyAppList.length > 0">
            <div class="div-9">
              <div class="div-2">
                <div class="text-wrapper">第三方應用</div>
                <div class="rectangle"></div>
              </div>
              <ng-container *ngFor="let app of thirdPartyAppList">
                <img
                  class="img-2"
                  [class.selected]="selectedApp === app"
                  [src]="getAppIconPath(app)"
                  (click)="selectApp(app)"
                />
              </ng-container>
            </div>
            <div class="frame-wrapper" *ngIf="selectedApp">
              <div class="div-10">
                <div class="div-4">
                  <div class="div-wrapper">
                    <div class="div-5">
                      <img
                        class="element"
                        [src]="getAppIconPath(selectedApp)"
                      />
                      <div class="text-wrapper-2">{{selectedApp}}</div>
                    </div>
                  </div>
                  <div class="div-6">
                    <div class="div-6">
                      <button class="button" *ngIf="isValidIP(0)">
                        <a class="label" target="_blank" href="http://{{deliveryIPs[0]}}:9992/how-to-use-flashvpn-with-{{selectedApp.toLowerCase().includes('clash') ? 'clash-verge' : selectedApp.toLowerCase()}}">操作指南 1&gt;</a>
                      </button>
                      <button class="button" *ngIf="isValidIP(1)">
                        <a class="label" target="_blank" href="http://{{deliveryIPs[1]}}:9992/how-to-use-flashvpn-with-{{selectedApp.toLowerCase().includes('clash') ? 'clash-verge' : selectedApp.toLowerCase()}}">操作指南 2&gt;</a>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="div-11" *ngIf="getAppDownloadUrls().length > 0 && !isIosThirdPartyApp">
                  <ng-container *ngFor="let downloadInfo of getAppDownloadUrls()">
                    <ng-container *ngFor="let fullUrl of getFullDownloadUrls(downloadInfo)">
                      <div class="div-wrapper-4">
                        <a class="text-wrapper-8"
                           [href]="fullUrl.url"
                           target="_blank">
                          {{fullUrl.name}}
                        </a>
                      </div>
                    </ng-container>
                  </ng-container>
                </div>
                <div class="div-11" *ngIf="getAppDownloadUrls().length > 0 && isIosThirdPartyApp">
                  <p>我们提供共享账号,请联系客服获取.</p>
                </div>
                <div class="div-12">
                  <ng-container *ngFor="let subscription of subscriptionUrls; let i = index">
                    <div class="frame-wrapper-3">
                      <div class="div-13">
                        <div class="text-wrapper-9">{{subscription.lineIcon}} {{subscription.name}}订阅地址</div>
                        <div class="div-14">
                          <div class="div-6">
                            <div class="label-2" (click)="copyToClipboard(subscription.url, $event)">拷貝</div>
                            <img class="line" src="assets/images/line-13-2.svg" />
                            <div class="div-14 label-2"
                             (click)="generateQrCode(subscription.url, subscription.name)"
                             [matTooltip]="'扫描二维码'"
                             >
                              <div>二維碼</div>
                              <img class="scan" src="assets/images/scan-2.svg" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                </div>
              </div>
            </div>
          </div>
        </div>
  `,
  styles: [`
    @import '~swiper/swiper-bundle.min.css';
    @import url("https://fonts.googleapis.com/css?family=Noto+Sans+SC:var(--ch-body-16-500-font-weight),400,var(--ch-body-14-500-font-weight),var(--ch-body-14-400-font-weight),500,700");


    .frame {
        --uicolorprimary: rgba(103, 80, 163, 1);
        --flowkitpurple: rgba(123, 97, 255, 1);
        --flowkitcharcoal: rgba(34, 34, 34, 1);
        --flowkitred: rgba(252, 85, 85, 1);
        --flowkitgreen: rgba(41, 204, 106, 1);
        --flowkitblue: rgba(0, 153, 255, 1);
        --flowkitwhite: rgba(255, 255, 255, 1);
        --neutral-800: rgba(44, 44, 44, 1);
        --neutral900-black: rgba(11, 11, 11, 1);
        --neutral-700: rgba(88, 88, 88, 1);
        --primary-red-500: rgba(255, 93, 93, 1);
        ---font-family: "PingFang SC", Helvetica;
        ---font-weight: 400;
        ---font-size: 14px;
        ---letter-spacing: 0px;
        ---line-height: 14px;
        ---font-style: normal;
        --caption-font-family: "PingFang SC", Helvetica;
        --caption-font-weight: 400;
        --caption-font-size: 12px;
        --caption-letter-spacing: 0px;
        --caption-line-height: 100%;
        --caption-font-style: normal;
        --ch-body-14-400-font-family: "Noto Sans SC", Helvetica;
        --ch-body-14-400-font-weight: 400;
        --ch-body-14-400-font-size: 14px;
        --ch-body-14-400-letter-spacing: 0px;
        --ch-body-14-400-line-height: 22px;
        --ch-body-14-400-font-style: normal;
        --ch-body-14-500-font-family: "Noto Sans SC", Helvetica;
        --ch-body-14-500-font-weight: 500;
        --ch-body-14-500-font-size: 14px;
        --ch-body-14-500-letter-spacing: 0px;
        --ch-body-14-500-line-height: 22px;
        --ch-body-14-500-font-style: normal;
        --ch-body-16-500-font-family: "Noto Sans SC", Helvetica;
        --ch-body-16-500-font-weight: 500;
        --ch-body-16-500-font-size: 16px;
        --ch-body-16-500-letter-spacing: 0.16px;
        --ch-body-16-500-line-height: 24px;
        --ch-body-16-500-font-style: normal;
        --card: 0px 0px 10px 0px rgba(255, 94, 94, 0.1);
        --variable-collection-flash: rgba(255, 94, 94, 1);
        --variable-collection-1: rgba(128, 128, 128, 1);
        --variable-collection: rgba(20, 21, 44, 1);
        --variable-collection-2: rgba(153, 153, 153, 1);
    }

    * {
        -webkit-font-smoothing: antialiased;
        box-sizing: border-box;
    }

    html,
    body {
        margin: 0px;
        height: 100%;
    }

    /* a blue color as a generic focus style */
    button:focus-visible {
        outline: 2px solid #4a90e2 !important;
        outline: -webkit-focus-ring-color auto 5px !important;
    }

    a {
        text-decoration: none;
    }

    @font-face {
        font-family: "PingFang SC-Semibold";
        src: url("https://anima-uploads.s3.amazonaws.com/projects/64216d7d4623c1aa040fa514/fonts/pingfangsc-semibold.otf") format("opentype");
    }

    @font-face {
        font-family: "PingFang SC-Medium";
        src: url("https://anima-uploads.s3.amazonaws.com/projects/64095f281bef4b15eceb2dc2/fonts/pingfang-sc-medium.otf") format("opentype");
    }

    .frame {
        display: inline-flex;
        height: auto;
        min-height: 407px;
        align-items: flex-start;
        gap: 24px;
        padding: 20px;
        position: relative;
    }

    .frame .div {
        display: inline-flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 24px;
        position: relative;
        flex: 0 0 auto;
    }

    .frame .div-2 {
        display: inline-flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        gap: 8px;
        position: relative;
        flex: 0 0 auto;
    }

    .frame .text-wrapper {
        position: relative;
        width: fit-content;
        margin-top: -1.00px;
        font-family: "Noto Sans SC-Bold", Helvetica;
        font-weight: 700;
        color: var(--neutral-800);
        font-size: 18px;
        letter-spacing: 0;
        line-height: normal;
    }

    .frame .rectangle {
        position: relative;
        width: 18px;
        height: 3px;
        background-color: var(--primary-red-500);
        border-radius: 999px;
    }

    .frame .frame-wrapper {
        display: flex;
        flex-direction: column;
        width: 472px;
        height: fit-content;
        align-items: flex-start;
        gap: 32px;
        padding: 0px 0px 32px;
        position: relative;
    }

    .frame .div-3 {
        display: flex;
        flex-direction: column;
        height: 328px;
        align-items: flex-start;
        position: relative;
        align-self: stretch;
        width: 100%;
        margin-bottom: -14.00px;
        border-radius: 12px;
        border: 1px solid;
        border-color: #0000001a;
    }

    .frame .div-4 {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 20px 16px 12px;
        position: relative;
        align-self: stretch;
        width: 100%;
        flex: 0 0 auto;
    }

    .frame .div-wrapper {
        display: flex;
        align-items: center;
        gap: 16px;
        position: relative;
        flex: 1;
        flex-grow: 1;
    }

    .frame .div-5 {
        display: inline-flex;
        align-items: center;
        gap: 16px;
        position: relative;
        flex: 0 0 auto;
    }

    .frame .start-icon {
        display: inline-flex;
        align-items: flex-start;
        position: relative;
        flex: 0 0 auto;
    }

    .frame .img {
        position: relative;
        width: 32px;
        height: 32px;
    }

    .frame .text-wrapper-2 {
        position: relative;
        width: fit-content;
        font-family: "PingFang SC-Semibold", Helvetica;
        font-weight: 400;
        color: var(--neutral-800);
        font-size: 20px;
        letter-spacing: 0;
        line-height: 14px;
        white-space: nowrap;
    }

    .frame .div-6 {
        display: inline-flex;
        align-items: center;
        gap: 12px;
        position: relative;
        flex: 0 0 auto;
    }

    .frame .button {
        all: unset;
        box-sizing: border-box;
        display: inline-flex;
        align-items: center;
        gap: 4px;
        position: relative;
        flex: 0 0 auto;
        border-radius: 6px;
    }

    .frame .label {
        position: relative;
        width: fit-content;
        margin-top: -1.00px;
        font-family: "Noto Sans SC-Medium", Helvetica;
        font-weight: 500;
        color: var(--neutral900-black);
        font-size: 14px;
        text-align: center;
        letter-spacing: 0.14px;
        line-height: 24px;
        text-decoration: underline;
        white-space: nowrap;
    }

    .frame .div-wrapper-2 {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        padding: 16px 16px 10px;
        position: relative;
        align-self: stretch;
        width: 100%;
        flex: 0 0 auto;
    }

    .frame .p {
        position: relative;
        flex: 1;
        margin-top: -1.00px;
        font-family: var(--ch-body-14-400-font-family);
        font-weight: var(--ch-body-14-400-font-weight);
        color: var(--neutral-700);
        font-size: var(--ch-body-14-400-font-size);
        letter-spacing: var(--ch-body-14-400-letter-spacing);
        line-height: var(--ch-body-14-400-line-height);
        font-style: var(--ch-body-14-400-font-style);
    }

    .frame .div-7 {
        display: inline-flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        gap: 5px;
        padding: 5px 16px;
        position: relative;
        flex: 0 0 auto;
    }

    .frame .text-wrapper-3 {
        width: fit-content;
        font-family: var(--ch-body-14-500-font-family);
        font-weight: var(--ch-body-14-500-font-weight);
        color: var(--neutral-800);
        letter-spacing: var(--ch-body-14-500-letter-spacing);
        line-height: var(--ch-body-14-500-line-height);
        white-space: nowrap;
        position: relative;
        font-size: var(--ch-body-14-500-font-size);
        font-style: var(--ch-body-14-500-font-style);
    }

    .frame .text-wrapper-4 {
        position: relative;
        width: fit-content;
        margin-top: -1.00px;
        font-family: var(--ch-body-16-500-font-family);
        font-weight: var(--ch-body-16-500-font-weight);
        color: var(--neutral-800);
        font-size: var(--ch-body-16-500-font-size);
        letter-spacing: var(--ch-body-16-500-letter-spacing);
        line-height: var(--ch-body-16-500-line-height);
        white-space: nowrap;
        font-style: var(--ch-body-16-500-font-style);
    }

    .frame .text-wrapper-5 {
        width: fit-content;
        margin-top: -1.00px;
        font-family: var(--ch-body-14-500-font-family);
        font-weight: var(--ch-body-14-500-font-weight);
        color: var(--neutral-800);
        letter-spacing: var(--ch-body-14-500-letter-spacing);
        line-height: var(--ch-body-14-500-line-height);
        white-space: nowrap;
        position: relative;
        font-size: var(--ch-body-14-500-font-size);
        font-style: var(--ch-body-14-500-font-style);
    }

    .frame .text-wrapper-6 {
        position: relative;
        width: 169px;
        margin-top: -1.00px;
        font-family: var(--ch-body-14-500-font-family);
        font-weight: var(--ch-body-14-500-font-weight);
        color: var(--neutral-800);
        font-size: var(--ch-body-14-500-font-size);
        letter-spacing: var(--ch-body-14-500-letter-spacing);
        line-height: var(--ch-body-14-500-line-height);
        font-style: var(--ch-body-14-500-font-style);
    }

    .frame .frame-wrapper-2 {
        display: flex;
        height: 48px;
        align-items: center;
        gap: 8px;
        padding: 5px 16px;
        position: relative;
        align-self: stretch;
        width: 100%;
    }

    .frame .div-8 {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        position: relative;
        flex: 0 0 auto;
    }

    .frame .div-wrapper-3 {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 8px 24px;
        position: relative;
        flex: 0 0 auto;
        border-radius: 2px;
        background: linear-gradient(180deg,
                rgb(255, 245.33, 158.31) 0%,
                rgb(255, 225.12, 125.34) 2.6%,
                rgb(255, 193.23, 73.31) 60.42%,
                rgb(255, 190.14, 23.37) 100%);
    }

    .frame .text-wrapper-7 {
        cursor: pointer;
        position: relative;
        width: fit-content;
        margin-top: -1.00px;
        font-family: "PingFang SC-Medium", Helvetica;
        font-weight: 500;
        color: #000000;
        font-size: 14px;
        letter-spacing: 0;
        line-height: normal;
    }

    .frame .div-9 {
        display: inline-flex;
        align-items: flex-start;
        gap: 24px;
        position: relative;
        flex: 0 0 auto;
    }

    .frame .img-2 {
        position: relative;
        width: 42px;
        height: 42px;
        margin-top: -2.00px;
        margin-bottom: -3.00px;
        object-fit: cover;
    }

    .frame .image {
        position: relative;
        width: 42px;
        height: 42px;
        margin-top: -2.00px;
        margin-bottom: -3.00px;
        margin-right: -4.00px;
        object-fit: cover;
    }

    .frame .div-10 {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        position: relative;
        align-self: stretch;
        width: 100%;
        flex: 0 0 auto;
        border-radius: 12px;
        border: 1px solid;
        border-color: #0000001a;
        max-height: 500px;
        overflow-y: auto;
    }

    .frame .element {
        position: relative;
        width: 56px;
        height: 56px;
        margin-top: -2.00px;
        margin-bottom: -6.00px;
        margin-left: -4.00px;
        object-fit: cover;
    }

    .frame .div-11 {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 8px;
        padding: 16px;
        position: relative;
        align-self: stretch;
        width: max-content;
        min-height: 48px;
        max-width: 100%;
    }

    .frame .div-wrapper-4 {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        padding: 4px 16px;
        position: relative;
        flex: 0 0 auto;
        margin: 4px;
        border-radius: 999px;
        border: 1px solid;
        border-color: var(--variable-collection-flash);
    }

    .frame .text-wrapper-8 {
        width: fit-content;
        margin-top: -1.00px;
        font-family: "Noto Sans SC-Medium", Helvetica;
        font-weight: 500;
        color: var(--variable-collection-flash);
        letter-spacing: 0.14px;
        line-height: 24px;
        white-space: nowrap;
        position: relative;
        font-size: 14px;
    }

    .frame .div-12 {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 17px;
        padding: 8px 16px 16px;
        position: relative;
        align-self: stretch;
        width: 100%;
        flex: 0 0 auto;
    }

    .frame .frame-wrapper-3 {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        gap: 12px;
        position: relative;
        align-self: stretch;
        width: 100%;
        flex: 0 0 auto;
    }

    .frame .div-13 {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        position: relative;
        align-self: stretch;
        width: 100%;
        flex: 0 0 auto;
        border-radius: 4px;
        border: 1px solid;
        border-color: #221c121a;
    }

    .frame .text-wrapper-9 {
        flex: 1;
        font-family: "Noto Sans SC-Regular", Helvetica;
        font-weight: 400;
        color: #000000;
        letter-spacing: 0;
        line-height: 20px;
        position: relative;
        font-size: 14px;
    }

    .frame .div-14 {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        position: relative;
        flex: 0 0 auto;
    }

    .frame .label-2 {
        color: var(--primary-red-500);
        font-size: var(--ch-body-14-500-font-size);
        position: relative;
        width: fit-content;
        margin-top: -1.00px;
        font-family: var(--ch-body-14-500-font-family);
        font-weight: var(--ch-body-14-500-font-weight);
        text-align: center;
        letter-spacing: var(--ch-body-14-500-letter-spacing);
        line-height: var(--ch-body-14-500-line-height);
        white-space: nowrap;
        font-style: var(--ch-body-14-500-font-style);
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .frame .label-2:hover {
        background-color: rgba(255, 93, 93, 0.1);
    }

    .frame .label-2:active {
        background-color: rgba(255, 93, 93, 0.2);
        transform: scale(0.98);
    }

    .frame .label-2.copied {
        animation: copied 0.5s ease;
    }

    @keyframes copied {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.1);
        }
        100% {
            transform: scale(1);
        }
    }

    .frame .line {
        position: relative;
        width: 1px;
        height: 20px;
        object-fit: cover;
    }

    .frame .scan {
        position: relative;
        width: 16px;
        height: 16px;
    }

    .frame .frame-wrapper-4 {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        gap: 12px;
        position: relative;
        align-self: stretch;
        width: 100%;
        flex: 0 0 auto;
        background-color: #d9d9d980;
    }

    .frame .label-wrapper {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        padding: 0px 8px;
        position: relative;
        flex: 0 0 auto;
        background-color: var(--variable-collection);
        border-radius: 4px;
    }

    .frame .label-3 {
        color: var(--variable-collection-1);
        font-size: 12px;
        position: relative;
        width: fit-content;
        margin-top: -1.00px;
        font-family: "Noto Sans SC-Medium", Helvetica;
        font-weight: 500;
        text-align: center;
        letter-spacing: 0;
        line-height: 22px;
        white-space: nowrap;
    }

    .frame .label-4 {
        color: #7f7f7f;
        font-size: var(--ch-body-14-500-font-size);
        position: relative;
        width: fit-content;
        margin-top: -1.00px;
        font-family: var(--ch-body-14-500-font-family);
        font-weight: var(--ch-body-14-500-font-weight);
        text-align: center;
        letter-spacing: var(--ch-body-14-500-letter-spacing);
        line-height: var(--ch-body-14-500-line-height);
        white-space: nowrap;
        font-style: var(--ch-body-14-500-font-style);
    }

    .img-2.selected {
      border: 2px solid var(--primary-red-500);
      border-radius: 8px;
    }

    /* Add responsive styles */
    @media screen and (max-width: 768px) {
      .frame {
        flex-direction: column;
        padding: 12px;
        gap: 16px;
        width: 100%;
      }

      .frame .frame-wrapper {
        width: 100%; 
        padding: 0;
      }

      .frame .div {
        height: auto;
      }

      .frame > div:nth-child(2) {
        margin-top: 16px;
        width: 100%;
      }

      .frame .div-3,
      .frame .div-10 {
        max-height: fit-content;
        height: auto; /* Allow height to adjust */
        min-height: unset;
      }

      .frame .div-4 {
        flex-direction: column;
        align-items: flex-start;
        padding: 16px 12px;
      }

      .frame .div-6 {
        width: 100%;
        justify-content: space-between;
        margin-top: 8px;
      }

      .frame .text-wrapper-2 {
        font-size: 18px;
        line-height: 24px;
      }

      .frame .button {
        padding: 8px 12px;
      }

      .frame .label {
        font-size: 14px;
      }

      /* Adjust third party app icons grid */
      .frame .div-9 {
        flex-wrap: wrap;
        gap: 16px;
        justify-content: flex-start;
      }

      .frame .img-2 {
        width: 36px;
        height: 36px;
      }

      /* Adjust subscription URLs section */
      .frame .div-13 {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        padding: 12px;
      }

      .frame .div-14 {
        width: fit-content;
        justify-content: flex-end;
      }

      .frame .text-wrapper-9 {
        font-size: 14px;
        line-height: 20px;
      }

      /* Adjust download buttons */
      .frame .div-11 {
        padding: 12px;
      }

      .frame .div-wrapper-4 {
        width: 100%;
        margin: 4px 0;
      }

      .frame .text-wrapper-8 {
        width: 100%;
        text-align: center;
      }

      /* Improve touch targets */
      .frame .label-2 {
        padding: 8px 12px;
        font-size: 14px;
      }

      /* Adjust version info section */
      .frame .div-7 {
        padding: 12px;
      }

      .frame .text-wrapper-6 {
        width: 100%;
      }

      /* Make copy and QR code buttons more touch-friendly */
      .frame .div-14 .label-2 {
        min-height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .frame .scan {
        width: 20px;
        height: 20px;
      }

      /* Improve spacing between sections */
      .frame .div-2 {
        margin-bottom: 8px;
      }

      .frame .text-wrapper {
        font-size: 16px;
      }

      /* Adjust description text */
      .frame .div-wrapper-2 {
        padding: 12px;
      }

      .frame .p {
        font-size: 14px;
        line-height: 20px;
      }
    }

    /* Additional styles for extra small screens */
    @media screen and (max-width: 375px) {
      .frame {
        padding: 8px;
      }

      .frame .text-wrapper-2 {
        font-size: 16px;
      }

      .frame .div-6 {
        flex-wrap: wrap;
        gap: 8px;
      }

      .frame .button {
        width: 100%;
        justify-content: center;
      }
    }
  `]
})
export class AppVersionsInfoComponent implements OnInit, OnChanges {
  @Input() public allVersionArr: any;
  @Input() public platform: string;
  @Input() public describe: string;
  @Input() public deliveryIPs: string[] = [];
  @Input() public deliverySubDomains: string[] = [];
  @Input() public deliveryDownloadDomains: string[] = [];
  @Input() public thirdPartyAppList: string[] = [];
  @Input() public thirdPartyAppDownloadUrls: any[] = [];
  @Input() public storeUrl: string;
  private _subscriptionPath: string;
  public subscriptionUrls: any[] = [];  // 缓存订阅地址列表
  public isIosThirdPartyApp: boolean = false;
  public isThirdPartyApp: string[] = [
    "Stash",
    "Shadowrocket",
    "Quantumult X"
  ];

  @Input()
  set subscriptionPath(value: string) {
    if (this._subscriptionPath !== value) {  // 只在值真正变化时更新
      this._subscriptionPath = value.replace(/^https?:\/\/[^\/]+/, '');
      this.updateSubscriptionUrls();  // 更新订阅地址列表
    }
  }

  get subscriptionPath(): string {
    return this._subscriptionPath;
  }

  // 更新订阅地址列表
  private updateSubscriptionUrls() {

    if (!this._subscriptionPath) {
      this.subscriptionUrls = [];
      return;
    }

    const urls = [];

    if (this.isValidDomain(0)) {
      urls.push({
        name: '线路1',
        lineIcon: '❶',
        url: `https://${this.deliverySubDomains[0]}${this._subscriptionPath}`
      });
    }

    // 添加deliveryIPs的订阅地址
    if (this.isValidDomain(1)) {
      urls.push({
        name: '线路2',
        lineIcon: '➋',
        url: `https://${this.deliverySubDomains[1]}${this._subscriptionPath}`
      });
    }

    if (this.isValidDomain(2)) {
      urls.push({
        name: '线路3',
        lineIcon: '➌',
        url: `https://${this.deliverySubDomains[2]}${this._subscriptionPath}`
      });
    }

    this.subscriptionUrls = urls;
  }

  // 获取订阅地址列表
  getSubscriptionUrls(): any[] {
    return this.subscriptionUrls;
  }

  // 也监听deliveryIPs的变化，因为它也会影响订阅地址
  ngOnChanges(changes: SimpleChanges) {
    if (changes['deliveryIPs'] && !changes['deliveryIPs'].firstChange) {
      this.updateSubscriptionUrls();
    }
  }

  public selectedApp: string = ''; // 记录当前选中的应用

  constructor(
    public qrDialogService: QrDialogService,  // 改为public
    public appService: AppService,
  ) { }

  async ngOnInit() {
    // 默认选中第一个应用
    if (this.thirdPartyAppList && this.thirdPartyAppList.length > 0) {
      this.selectedApp = this.thirdPartyAppList[0];
    }

    // 初始化时也更新一次订阅地址
    this.updateSubscriptionUrls();
  }

  // 选择应用
  selectApp(app: string) {
    this.selectedApp = app;
    this.isIosThirdPartyApp = this.isThirdPartyApp.includes(app);
  }

  // 判断IP是否有效
  isValidIP(index: number): boolean {
    return this.deliveryIPs &&
      Array.isArray(this.deliveryIPs) &&
      this.deliveryIPs.length > index &&
      this.deliveryIPs[index] &&
      this.deliveryIPs[index].trim() !== '';
  }

  isValidDomain(index: number): boolean {
    return this.deliverySubDomains &&
      Array.isArray(this.deliverySubDomains) &&
      this.deliverySubDomains.length > index &&
      this.deliverySubDomains[index] &&
      this.deliverySubDomains[index].trim() !== '';
  }

  // 获取应用图标路径
  getAppIconPath(app: string): string {
    const appLower = app.toLowerCase().replace(' ', '');
    switch (appLower) {
      case 'stash':
        return `assets/images/apps/${appLower}.png`;
      case 'clash-x32':
        return 'assets/images/apps/clash-x32.png  ';
      case 'clash-x64':
        return 'assets/images/apps/clash-x64.png';
      case 'clash-m':
        return 'assets/images/apps/clash-m.png';
      case 'clash-intel':
        return 'assets/images/apps/clash-intel.png';
      default:
        return `assets/images/apps/${appLower}.svg`;
    }
  }

  // 获取当前选中应用的下载地址列表
  getAppDownloadUrls(): any[] {
    if (!this.selectedApp || !this.thirdPartyAppDownloadUrls) return [];
    const appConfig = this.thirdPartyAppDownloadUrls.find(
      config => config.app.toLowerCase() === this.selectedApp.toLowerCase()
    );

    if (!appConfig) return [];

    return appConfig.platforms.filter(p => p.platform.toLowerCase() === this.platform.toLowerCase());
  }

  // 判断是否是完整URL
  isFullUrl(url: string): boolean {
    return url.startsWith('http://') || url.startsWith('https://');
  }

  // 获取当前网页的domain
  getCurrentDomain(): string {
    return window.location.hostname;
  }

  // 获取完整的下载URL列表
  getFullDownloadUrls(downloadInfo: any): any[] {
    if (this.isFullUrl(downloadInfo.url)) {
      return [{ name: downloadInfo.name, url: downloadInfo.url }];
    }

    const urls = [];


    // 添加deliveryDownloadDomains的下载地址
    if (this.isValidDomain(0)) {
      urls.push({
        name: `${downloadInfo.name} - 线路1`,
        url: `https://${this.deliveryDownloadDomains[0]}${downloadInfo.url}`
      });
    }

    if (this.isValidDomain(1)) {
      urls.push({
        name: `${downloadInfo.name} - 线路2`,
        url: `https://${this.deliveryDownloadDomains[1]}${downloadInfo.url}`
      });
    }

    return urls;
  }

  generateQrCode(url: string, subscriptionName: string) {
    url = url + '?agent=' + this.appToAgent(this.selectedApp);
    this.qrDialogService.showQRCode(url, subscriptionName);
  }
  // 复制文本到剪贴板
  copyToClipboard(text: string, event: any) {
    text = text + '?agent=' + this.appToAgent(this.selectedApp);
    navigator.clipboard.writeText(text);

    this.appService.snackUp('复制成功');
  }

  appToAgent(appName: string): string {
    switch (appName) {
      case 'Stash':
        return 'clash-fallback';
      case 'Shadowrocket':
        return 'shadowrocket';
      case 'shadowrocket':
        return 'shadowrocket';
      case 'Quantumult X':
        return 'quantumultx';
      default:
        return 'clash-hysteria';
    }
  }

  // 生成二维码
  async generateQRCode(text: string): Promise<string> {
    try {
      return await qrcode.toDataURL(text);
    } catch (err) {
      console.error('Error generating QR code:', err);
      return '';
    }
  }

}
