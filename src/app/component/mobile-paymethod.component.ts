import { ChangeDetectorRef, Component, EventEmitter, Input, Output } from "@angular/core";
import { PaymentMethodService } from "@flashvpn-io/web-core";
import { PayMethod, PayType } from "@flashvpn-io/web-core";
import { BehaviorSubject, merge, Subject } from "rxjs";
import { filter, map, switchMap } from "rxjs/operators";

@Component({
  selector: "app-mobile-paymethod",
  template: `
    <mat-nav-list>
      <a mat-list-item *ngFor="let item of filteredPayMethods$ | async" (click)="setCurrentMethod($event,item)">
        <img [src]="item.imgUrl" alt="" />
        <span style="padding-left: 10px">{{ item.payName }}</span>
      </a>
    </mat-nav-list>
  `,
  styles: [
    `
      /deep/ .mat-bottom-sheet-container {
        border-radius: 8px 8px 0 0;
      }
      .list-button {
        margin-top: 20px;
        margin-bottom: 30px;
      }
      /deep/ .list-button .mat-list-item-content {
        justify-content: center;
        background: #e2e8f0;
        border-radius: 8px;
      }
      .box {
        background-color: #ffffff;
        width: 100%;
        height: 50px;
      }
    `,
  ],
})
export class MobilePaymethodComponent {

  @Input() excludePayType: PayType[] = [];
  @Output() payMethodSelected = new EventEmitter<PayMethod>();

  filteredPayMethods$: BehaviorSubject<PayMethod[]> = new BehaviorSubject<PayMethod[]>([]);

  constructor(
    public paymentMethodService: PaymentMethodService,
    private cdr: ChangeDetectorRef
  ) { }
  ngAfterViewInit(): void {
    this.paymentMethodService.paymentMethods$
      .pipe(
        filter((payMethods) => payMethods !== null && payMethods.length > 0),
        map((payMethods) => payMethods.filter((method) => !this.excludePayType.includes(method.payType)))
      )
      .subscribe((filteredMethods) => {
        this.filteredPayMethods$.next(filteredMethods as PayMethod[]);
        // somehow the filteredPayMethods$ is not updating
        this.cdr.detectChanges();
      });
  }
  setCurrentMethod($event: any, item: PayMethod) {
    this.paymentMethodService.currentMethod$.next(item);
    this.payMethodSelected.emit(item);
  }
}
