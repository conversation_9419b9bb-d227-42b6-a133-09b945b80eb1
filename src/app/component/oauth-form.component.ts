import { Component, EventEmitter, Input, Output } from "@angular/core";
import { BehaviorSubject } from "rxjs";
import { emailList } from "../../constants/email-list.constants";
import { AppService } from "../app.service";
import { OauthType } from "@flashvpn-io/web-core";

@Component({
  selector: "app-oauth-form",
  template: `
    <div class="oauthForm">
      <div id="tipsInfo" class="none-tips">
        <mat-icon>error</mat-icon>
        {{ tipsInfo }}
      </div>
      <form #form="ngForm" id="oauthForm">
        <mat-form-field class="form-input" style="margin-bottom: 22px" floatLabel="never" appearance="fill" empty="false" autofilled="false">
          <svg MatPrefix style="margin-right: 20px;" width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M20 2C20 0.9 19.1 0 18 0H2C0.9 0 0 0.9 0 2V14C0 15.1 0.9 16 2 16H18C19.1 16 20 15.1 20 14V2ZM18 2L10 7L2 2H18ZM10 9L2 4V14H18V4L10 9Z"
              fill="black"
              fill-opacity="0.54" />
          </svg>
          <input
            id="emailInput"
            matInput
            i18n-placeholder
            placeholder="Please enter your account number"
            (input)="sendNewValue()"
            (focus)="emailGetFocus(true)"
            (blur)="emailGetFocus(false)"
            [(ngModel)]="email"
            name="email" />
        </mat-form-field>
        <mat-nav-list class="email-list" *ngIf="emailListShow">
          <a id="sign-in-email-tips" href="javascript:void(0)" mat-list-item *ngFor="let item of emailTips" (mousedown)="setEmail($event, item)">
            <span mat-line>{{ email.indexOf("@") > -1 ? email.substring(0, email.indexOf("@") + 1) + item : email + "@" + item }}</span>
          </a>
        </mat-nav-list>
        <mat-form-field class="form-input" floatLabel="never" appearance="fill" empty="false" autofilled="false">
          <svg MatPrefix style="margin-right: 20px;" width="16" height="22" viewBox="0 0 16 22" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M13 7.5H14C15.1 7.5 16 8.4 16 9.5V19.5C16 20.6 15.1 21.5 14 21.5H2C0.9 21.5 0 20.6 0 19.5V9.5C0 8.4 0.9 7.5 2 7.5H3V5.5C3 2.74 5.24 0.5 8 0.5C10.76 0.5 13 2.74 13 5.5V7.5ZM8 2.5C6.34 2.5 5 3.84 5 5.5V7.5H11V5.5C11 3.84 9.66 2.5 8 2.5ZM2 19.5V9.5H14V19.5H2ZM10 14.5C10 15.6 9.1 16.5 8 16.5C6.9 16.5 6 15.6 6 14.5C6 13.4 6.9 12.5 8 12.5C9.1 12.5 10 13.4 10 14.5Z"
              fill="black"
              fill-opacity="0.54" />
          </svg>

          <input
            (keyup.enter)="onSubmit.emit()"
            id="passwordInput"
            (input)="passwordChange()"
            (blur)="passwordGetFocus()"
            matInput
            i18n-placeholder
            placeholder="Enter your password"
            [type]="hide ? 'password' : 'text'"
            [(ngModel)]="password"
            name="password" />
          <a
            mat-icon-button
            matSuffix
            style="margin-left: 20px;display: flex;align-items: center;justify-content: center;"
            (click)="hide = !hide"
            [attr.aria-label]="'Hide password'"
            [attr.aria-pressed]="hide">
            <mat-icon>{{ hide ? "visibility_off" : "visibility" }}</mat-icon>
          </a>
        </mat-form-field>
        <div class="forgot-pwd" *ngIf="oAuthType === OauthType.SIGNIN">
          <a id="sign-in-forget-password" style="text-decoration-line: underline;color: #FF5E5E;" routerLink="/users/forget-password" i18n>
            忘记密码
          </a>
        </div>
      </form>
    </div>
  `,
  styles: [
    `
      .oauthForm {
        width: 420px;
      }
      .none-tips {
        display: none;
      }
      :host ::ng-deep .mat-form-field-underline {
        display: none !important;
      }
      :host ::ng-deep .mat-form-field-wrapper {
        padding-bottom: 0;
        height: 100%;
      }
      :host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
        padding: 0 12px;
        height: 100%;
        align-items: center;
      }
      :host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-infix {
        padding: 0;
        margin: 0;
        border: 0;
        display: flex;
        align-items: center;
      }
      .form-input {
        width: 420px;
        height: 56px;
      }
      .email-list {
        position: absolute;
        width: 420px;
        font-size: 14px;
        z-index: 99 !important;
        border-radius: 8px;
        background: #ffffff;
        box-shadow: 11px 11px 22px #9b9b9b, -11px -11px 22px #ffffff;
        margin-top: -15px;
      }
      .email-list .mat-list-item {
        height: 32px;
        font-size: 14px;
      }
      .input-tips {
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 10px;
        gap: 9px;
        width: 420px;
        height: 38px;
        background: rgba(58, 144, 246, 0.2);
        border: 1px solid #3a73f6;
        border-radius: 2px;
        flex: none;
        order: 0;
        flex-grow: 0;
        margin-bottom: 10px;
        color: #3a73f6;
      }
      .input-tips .mat-icon {
        width: 18px;
        height: 18px;
      }
      .input-tips .material-icons {
        font-size: 18px;
      }
      .fade-in-fwd {
        -webkit-animation: fade-in-fwd 0.3s cubic-bezier(0.39, 0.575, 0.565, 1) both;
        animation: fade-in-fwd 0.3s cubic-bezier(0.39, 0.575, 0.565, 1) both;
      }
      @-webkit-keyframes fade-in-fwd {
        0% {
          -webkit-transform: translateZ(-80px);
          transform: translateZ(-80px);
          opacity: 0;
        }
        100% {
          -webkit-transform: translateZ(0);
          transform: translateZ(0);
          opacity: 1;
        }
      }
      @keyframes fade-in-fwd {
        0% {
          -webkit-transform: translateZ(-80px);
          transform: translateZ(-80px);
          opacity: 0;
        }
        100% {
          -webkit-transform: translateZ(0);
          transform: translateZ(0);
          opacity: 1;
        }
      }
      .fade-out-bck {
        -webkit-animation: fade-out-bck 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
        animation: fade-out-bck 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      }
      @-webkit-keyframes fade-out-bck {
        0% {
          -webkit-transform: translateZ(0);
          transform: translateZ(0);
          opacity: 1;
        }
        100% {
          -webkit-transform: translateZ(-80px);
          transform: translateZ(-80px);
          opacity: 0;
        }
      }
      @keyframes fade-out-bck {
        0% {
          -webkit-transform: translateZ(0);
          transform: translateZ(0);
          opacity: 1;
        }
        100% {
          -webkit-transform: translateZ(-80px);
          transform: translateZ(-80px);
          opacity: 0;
        }
      }
      .slide-bottom {
        -webkit-animation: slide-bottom 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
        animation: slide-bottom 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      }
      @-webkit-keyframes slide-bottom {
        0% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
        }
        100% {
          -webkit-transform: translateY(18px);
          transform: translateY(18px);
        }
      }
      @keyframes slide-bottom {
        0% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
        }
        100% {
          -webkit-transform: translateY(18px);
          transform: translateY(18px);
        }
      }
      .slide-top {
        -webkit-animation: slide-top 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
        animation: slide-top 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      }
      @-webkit-keyframes slide-top {
        0% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
        }
        100% {
          -webkit-transform: translateY(-48px);
          transform: translateY(-48px);
        }
      }
      @keyframes slide-top {
        0% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
        }
        100% {
          -webkit-transform: translateY(-48px);
          transform: translateY(-48px);
        }
      }
      .forgot-pwd {
        width: 100%;
        margin-top: 8px;
        display: flex;
        justify-content: flex-end;
      }
      @media screen and (max-width: 599px) {
        .form-input {
          width: 100%;
          height: 56px;
        }
        .email-list {
          width: 340px;
        }
        .input-tips {
          width: 100%;
        }
        .oauthForm {
          width: 100%;
        }
      }
    `,
  ],
})
export class OauthFormComponent {
  public hide = true;
  public email: string;
  public password: string;
  public emailError = false;
  public passwordError = false;
  public tipsInfo: string;
  public passwordErrorInfo: string;
  public emailErrorInfo: string;
  public emailListShow = false;
  public emailTips: string[];

  public isDisabled$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);

  @Input() oAuthType: OauthType = OauthType.SIGNUP;

  @Output() onSubmit = new EventEmitter();

  constructor(public appService: AppService) { }

  setEmail(event, item) {
    event?.preventDefault();
    this.email = this.email.indexOf("@") > -1 ? this.email.substring(0, this.email.indexOf("@") + 1) + item : this.email + "@" + item;
    this.emailListShow = false;
    this.updateCheckDisabled();
  }

  setEmailError(error, tips) {
    if (error) {
      document.getElementById("emailInput").style.color = "#FF5E5E";
      this.emailErrorInfo = tips;
      this.tipsInfo = tips;
      document.getElementById("tipsInfo").className = "input-tips fade-in-fwd";
      document.getElementById("oauthForm").className = "slide-bottom";
    } else if (this.emailError && !error) {
      if (this.passwordError) {
        this.emailErrorInfo = null;
        this.tipsInfo = this.passwordErrorInfo;
        document.getElementById("emailInput").style.color = "";
      } else {
        document.getElementById("tipsInfo").className = "input-tips fade-out-bck";
        document.getElementById("oauthForm").className = "slide-top";
        document.getElementById("emailInput").style.color = "";
        this.emailErrorInfo = null;
        this.tipsInfo = null;
      }
    }
    this.emailError = error;
    this.updateCheckDisabled();
  }

  setPasswordError(error, tips) {
    if (error) {
      document.getElementById("passwordInput").style.color = "#FF5E5E";
      this.passwordErrorInfo = tips;
      this.tipsInfo = tips;
      document.getElementById("tipsInfo").className = "input-tips fade-in-fwd";
      document.getElementById("oauthForm").className = "slide-bottom";
    } else if (this.passwordError && !error) {
      if (this.emailError) {
        this.passwordErrorInfo = null;
        this.tipsInfo = this.emailErrorInfo;
        document.getElementById("passwordInput").style.color = "";
      } else {
        document.getElementById("tipsInfo").className = "input-tips fade-out-bck";
        document.getElementById("oauthForm").className = "slide-top";
        document.getElementById("passwordInput").style.color = "";
        this.passwordErrorInfo = null;
        this.tipsInfo = null;
      }
    }
    this.passwordError = error;
    this.updateCheckDisabled();
  }

  emailGetFocus(value) {
    if (value) {
      this.emailPrompt();
    } else {
      if (this.email && !this.emailError) {
        this.emailListShow = false;
        const reg = /^\w+((.\w+)|(-\w+))@[A-Za-z0-9]+((.|-)[A-Za-z0-9]+).[A-Za-z0-9]+$/;
        this.setEmailError(!reg.test(this.email), this.appService.translate("EmailFormatError"));
      }
    }
    this.updateCheckDisabled();
  }

  passwordGetFocus() {
    const errorInfo = this.checkPassword();
    if (this.oAuthType === OauthType.SIGNUP && this.password && this.password.length < 8) {
      errorInfo.isError = true;
      errorInfo.errorTips = this.appService.translate("PasswordShortError");
    }
    this.setPasswordError(errorInfo.isError, errorInfo.errorTips);
  }

  emailPrompt() {
    if (this.email && this.email.indexOf("@") > -1) {
      this.emailTips = emailList.filter((emailStr) => {
        return emailStr.indexOf(this.email.substring(this.email.indexOf("@") + 1, this.email.length)) > -1;
      });
    } else {
      this.emailTips = emailList;
    }
    this.emailListShow = this.email && this.emailTips && this.emailTips.length > 0 && !this.emailError ? true : false;
  }

  sendNewValue() {
    const errorInfo = this.checkEmail();
    if (!errorInfo.isError && this.oAuthType === OauthType.SIGNIN) {
      this.setPasswordError(errorInfo.isError, errorInfo.errorTips);
    }
    this.setEmailError(errorInfo.isError, errorInfo.errorTips);
    this.emailPrompt();
    this.updateCheckDisabled();
  }

  passwordChange() {
    const errorInfo = this.checkPassword();
    if (!errorInfo.isError && this.oAuthType === OauthType.SIGNIN) {
      this.setEmailError(errorInfo.isError, errorInfo.errorTips);
    }
    this.setPasswordError(errorInfo.isError, errorInfo.errorTips);
    this.updateCheckDisabled();
  }

  updateCheckDisabled() {
    const isDisabled = !(this.email && this.password && !this.emailError && !this.passwordError);
    this.isDisabled$.next(isDisabled);
  }

  checkEmail() {
    const errorInfo = {
      isError: false,
      errorTips: null,
    };
    const reg = /(^\s+)|(\s+$)|\s+|\++/g;
    const isReg = reg.test(this.email);
    if (!/foxit.io/.test(this.email) && isReg) {
      errorInfo.isError = true;
      errorInfo.errorTips = this.appService.translate("EmailIllegalCharacters");
      return errorInfo;
    }
    if (this.oAuthType === OauthType.SIGNUP && this.email && this.email.length > 40) {
      errorInfo.isError = true;
      errorInfo.errorTips = this.appService.translate("EmailLongError");
      return errorInfo;
    }
    return errorInfo;
  }

  checkPassword() {
    const errorInfo = {
      isError: false,
      errorTips: null,
    };
    if (this.oAuthType === OauthType.SIGNUP) {
      const reg = /(^\s+)|(\s+$)|\s+|\++/g;
      const isReg = reg.test(this.password);
      const reg2 = /[\u4e00-\u9fa5]+/g;
      const isReg2 = reg2.test(this.password);
      if (isReg || isReg2) {
        errorInfo.isError = true;
        errorInfo.errorTips = this.appService.translate("PasswordIllegalCharacters");
        return errorInfo;
      }
      if (this.password && this.password.length > 20) {
        errorInfo.isError = true;
        errorInfo.errorTips = this.appService.translate("PasswordLongError");
        return errorInfo;
      }
    }
    return errorInfo;
  }

  protected readonly OauthType = OauthType;
}
