import { Component, Input, OnInit } from "@angular/core";
import { DateRange } from "./date-picker.component";
import * as moment from 'moment';

export interface TrafficLogItem {
  date: string;
  usage: number;
  maxUsage: number;
  displayUsage: string;
}

@Component({
  selector: "app-traffic-log",
  template: `
    <div class="traffic-log">
      <div class="header">
        <div class="title">流量日志</div>
        <div class="date-range" (click)="openDatePicker()">
          <div class="date-selector">
            <div class="date-text">
              <span class="start-date">{{ startDate }}</span>
              <span class="separator">-</span>
              <span class="end-date">{{ endDate }}</span>
            </div>
            <div class="usage-info">
              <span class="usage-text">{{ currentUsage }}/{{ maxUsage }}</span>
            </div>
            <div class="chevron-icon">
              <svg width="10" height="5" viewBox="0 0 10 5" fill="none">
                <path d="M0 0L5 5L10 0" stroke="#808080" stroke-width="1"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
      
      <div class="log-content">
        <div class="log-item" *ngFor="let item of logItems">
          <div class="item-left">
            <div class="date">{{ item.date }}</div>
            <div class="usage-bar">
              <div class="bar-background">
                <div 
                  class="bar-fill" 
                  [style.width.%]="(item.usage / item.maxUsage) * 100"
                ></div>
              </div>
            </div>
          </div>
          <div class="item-right">
            <div class="usage-amount">{{ item.displayUsage }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Date Picker Modal -->
    <app-date-picker 
      [isVisible]="showDatePicker"
      [initialStartDate]="startDate"
      [initialEndDate]="endDate"
      (dateSelected)="onDateSelected($event)"
      (closed)="onDatePickerClosed()">
    </app-date-picker>
  `,
  styles: [
    `
      .traffic-log {
        background-color: #ffffff;
        box-shadow: 0px 0px 10px 0px rgba(255, 94, 94, 0.1);
        width: 100%;
        height: 100%;
        padding: 32px 24px 0;
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .header {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .title {
        font-family: 'PingFang SC', sans-serif;
        font-size: 16px;
        font-weight: 600;
        line-height: 16px;
        color: #000000;
      }

      .date-range {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        width: 100%;
        cursor: pointer;
        transition: border-color 0.2s ease;
      }

      .date-range:hover {
        border-color: #ff5e5e;
      }

      .date-selector {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;
      }

      .date-text {
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: 'PingFang SC', sans-serif;
        font-size: 14px;
        font-weight: 600;
        color: #808080;
      }

      .separator {
        font-family: 'Montserrat', sans-serif;
        font-weight: bold;
      }

      .usage-info {
        display: flex;
        align-items: center;
        background-color: rgba(128, 128, 128, 0.1);
        border-radius: 20px;
        padding: 4px 7px;
        height: 24px;
      }

      .usage-text {
        font-family: 'PingFang SC', sans-serif;
        font-size: 14px;
        color: #808080;
        white-space: nowrap;
      }

      .chevron-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        transform: rotate(90deg);
      }

      .log-content {
        height: 412px;
        overflow-y: auto;
        padding-right: 14px;
      }

      .log-content::-webkit-scrollbar {
        width: 4px;
      }

      .log-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
      }

      .log-content::-webkit-scrollbar-thumb {
        background: #ff5e5e;
        border-radius: 2px;
        opacity: 0.6;
      }

      .log-content::-webkit-scrollbar-thumb:hover {
        background: #e54e4e;
        opacity: 1;
      }

      .log-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0;
        margin-bottom: 28px;
        width: 100%;
      }

      .item-left {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .date {
        font-family: 'PingFang SC', sans-serif;
        font-size: 14px;
        font-weight: 500;
        color: #808080;
        width: 63px;
        text-align: left;
      }

      .usage-bar {
        width: 140px;
        height: 12px;
      }

      .bar-background {
        width: 100%;
        height: 100%;
        background-color: #f0f0f0;
        border-radius: 6px;
        overflow: hidden;
      }

      .bar-fill {
        height: 100%;
        background-color: #ff5e5e;
        border-radius: 6px;
        transition: width 0.3s ease;
      }

      .item-right {
        display: flex;
        align-items: center;
      }

      .usage-amount {
        font-family: 'Montserrat', sans-serif;
        font-size: 14px;
        font-weight: 500;
        color: #808080;
        white-space: nowrap;
      }

      /* Mobile responsive styles */
      @media (max-width: 768px) {
        .traffic-log {
          padding: 32px 24px 0;
        }

        .header {
          width: 100%;
        }

        .date-range {
          width: 100%;
        }

        .log-content {
          width: 100%;
          padding-right: 8px;
        }

        .log-content::-webkit-scrollbar {
          width: 3px;
        }

        .log-item {
          width: 100%;
        }
      }

      /* Extra small mobile screens */
      @media (max-width: 320px) {
        .header {
          width: 100%;
        }

        .date-range {
          width: 100%;
        }

        .log-content {
          width: 100%;
        }

        .log-item {
          width: 100%;
        }

        .usage-bar {
          width: 100px;
        }
      }
    `,
  ],
})
export class TrafficLogComponent implements OnInit {
  @Input() startDate: string = moment().subtract(1, 'month').format('YYYY.M.D');
  @Input() endDate: string = moment().endOf('day').format('YYYY.M.D');
  @Input() currentUsage: string = '20G';
  @Input() maxUsage: string = '50G';
  @Input() logItems: TrafficLogItem[] = [];

  showDatePicker: boolean = false;

  ngOnInit() {
    if (this.logItems.length === 0) {
      this.logItems = this.generateSampleData();
    }
  }

  openDatePicker() {
    this.showDatePicker = true;
  }

  onDateSelected(dateRange: DateRange) {
    this.startDate = dateRange.startDate;
    this.endDate = dateRange.endDate;
    // 这里可以添加重新加载数据的逻辑
    this.reloadTrafficData();
  }

  onDatePickerClosed() {
    this.showDatePicker = false;
  }

  private reloadTrafficData() {
    // 根据新的日期范围重新生成或加载数据
    // 在实际应用中，这里应该调用服务来获取新的数据
    this.logItems = this.generateSampleData();
  }

  private generateSampleData(): TrafficLogItem[] {
    const items: TrafficLogItem[] = [];
    const dates = [
      moment().subtract(1, 'days').format('YYYY.M.D'), moment().subtract(2, 'days').format('YYYY.M.D'), moment().subtract(3, 'days').format('YYYY.M.D'), moment().subtract(4, 'days').format('YYYY.M.D'), moment().subtract(5, 'days').format('YYYY.M.D'),
      moment().subtract(6, 'days').format('YYYY.M.D'), moment().subtract(7, 'days').format('YYYY.M.D'), moment().subtract(8, 'days').format('YYYY.M.D'), moment().subtract(9, 'days').format('YYYY.M.D'), moment().subtract(10, 'days').format('YYYY.M.D'),
      moment().subtract(11, 'days').format('YYYY.M.D'), moment().subtract(12, 'days').format('YYYY.M.D'), moment().subtract(13, 'days').format('YYYY.M.D'), moment().subtract(14, 'days').format('YYYY.M.D'), moment().subtract(15, 'days').format('YYYY.M.D')
    ];

    dates.forEach(date => {
      items.push({
        date: date,
        usage: 56,
        maxUsage: 100,
        displayUsage: '56MB'
      });
    });

    return items;
  }

  constructor() { }
}