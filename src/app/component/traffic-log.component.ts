import { Component, Input, OnInit } from "@angular/core";
import { DateRange } from "./date-picker.component";
import * as moment from 'moment';
import { ServiceService } from "@flashvpn-io/web-core";

export interface TrafficLogItem {
  date: string;
  usage: number;
  maxUsage: number;
  displayUsage: string;
}

@Component({
  selector: "app-traffic-log",
  template: `
    <div class="traffic-log">
      <div class="header">
        <div class="title">流量日志</div>
        <div class="date-range" (click)="openDatePicker()">
          <div class="date-selector">
            <div class="date-text">
              <span class="start-date">{{ startDate }}</span>
              <span class="separator">-</span>
              <span class="end-date">{{ endDate }}</span>
            </div>
            <div class="usage-info">
              <span class="usage-text">{{ currentUsage }}/{{ maxUsage }}</span>
            </div>
            <div class="chevron-icon">
              <svg width="10" height="5" viewBox="0 0 10 5" fill="none">
                <path d="M0 0L5 5L10 0" stroke="#808080" stroke-width="1"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
      
      <div class="log-content">
        <div *ngIf="isLoading" class="loading-container">
          <div class="loading-text">加载中...</div>
        </div>
        <ng-container *ngIf="!isLoading">
          <div *ngIf="logItems.length === 0" class="empty-container">
            <div class="empty-text">暂无流量数据</div>
          </div>
          <div class="log-item" *ngFor="let item of logItems">
            <div class="item-left">
              <div class="date">{{ item.date }}</div>
              <div class="usage-bar">
                <div class="bar-background">
                  <div
                    class="bar-fill"
                    [style.width.%]="(item.usage / item.maxUsage) * 100"
                  ></div>
                </div>
              </div>
            </div>
            <div class="item-right">
              <div class="usage-amount">{{ item.displayUsage }}</div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
    
    <!-- Date Picker Modal -->
    <app-date-picker 
      [isVisible]="showDatePicker"
      [initialStartDate]="startDate"
      [initialEndDate]="endDate"
      (dateSelected)="onDateSelected($event)"
      (closed)="onDatePickerClosed()">
    </app-date-picker>
  `,
  styles: [
    `
      .traffic-log {
        background-color: #ffffff;
        box-shadow: 0px 0px 10px 0px rgba(255, 94, 94, 0.1);
        width: 100%;
        height: 100%;
        padding: 32px 24px 0;
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .header {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .title {
        font-family: 'PingFang SC', sans-serif;
        font-size: 16px;
        font-weight: 600;
        line-height: 16px;
        color: #000000;
      }

      .date-range {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        width: 100%;
        cursor: pointer;
        transition: border-color 0.2s ease;
      }

      .date-range:hover {
        border-color: #ff5e5e;
      }

      .date-selector {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;
      }

      .date-text {
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: 'PingFang SC', sans-serif;
        font-size: 14px;
        font-weight: 600;
        color: #808080;
      }

      .separator {
        font-family: 'Montserrat', sans-serif;
        font-weight: bold;
      }

      .usage-info {
        display: flex;
        align-items: center;
        background-color: rgba(128, 128, 128, 0.1);
        border-radius: 20px;
        padding: 4px 7px;
        height: 24px;
      }

      .usage-text {
        font-family: 'PingFang SC', sans-serif;
        font-size: 14px;
        color: #808080;
        white-space: nowrap;
      }

      .chevron-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        transform: rotate(90deg);
      }

      .log-content {
        height: 412px;
        overflow-y: auto;
        padding-right: 14px;
      }

      .log-content::-webkit-scrollbar {
        width: 4px;
      }

      .log-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
      }

      .log-content::-webkit-scrollbar-thumb {
        background: #ff5e5e;
        border-radius: 2px;
        opacity: 0.6;
      }

      .log-content::-webkit-scrollbar-thumb:hover {
        background: #e54e4e;
        opacity: 1;
      }

      .log-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0;
        margin-bottom: 28px;
        width: 100%;
      }

      .item-left {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .date {
        font-family: 'PingFang SC', sans-serif;
        font-size: 14px;
        font-weight: 500;
        color: #808080;
        width: 63px;
        text-align: left;
      }

      .usage-bar {
        width: 140px;
        height: 12px;
      }

      .bar-background {
        width: 100%;
        height: 100%;
        background-color: #f0f0f0;
        border-radius: 6px;
        overflow: hidden;
      }

      .bar-fill {
        height: 100%;
        background-color: #ff5e5e;
        border-radius: 6px;
        transition: width 0.3s ease;
      }

      .item-right {
        display: flex;
        align-items: center;
      }

      .usage-amount {
        font-family: 'Montserrat', sans-serif;
        font-size: 14px;
        font-weight: 500;
        color: #808080;
        white-space: nowrap;
      }

      /* Mobile responsive styles */
      @media (max-width: 768px) {
        .traffic-log {
          padding: 32px 24px 0;
        }

        .header {
          width: 100%;
        }

        .date-range {
          width: 100%;
        }

        .log-content {
          width: 100%;
          padding-right: 8px;
        }

        .log-content::-webkit-scrollbar {
          width: 3px;
        }

        .log-item {
          width: 100%;
        }
      }

      /* Extra small mobile screens */
      @media (max-width: 320px) {
        .header {
          width: 100%;
        }

        .date-range {
          width: 100%;
        }

        .log-content {
          width: 100%;
        }

        .log-item {
          width: 100%;
        }

        .usage-bar {
          width: 100px;
        }
      }

      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px 0;
      }

      .loading-text {
        font-size: 14px;
        color: #666;
      }

      .empty-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px 0;
      }

      .empty-text {
        font-size: 14px;
        color: #999;
      }
    `,
  ],
})
export class TrafficLogComponent implements OnInit {
  @Input() startDate: string = moment().subtract(1, 'month').format('YYYY.M.D');
  @Input() endDate: string = moment().endOf('day').format('YYYY.M.D');
  @Input() currentUsage: string = '20G';
  @Input() maxUsage: string = '50G';
  @Input() logItems: TrafficLogItem[] = [];
  @Input() serviceId?: number; // 可选的服务ID，如果不传则使用当前服务

  showDatePicker: boolean = false;
  isLoading: boolean = false;

  ngOnInit() {
    if (this.logItems.length === 0) {
      this.loadTrafficData();
    }
  }

  openDatePicker() {
    this.showDatePicker = true;
  }

  onDateSelected(dateRange: DateRange) {
    this.startDate = dateRange.startDate;
    this.endDate = dateRange.endDate;
    // 重新加载数据
    this.loadTrafficData();
  }

  onDatePickerClosed() {
    this.showDatePicker = false;
  }

  private async loadTrafficData() {
    try {
      console.log("called===============")

      this.isLoading = true;

      // 获取服务ID：优先使用传入的serviceId，否则使用当前服务ID
      let targetServiceId: number;
      if (this.serviceId) {
        targetServiceId = this.serviceId;
      } else {
        // 从当前服务获取ID
        const currentService = this.serviceService.currentService$.value;
        if (!currentService) {
          throw new Error('没有可用的服务ID');
        }
        targetServiceId = currentService.id;
      }

      // 将日期格式从 'YYYY.M.D' 转换为 'YYYY-MM-DD' (API 需要的格式)
      const startDateFormatted = moment(this.startDate, 'YYYY.M.D').format('YYYY-MM-DD');
      const endDateFormatted = moment(this.endDate, 'YYYY.M.D').format('YYYY-MM-DD');


      // 调用 API 获取每日流量数据
      const dailyUsageData = await this.serviceService.fetchServiceDailyUsage(targetServiceId, startDateFormatted, endDateFormatted);

      console.log(dailyUsageData);

      // 处理 API 返回的数据并转换为组件需要的格式
      this.logItems = this.processApiData(dailyUsageData);

    } catch (error) {
      console.error('获取流量数据失败:', error);
      // 如果 API 调用失败，显示空数据
      this.logItems = [];
    } finally {
      this.isLoading = false;
    }
  }

  private processApiData(apiData: any): TrafficLogItem[] {
    if (!apiData || !Array.isArray(apiData)) {
      return []; // 返回空数组而不是模拟数据
    }

    return apiData.map(item => {
      // 处理 API 返回的数据格式
      const totalUsage = (item.upload || 0) + (item.download || 0);
      const usageInMB = totalUsage / (1024 * 1024); // 转换为 MB
      const usageInGB = totalUsage / (1024 * 1024 * 1024); // 转换为 GB

      let displayUsage: string;
      if (usageInGB >= 1) {
        displayUsage = `${usageInGB.toFixed(2)}GB`;
      } else {
        displayUsage = `${usageInMB.toFixed(0)}MB`;
      }

      return {
        date: moment(item.date).format('YYYY.M.D'),
        usage: usageInMB,
        maxUsage: 100, // 这个值可能需要从服务配置中获取
        displayUsage: displayUsage
      };
    });
  }



  constructor(private serviceService: ServiceService) { }
}