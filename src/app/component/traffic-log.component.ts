import { Component, Input, OnInit, OnD<PERSON>roy } from "@angular/core";
import { DateRange } from "./date-picker.component";
import * as moment from 'moment';
import { ServiceService } from "@flashvpn-io/web-core";
import { Subscription } from "rxjs";
import { filter, take } from "rxjs/operators";

export interface TrafficLogItem {
  date: string;
  usage: number;
  maxUsage: number;
  displayUsage: string;
}

@Component({
  selector: "app-traffic-log",
  template: `
    <div class="traffic-log">
      <div class="header">
        <div class="title">流量日志</div>
        <div class="date-range" (click)="openDatePicker()">
          <div class="date-selector">
            <div class="date-text">
              <span class="start-date">{{ startDate }}</span>
              <span class="separator">-</span>
              <span class="end-date">{{ endDate }}</span>
            </div>
            <div class="usage-info">
              <span class="usage-text">{{ calculatedCurrentUsage }}/{{ calculatedMaxUsage }}</span>
            </div>
            <div class="chevron-icon">
              <svg width="10" height="5" viewBox="0 0 10 5" fill="none">
                <path d="M0 0L5 5L10 0" stroke="#808080" stroke-width="1"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
      
      <div class="log-content">
        <div *ngIf="isLoading" class="loading-container">
          <div class="loading-text">加载中...</div>
        </div>
        <ng-container *ngIf="!isLoading">
          <div *ngIf="logItems.length === 0" class="empty-container">
            <div class="empty-text">暂无流量数据</div>
          </div>
          <div class="log-item" *ngFor="let item of logItems">
            <div class="item-left">
              <div class="date">{{ item.date }}</div>
              <div class="usage-bar">
                <div class="bar-background">
                  <div
                    class="bar-fill"
                    [style.width.%]="(item.usage / item.maxUsage) * 100"
                  ></div>
                </div>
              </div>
            </div>
            <div class="item-right">
              <div class="usage-amount">{{ item.displayUsage }}</div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
    
    <!-- Date Picker Modal -->
    <app-date-picker 
      [isVisible]="showDatePicker"
      [initialStartDate]="startDate"
      [initialEndDate]="endDate"
      (dateSelected)="onDateSelected($event)"
      (closed)="onDatePickerClosed()">
    </app-date-picker>
  `,
  styles: [
    `
      .traffic-log {
        background-color: #ffffff;
        box-shadow: 0px 0px 10px 0px rgba(255, 94, 94, 0.1);
        width: 100%;
        height: 100%;
        padding: 32px 24px 0;
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .header {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .title {
        font-family: 'PingFang SC', sans-serif;
        font-size: 16px;
        font-weight: 600;
        line-height: 16px;
        color: #000000;
      }

      .date-range {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        width: 100%;
        cursor: pointer;
        transition: border-color 0.2s ease;
      }

      .date-range:hover {
        border-color: #ff5e5e;
      }

      .date-selector {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;
      }

      .date-text {
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: 'PingFang SC', sans-serif;
        font-size: 14px;
        font-weight: 600;
        color: #808080;
      }

      .separator {
        font-family: 'Montserrat', sans-serif;
        font-weight: bold;
      }

      .usage-info {
        display: flex;
        align-items: center;
        background-color: rgba(128, 128, 128, 0.1);
        border-radius: 20px;
        padding: 4px 7px;
        height: 24px;
      }

      .usage-text {
        font-family: 'PingFang SC', sans-serif;
        font-size: 14px;
        color: #808080;
        white-space: nowrap;
      }

      .chevron-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        transform: rotate(90deg);
      }

      .log-content {
        height: 412px;
        overflow-y: auto;
        padding-right: 14px;
      }

      .log-content::-webkit-scrollbar {
        width: 4px;
      }

      .log-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
      }

      .log-content::-webkit-scrollbar-thumb {
        background: #ff5e5e;
        border-radius: 2px;
        opacity: 0.6;
      }

      .log-content::-webkit-scrollbar-thumb:hover {
        background: #e54e4e;
        opacity: 1;
      }

      .log-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0;
        margin-bottom: 28px;
        width: 100%;
      }

      .item-left {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .date {
        font-family: 'PingFang SC', sans-serif;
        font-size: 14px;
        font-weight: 500;
        color: #808080;
        width: 63px;
        text-align: left;
      }

      .usage-bar {
        width: 140px;
        height: 12px;
      }

      .bar-background {
        width: 100%;
        height: 100%;
        background-color: #f0f0f0;
        border-radius: 6px;
        overflow: hidden;
      }

      .bar-fill {
        height: 100%;
        background-color: #ff5e5e;
        border-radius: 6px;
        transition: width 0.3s ease;
      }

      .item-right {
        display: flex;
        align-items: center;
      }

      .usage-amount {
        font-family: 'Montserrat', sans-serif;
        font-size: 14px;
        font-weight: 500;
        color: #808080;
        white-space: nowrap;
      }

      /* Mobile responsive styles */
      @media (max-width: 768px) {
        .traffic-log {
          padding: 32px 24px 0;
        }

        .header {
          width: 100%;
        }

        .date-range {
          width: 100%;
        }

        .log-content {
          width: 100%;
          padding-right: 8px;
        }

        .log-content::-webkit-scrollbar {
          width: 3px;
        }

        .log-item {
          width: 100%;
        }
      }

      /* Extra small mobile screens */
      @media (max-width: 320px) {
        .header {
          width: 100%;
        }

        .date-range {
          width: 100%;
        }

        .log-content {
          width: 100%;
        }

        .log-item {
          width: 100%;
        }

        .usage-bar {
          width: 100px;
        }
      }

      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px 0;
      }

      .loading-text {
        font-size: 14px;
        color: #666;
      }

      .empty-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px 0;
      }

      .empty-text {
        font-size: 14px;
        color: #999;
      }
    `,
  ],
})
export class TrafficLogComponent implements OnInit, OnDestroy {
  @Input() startDate: string = moment().subtract(1, 'month').format('YYYY.M.D');
  @Input() endDate: string = moment().endOf('day').format('YYYY.M.D');
  @Input() currentUsage: string = '20G';
  @Input() maxUsage: string = '50G';
  @Input() logItems: TrafficLogItem[] = [];
  @Input() serviceId?: number; // 可选的服务ID，如果不传则使用当前服务

  showDatePicker: boolean = false;
  isLoading: boolean = false;
  private serviceSubscription?: Subscription;

  // 计算得出的当前区间总使用量
  private _calculatedCurrentUsage: string = '0B';
  private _calculatedMaxUsage: string = '0B';

  get calculatedCurrentUsage(): string {
    console.log('Getting calculatedCurrentUsage:', this._calculatedCurrentUsage);
    return this._calculatedCurrentUsage;
  }

  get calculatedMaxUsage(): string {
    console.log('Getting calculatedMaxUsage:', this._calculatedMaxUsage);
    return this._calculatedMaxUsage;
  }

  ngOnInit() {
    if (this.logItems.length === 0) {
      // 如果传入了serviceId，直接加载数据
      if (this.serviceId) {
        this.loadTrafficData();
      } else {
        // 检查当前是否已有服务
        const currentService = this.serviceService.currentService$.value;
        if (currentService && currentService.id) {
          // 如果已有服务，直接加载数据
          this.loadTrafficData();
        } else {
          // 否则等待当前服务加载完成，并主动刷新服务信息
          this.serviceService.refreshServiceInfo();
          this.waitForCurrentService();
        }
      }
    }
  }

  private waitForCurrentService() {
    // 订阅当前服务变化，当服务可用时加载数据（只执行一次）
    this.serviceSubscription = this.serviceService.currentService$
      .pipe(
        filter(service => !!service && !!service.id), // 过滤出有效的服务
        take(1) // 只取第一个有效的服务
      )
      .subscribe(service => {
        this.loadTrafficData();
      });
  }

  ngOnDestroy() {
    // 清理订阅
    if (this.serviceSubscription) {
      this.serviceSubscription.unsubscribe();
    }
  }

  openDatePicker() {
    this.showDatePicker = true;
  }

  onDateSelected(dateRange: DateRange) {
    this.startDate = dateRange.startDate;
    this.endDate = dateRange.endDate;
    // 重新加载数据
    this.loadTrafficData();
  }

  onDatePickerClosed() {
    this.showDatePicker = false;
  }

  private async loadTrafficData() {
    try {
      this.isLoading = true;

      // 获取服务ID：优先使用传入的serviceId，否则使用当前服务ID
      let targetServiceId: number;
      if (this.serviceId) {
        targetServiceId = this.serviceId;
      } else {
        // 从当前服务获取ID
        const currentService = this.serviceService.currentService$.value;
        if (!currentService || !currentService.id) {
          console.log('没有可用的服务ID，等待服务加载...');
          this.isLoading = false;
          return; // 直接返回，不抛出错误
        }
        targetServiceId = currentService.id;
      }

      // 将日期格式从 'YYYY.M.D' 转换为 'YYYY-MM-DD' (API 需要的格式)
      const startDateFormatted = moment(this.startDate, 'YYYY.M.D').format('YYYY-MM-DD');
      const endDateFormatted = moment(this.endDate, 'YYYY.M.D').format('YYYY-MM-DD');


      // 调用 API 获取每日流量数据
      const dailyUsageData = await this.serviceService.fetchServiceDailyUsage(targetServiceId, startDateFormatted, endDateFormatted);

      // 处理 API 返回的数据并转换为组件需要的格式
      this.logItems = this.processApiData(dailyUsageData);

    } catch (error) {
      console.error('获取流量数据失败:', error);
      // 如果 API 调用失败，显示空数据
      this.logItems = [];
    } finally {
      this.isLoading = false;
    }
  }

  private processApiData(apiData: any): TrafficLogItem[] {
    if (!apiData || !Array.isArray(apiData)) {
      this.updateUsageDisplay(0, 0); // 没有数据时重置显示
      return []; // 返回空数组而不是模拟数据
    }

    // 只计算一次最大使用量
    const currentService = this.serviceService.currentService$.value;
    const maxUsageInBytes = this.getMaxUsage(currentService?.productId);
    const maxUsageInMB = maxUsageInBytes / (1024 * 1024); // 转换为 MB

    // 计算区间内的总使用量
    const totalUsageInBytes = apiData.reduce((sum, item) => {
      const itemTotal = Number(item.total) || 0;
      console.log('Item total:', itemTotal, 'Type:', typeof itemTotal, 'Current sum:', sum);

      // 验证数值有效性
      if (!isFinite(itemTotal) || itemTotal < 0) {
        console.warn('Invalid item total:', item.total, 'Skipping...');
        return sum;
      }

      return sum + itemTotal;
    }, 0);

    console.log('Total usage in bytes:', totalUsageInBytes);
    console.log('Max usage in bytes:', maxUsageInBytes);

    // 更新显示的使用量
    this.updateUsageDisplay(totalUsageInBytes, maxUsageInBytes);

    return apiData.map(item => {
      // 处理 API 返回的数据格式 - 单日使用量
      const dailyUsageInBytes = item.total || 0;
      // 修正单位转换：API 返回的数据单位是字节，使用二进制计算（1024^2, 1024^3）
      const dailyUsageInMB = dailyUsageInBytes / (1024 * 1024); // 转换为 MB (二进制)
      const dailyUsageInGB = dailyUsageInBytes / (1024 * 1024 * 1024); // 转换为 GB (二进制)

      // 智能选择单位显示
      let displayUsage: string;
      const dailyUsageInKB = dailyUsageInBytes / 1024; // 转换为 KB

      if (dailyUsageInGB >= 1) {
        displayUsage = `${dailyUsageInGB.toFixed(2)}GB`;
      } else if (dailyUsageInMB >= 1) {
        displayUsage = `${dailyUsageInMB.toFixed(0)}MB`;
      } else if (dailyUsageInKB >= 1) {
        displayUsage = `${dailyUsageInKB.toFixed(0)}KB`;
      } else {
        displayUsage = `${dailyUsageInBytes}B`;
      }

      return {
        date: moment(item.date).format('YYYY.M.D'),
        usage: dailyUsageInMB, // 单日使用量
        maxUsage: maxUsageInMB,  // 总容量
        displayUsage: displayUsage
      };
    });
  }

  private updateUsageDisplay(totalUsageInBytes: number, maxUsageInBytes: number) {
    console.log('updateUsageDisplay - total:', totalUsageInBytes, 'max:', maxUsageInBytes);

    // 如果总使用量超过最大容量，可能是数据问题，显示警告
    if (totalUsageInBytes > maxUsageInBytes) {
      console.warn('总使用量超过最大容量，可能是数据累积问题');
      console.warn('Total:', totalUsageInBytes, 'Max:', maxUsageInBytes);
    }

    // 格式化总使用量显示
    this._calculatedCurrentUsage = this.formatBytes(totalUsageInBytes);
    this._calculatedMaxUsage = this.formatBytes(maxUsageInBytes);

    console.log('Display values:', this._calculatedCurrentUsage, '/', this._calculatedMaxUsage);
  }

  private formatBytes(bytes: number): string {
    console.log('formatBytes input:', bytes, 'type:', typeof bytes);

    // 验证输入值
    if (bytes === null || bytes === undefined || bytes < 0 || !isFinite(bytes) || isNaN(bytes)) {
      console.log('Invalid bytes value:', bytes);
      return '0B';
    }

    // 确保是数字类型
    const numBytes = Number(bytes);
    if (!isFinite(numBytes)) {
      console.log('Cannot convert to valid number:', bytes);
      return '0B';
    }

    const gb = numBytes / (1024 * 1024 * 1024);
    const mb = numBytes / (1024 * 1024);
    const kb = numBytes / 1024;

    console.log('Converted values - GB:', gb, 'MB:', mb, 'KB:', kb);

    if (gb >= 1) {
      const result = `${gb.toFixed(2)}GB`;
      console.log('formatBytes result:', result);
      return result;
    } else if (mb >= 1) {
      const result = `${mb.toFixed(0)}MB`;
      console.log('formatBytes result:', result);
      return result;
    } else if (kb >= 1) {
      const result = `${kb.toFixed(0)}KB`;
      console.log('formatBytes result:', result);
      return result;
    } else {
      const result = `${numBytes}B`;
      console.log('formatBytes result:', result);
      return result;
    }
  }

  getMaxUsage(productId: number) {
    let maxUsage: number;
    if (productId === 34) {
      maxUsage = 214748364800; // 200GB
    } else if (productId === 33) {
      maxUsage = 107374182400; // 100GB
    } else {
      maxUsage = 53687091200;  // 50GB
    }

    console.log('getMaxUsage - productId:', productId, 'maxUsage:', maxUsage);
    return maxUsage;
  }

  constructor(private serviceService: ServiceService) { }
}