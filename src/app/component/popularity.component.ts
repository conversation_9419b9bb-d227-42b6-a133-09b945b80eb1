import { Component, ElementRef, OnInit, ViewChild } from "@angular/core";
import { UsersService } from "@flashvpn-io/web-core";
import { AppService } from "../app.service";
import { Router } from "@angular/router";
import { APIManager } from "@flashvpn-io/web-core";
import { MatSnackBar } from "@angular/material/snack-bar";
import { DialogsService } from "../utils/dialogs/dialogs.service";

@Component({
  selector: "app-popularity",
  template: `
    <div class="popularity">
      <div class="prompt">
        <div>您可以通过链接或电邮邀请好友体验FlashVPN</div>
        <div>服务，并获得现金奖励</div>
      </div>
      <div class="information">
        <div class="information-title">累计邀请奖励</div>
        <div class="information-award">
          <div class="award-left">
            <div class="award-title">累计奖励</div>
            <div class="award-value">\${{ award }}</div>
          </div>
          <div class="award-right">
            <div class="award-title">累计邀请好友</div>
            <div class="award-value">{{ invitees }} 人</div>
          </div>
        </div>
        <div class="record" (click)="goToInviteRecord()">邀请记录 ></div>
      </div>
      <div class="operate">
        <div class="operate-left">
          <div class="operate-title">
            <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M13 1.50012C6.64873 1.50012 1.5 6.64885 1.5 13.0001C1.5 19.3514 6.64873 24.5001 13 24.5001C19.3513 24.5001 24.5 19.3514 24.5 13.0001C24.5 6.64885 19.3513 1.50012 13 1.50012ZM0.5 13.0001C0.5 6.09656 6.09644 0.500122 13 0.500122C19.9036 0.500122 25.5 6.09656 25.5 13.0001C25.5 19.9037 19.9036 25.5001 13 25.5001C6.09644 25.5001 0.5 19.9037 0.5 13.0001ZM14.878 7.34783L15.0304 7.12366L15.1829 6.90272L15.5226 6.40601C15.5648 6.34473 15.5474 6.26248 15.4844 6.22216C15.4223 6.18104 15.337 6.19797 15.2964 6.25925L14.7777 7.0148L14.622 7.24219C14.129 7.05593 13.5797 6.95191 12.9998 6.95191C12.4207 6.95191 11.8705 7.05593 11.3776 7.24219L11.2226 7.0148L11.0694 6.79145L10.7048 6.25925C10.6626 6.19797 10.5781 6.18185 10.5151 6.22216C10.453 6.26248 10.4356 6.34473 10.477 6.40601L10.8167 6.90272L10.9691 7.12366L11.1224 7.34783C9.96498 7.87276 9.18205 8.86699 9.18205 10.0047H16.8175C16.8175 8.86699 16.0346 7.87276 14.878 7.34783Z"
                fill="#FF5E5E" />
            </svg>
            <span>方式1 - 电邮邀请</span>
          </div>
          <mat-form-field class="form-input" floatLabel="never" appearance="fill" empty="false" autofilled="false">
            <svg MatPrefix style="margin-right: 20px;" width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M20 2C20 0.9 19.1 0 18 0H2C0.9 0 0 0.9 0 2V14C0 15.1 0.9 16 2 16H18C19.1 16 20 15.1 20 14V2ZM18 2L10 7L2 2H18ZM10 9L2 4V14H18V4L10 9Z"
                fill="black"
                fill-opacity="0.54" />
            </svg>
            <input
              id="inviteEmailInput"
              matInput
              i18n-placeholder
              placeholder="Please enter an email"
              (change)="revert()"
              [(ngModel)]="email"
              name="email" />
          </mat-form-field>
          <button
            id="sendInviteEmail"
            *ngIf="!sendEmailLoading"
            mat-raised-button
            color="warn"
            style="width: max-content;"
            (click)="sendInviteEmail()">
            发送邀请邮件
          </button>
          <app-load-botton *ngIf="sendEmailLoading"></app-load-botton>
        </div>
        <div class="operate-right">
          <div class="operate-title">
            <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M13 1.50012C6.64873 1.50012 1.5 6.64885 1.5 13.0001C1.5 19.3514 6.64873 24.5001 13 24.5001C19.3513 24.5001 24.5 19.3514 24.5 13.0001C24.5 6.64885 19.3513 1.50012 13 1.50012ZM0.5 13.0001C0.5 6.09656 6.09644 0.500122 13 0.500122C19.9036 0.500122 25.5 6.09656 25.5 13.0001C25.5 19.9037 19.9036 25.5001 13 25.5001C6.09644 25.5001 0.5 19.9037 0.5 13.0001ZM7 8.70381L7.00448 12.7942L11.9062 12.7663L11.904 8.03593L7 8.70381ZM7.00472 13.2797L11.9029 13.3115L11.9067 18.0459L7.00499 17.372L7.00472 13.2797ZM12.4951 7.94913V12.7583L18.9974 12.7067V7.00012L12.4951 7.94913ZM12.4879 13.3452L19.0008 13.3558L18.9993 19.0366L12.497 18.1189L12.4879 13.3452Z"
                fill="#FF5E5E" />
            </svg>
            <span>方式2 - 链接邀请</span>
          </div>
          <div #share class="share-box">
            朋友！推荐你一个超好用的梯子，又快又稳定，快来领取👉：https://flashvpn.io/users/signup?inviterCode={{ usersService?.user?.inviterCode }}
          </div>
          <button id="copyInviteLink" mat-raised-button color="warn" style="width: max-content;" (click)="copy(share)">复制邀请链接</button>
        </div>
      </div>
    </div>
    <div id="copy-successful" class="copy-successful slide-in-fwd-center" style="display: none">
      <svg width="24" height="18" viewBox="0 0 24 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2 9.875L8.77686 16L22 2" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
      <span style="margin-left: 5px">成功复制到剪切板</span>
    </div>
  `,
  styles: [
    `
      .popularity {
        width: 100%;
      }
      .prompt {
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 22px;
        color: #616577;
      }
      .information {
        margin-top: 24px;
        width: 80%;
        height: 186px;
        background-image: url("../../assets/images/popularity.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        position: relative;
      }
      .information-title {
        position: absolute;
        left: 5%;
        right: 52.29%;
        top: 12.9%;
        bottom: 74.19%;
        font-family: "Libre Franklin";
        font-style: normal;
        font-weight: 600;
        font-size: 22px;
        line-height: 24px;
        color: #ffffff;
      }
      .information-award {
        position: absolute;
        left: 5%;
        top: 45%;
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        padding: 0px;
        gap: 64px;
      }
      .award-left {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 0px;
        gap: 8px;
        flex: none;
        order: 0;
        flex-grow: 0;
      }
      .award-right {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 0px;
        gap: 8px;
        flex: none;
        order: 1;
        flex-grow: 0;
      }
      .award-title {
        font-family: "Libre Franklin";
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        line-height: 17px;
        color: rgba(255, 255, 255, 0.5);
        flex: none;
        order: 0;
        flex-grow: 0;
      }
      .award-value {
        font-family: "Libre Franklin";
        font-style: normal;
        font-weight: 500;
        font-size: 24px;
        line-height: 29px;
        color: #ffffff;
        flex: none;
        order: 1;
        flex-grow: 0;
      }
      .record {
        position: absolute;
        left: 82.02%;
        right: -6.49%;
        top: 50%;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 22px;
        color: #ffffff;
        cursor: pointer;
      }
      .operate {
        width: 80%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: flex-start;
        padding: 0px;
        margin-top: 26px;
        gap: 24px;
      }
      .operate-title {
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 22px;
        color: #242c45;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 10px;
      }
      .operate-left {
        width: 50%;
        display: flex;
        flex-direction: column;
      }
      .operate-right {
        width: 50%;
        display: flex;
        flex-direction: column;
      }
      .form-input {
        width: 100%;
        height: 56px;
        margin: 12px 0;
      }
      :host ::ng-deep .mat-form-field-wrapper {
        padding-bottom: 0;
        height: 100%;
      }
      :host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
        padding: 0 12px;
        height: 100%;
        align-items: center;
      }
      :host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-infix {
        padding: 0;
        margin: 0;
        border: 0;
        display: flex;
        align-items: center;
      }
      :host ::ng-deep .mat-form-field-underline {
        display: none !important;
      }
      .share-box {
        background: #f6f6f6;
        border-radius: 8px;
        padding: 16px;
        margin: 12px 0;
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        color: rgba(0, 0, 0, 0.87);
        mix-blend-mode: normal;
      }
      .copy-successful {
        width: 216px;
        height: 52px;
        border-radius: 8px;
        background: rgba(0, 0, 0, 0.5);
        color: #fff;
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        display: flex;
        align-items: center;
        justify-content: center;
        position: fixed;
        left: 50%;
        top: 50%;
        margin-top: -26px;
        margin-left: -108px;
        z-index: 9999;
      }
      @media screen and (max-width: 599px) {
        .information {
          width: 100%;
          background-image: url("../../assets/images/popularity-mobile.png");
        }
        .information-award {
          top: 40%;
        }
        .record {
          position: absolute;
          left: 5%;
          top: 80%;
          font-style: normal;
          font-weight: 400;
          font-size: 16px;
          line-height: 22px;
          color: #ffffff;
          cursor: pointer;
        }
        .operate {
          width: 100%;
          flex-direction: column;
          justify-content: center;
          align-items: center;
        }
        .operate-left {
          width: 100%;
        }
        .operate-right {
          width: 100%;
        }
      }

      .slide-in-fwd-center {
        -webkit-animation: slide-in-fwd-center 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
        animation: slide-in-fwd-center 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      }

      @-webkit-keyframes slide-in-fwd-center {
        0% {
          -webkit-transform: translateZ(-1400px);
          transform: translateZ(-1400px);
          opacity: 0;
        }
        100% {
          -webkit-transform: translateZ(0);
          transform: translateZ(0);
          opacity: 1;
        }
      }
      @keyframes slide-in-fwd-center {
        0% {
          -webkit-transform: translateZ(-1400px);
          transform: translateZ(-1400px);
          opacity: 0;
        }
        100% {
          -webkit-transform: translateZ(0);
          transform: translateZ(0);
          opacity: 1;
        }
      }
      .slide-out-bck-center {
        -webkit-animation: slide-out-bck-center 0.5s cubic-bezier(0.55, 0.085, 0.68, 0.53) both;
        animation: slide-out-bck-center 0.5s cubic-bezier(0.55, 0.085, 0.68, 0.53) both;
      }

      @-webkit-keyframes slide-out-bck-center {
        0% {
          -webkit-transform: translateZ(0);
          transform: translateZ(0);
          opacity: 1;
        }
        100% {
          -webkit-transform: translateZ(-1100px);
          transform: translateZ(-1100px);
          opacity: 0;
        }
      }
      @keyframes slide-out-bck-center {
        0% {
          -webkit-transform: translateZ(0);
          transform: translateZ(0);
          opacity: 1;
        }
        100% {
          -webkit-transform: translateZ(-1100px);
          transform: translateZ(-1100px);
          opacity: 0;
        }
      }
    `,
  ],
})
export class PopularityComponent implements OnInit {
  public award = 0;
  public invitees = 0;
  public email: any;
  public sendEmailLoading = false;

  constructor(
    public usersService: UsersService,
    private appService: AppService,
    private router: Router,
    private elementRef: ElementRef,
    private apiManager: APIManager,
    private snackBar: MatSnackBar,
    private dialogsService: DialogsService
  ) {}

  async ngOnInit() {
    await this.getInvitRewardInfo();
  }

  goToInviteRecord() {
    this.router.navigate(["invite-record"]);
  }

  async copy(copyWord: HTMLElement) {
    const range = document.createRange();
    range.selectNode(copyWord);
    window.getSelection().removeAllRanges();
    window.getSelection().addRange(range);
    document.execCommand("copy");
    window.getSelection().removeAllRanges();
    const copyDiv = document.getElementById("copy-successful");
    if (copyDiv) {
      copyDiv.style.display = "flex";
      copyDiv.setAttribute("class", "copy-successful slide-in-fwd-center");
      setTimeout(() => {
        copyDiv.setAttribute("class", "copy-successful slide-out-bck-center");
      }, 1500);
    }
  }

  async getInvitRewardInfo() {
    this.apiManager.getInvitRewardInfo().subscribe(
      (res) => {
        if (res) {
          this.award = res?.award;
          this.invitees = res?.invitees;
        }
      },
      (error) => {
        this.appService.snackUp(error?.error?.message);
      }
    );
  }

  checkEmail() {
    const errorInfo = {
      isError: false,
      errorTips: null,
    };
    const reg = /^\w+((.\w+)|(-\w+))@[A-Za-z0-9]+((.|-)[A-Za-z0-9]+).[A-Za-z0-9]+$/;
    const isReg = reg.test(this.email);
    if (!isReg) {
      errorInfo.isError = true;
      errorInfo.errorTips = this.appService.translate("EmailFormatError");
      return errorInfo;
    }
    const reg2 = /(^\s+)|(\s+$)|\s+|\++/g;
    const isReg2 = reg2.test(this.email);
    if (!/foxit.io/.test(this.email) && isReg2) {
      errorInfo.isError = true;
      errorInfo.errorTips = this.appService.translate("EmailIllegalCharacters");
      return errorInfo;
    }
    if (this.email && this.email.length > 40) {
      errorInfo.isError = true;
      errorInfo.errorTips = this.appService.translate("EmailLongError");
      return errorInfo;
    }
    return errorInfo;
  }

  revert() {
    document.getElementById("emailInput").style.color = "";
  }

  async sendInviteEmail() {
    if (this.email) {
      const errorInfo = await this.checkEmail();
      if (errorInfo.isError) {
        this.snackBar.open(this.appService.translate(errorInfo.errorTips), "Okay", {
          duration: 2000,
          verticalPosition: "top",
        });
        document.getElementById("emailInput").style.color = "#FF5E5E";
        return;
      }
      this.sendEmailLoading = true;
      this.apiManager.sendInviteEmail(this.email).subscribe(
        (res) => {
          this.dialogsService.openDialog("send-success-dialog");
          this.sendEmailLoading = false;
        },
        (error) => {
          this.appService.snackUp(error?.error?.message);
          this.sendEmailLoading = false;
        }
      );
    } else {
      this.snackBar.open("请输入一个邮箱", "Okay", {
        duration: 2000,
        verticalPosition: "top",
      });
      this.sendEmailLoading = false;
    }
  }
}
