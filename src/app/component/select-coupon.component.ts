import { Component, Input, Output, EventEmitter } from "@angular/core";
import { DynamicDialogService, RewardService } from "@flashvpn-io/web-core";
import { CouponListComponent } from "./coupon-list.component";
import { Reward } from "@flashvpn-io/web-core";

@Component({
  selector: "app-select-coupon",
  template: `
    <div class="coupon-container">
      <div class="deduction-box">
        <div class="deduction-bottom-box">
          <div [class]="enabled ? 'voucher-box' : 'voucher-box-disabled'" (click)="goToSelectVoucher()">
            <svg width="18" height="16" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M0 2C0 0.895431 0.895431 0 2 0H16C17.1046 0 18 0.895431 18 2V6.00002C16.8954 6.00004 16 6.89546 16 8.00002C16 9.10458 16.8954 10 18 10V14C18 15.1046 17.1046 16 16 16H2C0.89543 16 0 15.1046 0 14V10H2.43187e-05C1.10459 10 2.00002 9.10459 2.00002 8.00002C2.00002 6.89545 1.10459 6.00002 2.43187e-05 6.00002H0V2ZM8.43769 2.85713H9.56269V12.5714H8.43769V2.85713ZM7.875 3.54746C7.46659 3.64583 7.10549 3.8058 6.78978 4.02933C6.17303 4.44519 5.87607 5.02278 5.87607 5.75055C5.87607 6.47831 6.18445 7.03279 6.81262 7.414C6.97967 7.51055 7.33508 7.65192 7.875 7.83812V6.55748C7.83844 6.54102 7.80791 6.52617 7.78343 6.51296C7.39511 6.30503 7.21237 6.01624 7.21237 5.66968C7.21237 5.27692 7.37227 4.98813 7.71491 4.81485C7.76451 4.78768 7.81785 4.76315 7.875 4.7412V3.54746ZM10.125 7.33436V8.58581C10.1905 8.6117 10.2399 8.63314 10.2733 8.65004C10.7758 8.90418 11.0385 9.25074 11.0385 9.6897C11.0385 10.0363 10.8558 10.302 10.5131 10.5099C10.3987 10.5754 10.2691 10.6307 10.125 10.6752V11.9167C10.6862 11.8177 11.1516 11.6317 11.5182 11.3647C12.0893 10.9373 12.3748 10.3482 12.3748 9.60884C12.3748 8.84643 12.0207 8.25728 11.3126 7.82987C11.096 7.70469 10.7013 7.53713 10.125 7.33436ZM7.875 11.8812V10.6015C7.7772 10.558 7.68962 10.5081 7.61211 10.4521C7.25805 10.1864 7.04105 9.73591 6.94968 9.11212H5.6248C5.68191 10.1633 6.05881 10.9257 6.74409 11.4109C7.05238 11.6273 7.42999 11.784 7.875 11.8812ZM10.125 4.8525V3.54805C10.5514 3.64646 10.9136 3.80625 11.2098 4.02933C11.7809 4.45674 12.1121 5.12675 12.1921 6.02779H10.8672C10.753 5.51951 10.536 5.14985 10.239 4.93037C10.2033 4.90259 10.1653 4.87664 10.125 4.8525Z"
                fill="#FF5E5E" />
            </svg>
            <div style="width: 100%" *ngIf="!isInvoices && (rewardService.availableCoupons$ | async)?.length === 0">
              <div class="voucher-content">
                <div class="voucher-left">
                  <span class="voucher-title">{{ "无可用的优惠券" }}</span>
                </div>
              </div>
            </div>
            <div style="width: 100%" *ngIf="isInvoices || (rewardService.availableCoupons$ | async)?.length !== 0">
              <div class="voucher-content" *ngIf="currentCoupon">
                <div class="voucher-left">
                  {{ currentCoupon.title }}
                </div>
                <div class="voucher-right">
                  <span class="voucher-amount">{{ currentCoupon.award }} 港币</span>
                  <svg _ngcontent-sdp-c211="" width="9" height="13" viewBox="0 0 9 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path _ngcontent-sdp-c211="" d="M1 1L7 6.5L1 12" stroke="black" stroke-width="2"></path>
                  </svg>
                </div>
              </div>
              <div class="voucher-content" *ngIf="!currentCoupon">
                <div class="voucher-left">
                  <span style="color: #FF5E5E;font-weight: 600">{{ (rewardService.availableCoupons$ | async)?.length }}</span>
                  张可用的优惠券
                </div>
                <div class="voucher-right">
                  <span [style]="{ color: enabled ? '#FF5E5E' : '#808080', fontWeight: 600 }" *ngIf="!currentCoupon">请选择</span>
                  <svg _ngcontent-sdp-c211="" width="9" height="13" viewBox="0 0 9 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path _ngcontent-sdp-c211="" d="M1 1L7 6.5L1 12" stroke="black" stroke-width="2"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .coupon-container {
        width: 100%;
      }
      .deduction-box {
        display: inline;
        width: 100%;
        margin-bottom: 30px;
        margin-top: 15px;
      }
      .deduction-bottom-box {
        display: flex;
        align-items: center;
      }
      .voucher-box {
        width: 344px;
        height: 55px;
        border-radius: 4px;
        box-shadow: 0 0 4px #0000001f;
        margin: 10px 0;
        display: flex;
        justify-content: start;
        align-items: center;
        padding: 0 20px;
      }
      .voucher-box-disabled {
        width: 344px;
        height: 55px;
        background: #f6f6f6;
        cursor: not-allowed;
        height: 55px;
        border-radius: 4px;
        box-shadow: 0 0 4px #0000001f;
        margin: 10px 0;
        display: flex;
        justify-content: start;
        align-items: center;
        padding: 0 20px;
      }
      .voucher-title {
        color: gray;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 16px;
      }
      .voucher-content {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding-left: 20px;
      }
      .voucher-right {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 10px;
      }
      .voucher-amount {
        color: #ff5e5e;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 12px;
      }
      h3 {
        text-align: center;
        color: #333;
      }

      @media screen and (max-width: 599px) {
        .voucher-box {
          width: 100%;
        }
        .voucher-box-disabled {
          width: 100%;
        }
      }
    `,
  ],
})
export class SelectCouponComponent {
  @Input() enabled: boolean = true;
  @Input() isInvoices: boolean = false;
  @Output() couponSelected = new EventEmitter<Reward>();

  public currentCoupon: Reward;

  constructor(public rewardService: RewardService, private dialogsService: DynamicDialogService) {}

  selectCoupon(coupon: Reward) {
    this.currentCoupon = coupon;
    this.couponSelected.emit(coupon);
  }

  goToSelectVoucher() {
    if (!this.enabled || this.rewardService.availableCoupons$.value.length === 0) {
      return;
    }
    const couponList = this.dialogsService.open(CouponListComponent);
    couponList.vouchers = this.rewardService.availableCoupons$.value;
    couponList.couponSelected.subscribe((coupon: Reward) => {
      this.selectCoupon(coupon);
      this.dialogsService.close();
    });
  }
}
