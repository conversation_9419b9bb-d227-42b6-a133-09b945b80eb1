import { ChangeDetectorRef, Component, EventEmitter, Input, Output } from "@angular/core";
import { PaymentMethodService } from "@flashvpn-io/web-core";
import { PayMethod, PayType } from "@flashvpn-io/web-core";
import { BehaviorSubject, merge, Subject } from "rxjs";
import { filter, map, switchMap } from "rxjs/operators";

@Component({
  selector: "app-oauth",
  template: `
    <div class="oauth">
      <a id="oauth-google" mat-icon-button class="oauthLink" (click)="signIn('google')"><mat-icon svgIcon="google"></mat-icon></a>
      <a id="oauth-facebook" mat-icon-button class="oauthLink" (click)="signIn('facebook')"><mat-icon svgIcon="facebook"></mat-icon></a>
      <a id="oauth-twitter" mat-icon-button class="oauthLink" (click)="signIn('twitter')"><mat-icon svgIcon="twitter"></mat-icon></a>
    </div>
  `,
  styles: [
    `
      .oauthLink {
        transform: scale(1.5);
      }
      .oauth {
        text-align: center;
      }
      a {
        margin-right: 24px;
      }
      a:last-child {
        margin-right: 0;
      }

      #oauth-facebook {
        display: none;
      }
    `,
  ],
})
export class OauthComponent {
  signIn(oauth: string) {
    switch (oauth) {
      case "google":
        window.location.replace("/api/v1/users/oauth/google");
        break;
      case "facebook":
        window.location.replace("/api/v1/users/oauth/facebook");
        break;
      case "twitter":
        window.location.replace("/api/v1/users/oauth/twitter");
        break;
      default:
        break;
    }
  }
}
