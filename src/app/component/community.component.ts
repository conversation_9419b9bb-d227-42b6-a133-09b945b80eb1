import { Component, Input, OnInit } from '@angular/core';
import { IVoucher, VoucherType } from './voucher.component';

@Component({
  selector: 'app-community',
  template: `
    <div class="community-list">
      <div class="community-card" *ngFor="let community of communityList">
        <div class="community-card-left">
          <mat-icon class="card-icon" color="warn" [svgIcon]="community.id"></mat-icon>
        </div>
        <div class="community-card-right">
          <div class="community-card-text">
            <span>{{community.title}}</span>
            <span>领 <span style="color: #FF5E5E">{{community.award}}港币</span> 红包</span>
          </div>
          <div class="receive-botton">
            <button *ngIf="!community.receive" class="voucher-button" mat-raised-button color="warn" (click)="trigger(community)">{{getBtnText(community)}}</button>
            <div class="received" *ngIf="community.receive">{{'已领取'}}</div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .community-list{
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      flex-direction: initial;
      gap: 24px;
    }
    .community-card{
      width: 343px;
      height: 84px;
      background: #FFFFFF;
      box-shadow: 0px 2px 10px rgba(255, 94, 94, 0.1);
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    @media screen and (max-width: 599px){
      .community-card{
        width: 100%;
      }
    }
    .community-card-left{
      display: flex;
      justify-content: center;
      align-items: center;
      width: 110px;
    }
    .community-card-right{
      display: flex;
      justify-content: space-between;
      align-items: center;
      float: right;
      width: 100%;
    }
    .community-card-text{
      display: flex;
      flex-direction: column;
      gap: 10px;
      width: 60%;
      color: #505050;
      font-size: 14px;
      font-weight: 500;
      line-height: 14px;
    }
    .card-icon{
      width: 48px;
      height: 48px;
    }
    .receive-botton{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 40%;
    }
    .voucher-button{
      line-height: 24px;
      padding: 0 8px;
    }
    .received{
      font-style: normal;
      font-weight: 600;
      font-size: 14px;
      line-height: 15px;
      color: #FF5E5E;
      padding: 0 16px;
    }
  `]
})
export class CommunityComponent implements OnInit {
  @Input() public communityList: IVoucher[] = [];
  @Input() public trigger: any;

  constructor() { }

  async ngOnInit() {
  }

  getBtnText(community: IVoucher) {
    let text = '';
    if (community.achieve) {
      text = '领取';
    } else {
      switch (community.type) {
        case VoucherType.BIND_TG:
          text = '立即绑定';
          break;
        case VoucherType.ENABLE_FCM:
          text = '立即开启';
          break;
        case VoucherType.QUESTIONNAIRE:
          text = '开启问卷';
          break;
        default:
          break;
      }
    }
    return text;
  }
}