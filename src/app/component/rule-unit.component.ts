import { Component, EventEmitter, Input, Output } from '@angular/core';

export type RuleAction = 'Directly' | 'Proxy' | 'Dropped';

@Component({
  selector: 'app-rule-unit',
  template: `
    <mat-card class="rule-unit">
      <div class="rule-info">
        <div class="rule-action" [ngClass]="actionClass">
          {{ action }}
        </div>
        <span class="rule-domain">{{ domain }}</span>
      </div>
      <div class="rule-actions">
        <button mat-icon-button (click)="onEdit.emit()" *ngIf="!isPlatform">
          <img src="assets/images/edit.svg" alt="Edit">
        </button>
        <button mat-icon-button (click)="onDelete.emit()" *ngIf="!isPlatform">
          <img src="assets/images/delete.svg" alt="Delete">
        </button>
      </div>
    </mat-card>
  `,
  styles: [`
    .rule-unit {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border: 1px solid #E6E6E6;
      border-radius: 2px;
      width: 100%;
      height: 52px;
      padding: 16px 8px;
      box-shadow: unset;
    }

    @media (min-width: 768px) {
      .rule-unit {
        width: 327px;
      }
    }

    .rule-info {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .rule-action {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 600;
      line-height: 1em;
    }

    .rule-action.directly {
      background-color: rgba(9, 224, 160, 0.1);
      color: #09E0A0;
    }

    .rule-action.proxy {
      background-color: rgba(255, 192, 57, 0.1);
      color: #FFC039;
    }

    .rule-action.dropped {
      background-color: rgba(255, 94, 94, 0.1);
      color: #FF5E5E;
    }

    .rule-domain {
      font-size: 14px;
      line-height: 1em;
      color: #000000;
    }

    .rule-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .rule-actions button {
      color: #808080;
      width: 18px;
      height: 18px;
      line-height: 18px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .rule-actions img {
      width: 18px;
      height: 18px;
    }
  `]
})
export class RuleUnitComponent {
  @Input() domain: string = '';
  @Input() action: RuleAction = 'Directly';
  @Input() isPlatform: boolean = false;
  @Output() onEdit = new EventEmitter<void>();
  @Output() onDelete = new EventEmitter<void>();
  @Output() onToggle = new EventEmitter<void>();

  get actionClass(): string {
    return this.action.toLowerCase();
  }
} 