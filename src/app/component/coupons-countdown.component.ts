import { Component, OnDestroy, Output } from "@angular/core";
import { RewardService, Reward, RouterBuyType, BuyType, ServiceService } from "@flashvpn-io/web-core";
import { Router } from "@angular/router";
import { APIManager } from "@flashvpn-io/web-core";
import { DialogsService } from "../utils/dialogs/dialogs.service";

@Component({
  selector: "app-coupons-countdown",
  template: `
    <div class="couponsBox">
      <img class="serviceCoupons" src="assets/images/service-coupons.png" alt="Coupons" />
      <div class="couponsBtnBox">
        <div class="expirationTime">
          <div class="timeItem" *ngIf="timeLeft.hoursTens && +timeLeft.hoursTens > 0">
            {{ timeLeft.hoursTens }}
          </div>
          <div class="timeItem">{{ timeLeft.hoursOnes }}</div>
          <div class="timeItem">{{ timeLeft.hoursTwos }}</div>
          <div class="pointBox">
            <div class="point"></div>
            <div class="point"></div>
          </div>
          <div class="timeItem">{{ timeLeft.minutesTens }}</div>
          <div class="timeItem">{{ timeLeft.minutesOnes }}</div>
          <div class="pointBox">
            <div class="point"></div>
            <div class="point"></div>
          </div>
          <div class="timeItem">{{ timeLeft.secondsTens }}</div>
          <div class="timeItem">{{ timeLeft.secondsOnes }}</div>
        </div>
        <button class="couponsBtn" color="warn" (click)="goToPaymentMethods()">
          <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="12" height="13.9998" rx="2" fill="#FF5E5E" />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M0 2.28935V11.9995C0 13.1041 0.895431 13.9995 2 13.9995H10C11.1046 13.9995 12 13.1041 12 11.9995V2.09375C10.2564 3.34325 8.14023 4.0753 5.85884 4.0753C3.69921 4.0753 1.68762 3.4193 0 2.28935Z"
              fill="#D24B4B" />
            <ellipse cx="6" cy="4.99998" rx="2" ry="1.99998" fill="#FFC039" />
          </svg>
          立即使用
        </button>
      </div>
    </div>
  `,
  styles: [
    `
      .couponsBox {
        height: 100%;
        box-shadow: 0px 0px 10px 0px rgba(255, 94, 94, 0.1);
        background: linear-gradient(45deg, rgba(255, 239, 239, 1), rgba(255, 253, 239, 0.8));
        cursor: pointer;
      }
      .serviceCoupons {
        width: 100%;
        height: 100%;
      }
      .serviceCoupons img {
        height: 100%;
        width: auto; /* Maintain aspect ratio */
      }
      .couponsBtnBox {
        position: absolute;
        bottom: 15px;
        left: 20px;
        padding: 10px;
      }
      .expirationTime {
        display: flex;
        flex-direction: row;
        gap: 5px;
        margin-bottom: 15px;
      }
      .timeItem {
        width: 15px;
        height: 24px;
        background: rgba(255, 94, 94, 1);
        font-family: Quantico;
        font-size: 16px;
        font-weight: 400;
        line-height: 16px;
        text-align: left;
        color: rgba(255, 255, 255, 1);
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 3px;
      }
      .pointBox {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 2px;
      }
      .point {
        width: 6px;
        height: 6px;
        border-radius: 2px;
        background: rgba(255, 94, 94, 1);
      }
      .couponsBtn {
        border-radius: 20px;
        padding: 8px 20px 8px 20px;
        border: 1px solid;
        cursor: pointer;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        color: rgba(0, 0, 0, 1);
        gap: 10px;
        background: linear-gradient(180deg, #fff59e 0%, #ffe17d 2.6%, #ffc149 60.42%, #ffbe17 100%);
      }
    `,
  ],
})
export class CouponsCountdownComponent implements OnDestroy {
  public timeLeft: TimeLeft = {
    hoursTens: "0",
    hoursOnes: "0",
    hoursTwos: "0",
    minutesTens: "0",
    minutesOnes: "0",
    secondsTens: "0",
    secondsOnes: "0",
  };

  public timer: any;

  public currentCoupon: Reward;

  constructor(
    private rewardService: RewardService,
    public router: Router,
    public serviceService: ServiceService,
    public apiManager: APIManager,
    public dialogsService: DialogsService
  ) {
    this.rewardService.coupons$.subscribe((coupons) => {
      this.initRewards(coupons);
    });
  }

  ngOnDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  }

  initRewards = (coupons: Reward[]) => {
    this.timeLeft = {
      hoursTens: "0",
      hoursOnes: "0",
      hoursTwos: "0",
      minutesTens: "0",
      minutesOnes: "0",
      secondsTens: "0",
      secondsOnes: "0",
    };
    if (!!this.timer) {
      clearInterval(this.timer);
    }
    if (!coupons) {
      return;
    }
    const claimedItems = coupons.filter((item) => item.status === "CLAIMED");

    if (!(claimedItems && claimedItems.length > 0)) {
      return;
    }

    claimedItems.forEach((item) => {
      const expiryDateStr = item.expiryDate ?? (item?.note?.split("使用期限：")[1] || "");
      item.expiryDateTime = expiryDateStr ? new Date(expiryDateStr) : undefined;
    });

    claimedItems.sort((a, b) => {
      const dateA = a.expiryDateTime ? a.expiryDateTime.getTime() : Infinity;
      const dateB = b.expiryDateTime ? b.expiryDateTime.getTime() : Infinity;
      return dateA - dateB;
    });

    const nearestItem = claimedItems.pop();
    this.currentCoupon = nearestItem;
    const calculateTimeLeft = (): TimeLeft => {
      const now = new Date();
      const difference = nearestItem?.expiryDateTime ? nearestItem.expiryDateTime.getTime() - now.getTime() : 0;

      if (difference > 0) {
        const totalSeconds = Math.floor(difference / 1000);
        const totalMinutes = Math.floor(totalSeconds / 60);
        const totalHours = Math.floor(totalMinutes / 60);

        const hours = totalHours;
        const minutes = totalMinutes % 60;
        const seconds = totalSeconds % 60;

        const hoursString = String(hours).padStart(3, "0");
        const minutesString = String(minutes).padStart(2, "0");
        const secondsString = String(seconds).padStart(2, "0");

        return {
          hoursTens: hoursString[0],
          hoursOnes: hoursString[1],
          hoursTwos: hoursString[2],
          minutesTens: minutesString[0],
          minutesOnes: minutesString[1],
          secondsTens: secondsString[0],
          secondsOnes: secondsString[1],
        };
      } else {
        return {
          hoursTens: "0",
          hoursOnes: "0",
          hoursTwos: "0",
          minutesTens: "0",
          minutesOnes: "0",
          secondsTens: "0",
          secondsOnes: "0",
        };
      }
    };
    this.timeLeft = calculateTimeLeft();
    if (!!this.timer) {
      clearInterval(this.timer);
    }
    this.timer = setInterval(() => {
      this.timeLeft = calculateTimeLeft();
    }, 1000);
  };

  async goToPaymentMethods() {
    let invoice = await this.apiManager.fetchLatestInvoice(this.serviceService.currentService$.value).toPromise();
    if (invoice && invoice.hadDiscount()) {
      this.dialogsService.openDialog("invoice-check-dialog", {
        invoiceId: invoice.id,
        buyType: BuyType.SERVICE,
      });
    } else {
      this.router.navigate([`payment-methods/${RouterBuyType.SERVICE}`], {
        queryParams: this.currentCoupon ? { currentCoupon: JSON.stringify(this.currentCoupon) } : {},
      });
    }
  }
}

interface TimeLeft {
  hoursTens: string;
  hoursOnes: string;
  hoursTwos: string;
  minutesTens: string;
  minutesOnes: string;
  secondsTens: string;
  secondsOnes: string;
}
