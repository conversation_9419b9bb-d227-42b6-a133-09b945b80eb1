import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Reward, RewardStatus } from '@flashvpn-io/web-core';

@Component({
  selector: 'app-my-voucher-item',
  template: `
<div class="my-voucher-card-list">
  <div
    class="my-voucher-card"
    (click)="selectCoupon(voucher)"
    [style]="{
      'box-shadow': voucherStaus.CLAIMED===voucher.status?'0px 0px 10px rgba(255, 94, 94, 0.1)':'0px 0px 10px rgba(0, 0, 0, 0.1)',
      'background': voucherStaus.EXPIRED===voucher.status?'#D9D9D9':'#FFFFFF'
    }"
    *ngFor="let voucher of vouchers"
  >
    <div
      [class]="voucherStaus.CLAIMED===voucher.status?'my-voucher-card-left':'my-voucher-card-left-black'"
      [style]="{
        background: voucherStaus.EXPIRED===voucher.status?'linear-gradient(#D9D9D9,#D9D9D9) padding-box,repeating-linear-gradient(-45deg,#999999 0, #ccc 0.1em,#D9D9D9 0,#D9D9D9 0.3em)'
          :'linear-gradient(white,white) padding-box,repeating-linear-gradient(-45deg,#999999 0, #ccc 0.1em,white 0,white 0.3em)'
      }"
    >
      <div class="award-box" [style]="{'color': voucherStaus.CLAIMED===voucher.status?'#FF5E5E':'#808080'}">
        <div class="award">{{voucher.award}}</div>
        <div class="award-ccy">港币</div>
      </div>
      <div id='bottomCover'></div>
      <div id='topCover'></div>
    </div>
    <div class="my-voucher-card-right">
      <div class="my-voucher-card-text" [style]="{'color': voucherStaus.CLAIMED===voucher.status?'#505050':'#808080'}" >
        <span>{{voucher.title}}</span>
        <div class="my-voucher-card-note">
          <span>{{voucher.note}}</span>
        </div>
      </div>
    </div>
    <div class="card-tag" [style]="{'background': voucherStaus.USED===voucher.status?'#FF5E5E':'#808080'}" *ngIf="voucherStaus.CLAIMED!==voucher.status">
      {{voucherStaus.USED===voucher.status?'已使用':'已过期'}}
    </div>
  </div>
</div>
  `,
  styles: [`
    .my-voucher-card-list{
  width: auto;
  display: flex;
  flex-wrap: wrap;
  flex-direction: initial;
  gap: 24px;
  padding: 24px;
}
.my-voucher-card {
  width: 343px;
  height: 84px;
  background: #FFFFFF;
  box-shadow: 0px 0px 10px rgba(255, 94, 94, 0.1);
  display: flex;
  align-items: center;
  border-radius: 8px;
  position: relative;
}
.my-voucher-card-right{
  display: flex;
  justify-content: space-between;
  align-items: center;
  float: right;
  width: 100%;
}
.my-voucher-card-text{
  display: flex;
  flex-direction: column;
  margin-left: 10px;
  gap: 10px;
  width: 100%;
  color: #505050;
  font-size: 16px;
  font-weight: 500;
  line-height: 14px;
}
@media screen and (max-width: 599px){
  .my-voucher-card{
    width: 100%;
  }
}
.my-voucher-card-left, .my-voucher-card-left-black{
  width: 90px;
  height: 84px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right:1px solid transparent;
  background: linear-gradient(white,white) padding-box,
  repeating-linear-gradient(-45deg,#999999 0, #ccc 0.1em,white 0,white 0.3em);
  border-radius: 8px;
}

.my-voucher-card-left::before,.my-voucher-card-left::after,.my-voucher-card-left-black::before,.my-voucher-card-left-black::after{
  content: ''
}

.my-voucher-card-left::before, #bottomCover {
  position: absolute;
  right: 0;
  bottom: -8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #FFFFFF;
  z-index: 1;
  box-shadow: inset 0 0px 10px rgba(255, 94, 94, 0.1);
}

.my-voucher-card-left-black::before, #bottomCover {
  position: absolute;
  right: 0;
  bottom: -8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #FFFFFF;
  z-index: 1;
  box-shadow: inset 0 0px 10px rgba(0, 0, 0, 0.1);
}

.my-voucher-card-left::after, #topCover {
  position: absolute;
  top: -8px;
  right: 0;
  bottom: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #FFFFFF;
  z-index: 1;
  box-shadow: inset 0 0px 10px rgba(255, 94, 94, 0.1);
}

.my-voucher-card-left-black::after, #topCover {
  position: absolute;
  top: -8px;
  right: 0;
  bottom: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #FFFFFF;
  z-index: 1;
  box-shadow: inset 0 0px 10px rgba(0, 0, 0, 0.1);
}

.my-voucher-card-left::after, #topCover{
  left: inherit;
  right: -8px;
  box-shadow: inset 0 0px 10px rgba(255, 94, 94, 0.1);
}

.my-voucher-card-left::before, #bottomCover{
  left: inherit;
  right: -8px;
  box-shadow: inset 0 0px 10px rgba(255, 94, 94, 0.1);
}

.my-voucher-card-left-black::after, #topCover{
  left: inherit;
  right: -8px;
  box-shadow: inset 0 0px 10px rgba(0, 0, 0, 0.1);
}

.my-voucher-card-left-black::before, #bottomCover{
  left: inherit;
  right: -8px;
  box-shadow: inset 0 0px 10px rgba(0, 0, 0, 0.1);
}

#topCover{
  right: -15px;
  top: -20px;
}

#bottomCover{
  right: -15px;
  bottom: -20px;
}

#bottomCover, #topCover{
  box-shadow: none;
  width: 24px;
  height: 24px;
  filter: blur(3px);
  z-index: 2;
}
.received{
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 15px;
  color: #FF5E5E;
  padding: 0 16px;
}
.my-voucher-card-note{
  font-size: 12px;
  line-height: 14px;
}
.award-box{
  color: #FF5E5E;
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.award{
  text-align: center;
  font-size: 26px;
  font-weight: 500;
  line-height: 26px;
}
.award-ccy{
  text-align: center;
  font-size: 12px;
  line-height: 12px;
}
.card-tag{
  position: absolute;
  top: 0;
  right: 0;
  color: #FFFFFF;
  font-size: 12px;
  padding: 3px;
  border-radius: 0 8px 0 8px;
}
@media screen and (max-width: 599px){
  .my-voucher-card-list{
    padding: 24px 7px;
  }
}

    
    `]
})
export class CouponListComponent implements OnInit {

  @Input() public vouchers: Reward[] = [];
  @Output() couponSelected = new EventEmitter<Reward>();

  selectCoupon(coupon: Reward) {
    this.couponSelected.emit(coupon);
  }

  public voucherStaus = RewardStatus;
  constructor() { }

  async ngOnInit() {
  }

}
