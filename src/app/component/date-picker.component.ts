import { Component, Input, Output, EventEmitter, OnInit } from "@angular/core";
import * as moment from 'moment';

export interface DateRange {
  startDate: string;
  endDate: string;
}

@Component({
  selector: "app-date-picker",
  template: `
    <div class="date-picker-overlay" *ngIf="isVisible" (click)="onOverlayClick($event)">
      <div class="date-picker-modal" (click)="$event.stopPropagation()">
        <div class="modal-header">
          <h3 class="modal-title">选择日期</h3>
          <button class="close-btn" (click)="onClose()">
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
              <path d="M13.5 4.5L4.5 13.5M4.5 4.5L13.5 13.5" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
        
        <div class="date-selection-area">
          <div class="date-inputs">
            <div class="input-group">
              <label>开始日期</label>
              <div class="custom-date-input" [class.focused]="showStartDatePicker">
                <input 
                  type="text" 
                  class="date-input-field"
                  [value]="formatDateInput(startDate)" 
                  (input)="onStartDateInput($event)"
                  (focus)="onStartDateFocus()"
                  (blur)="onStartDateBlur()"
                  (keydown)="onDateKeydown($event, 'start')"
                  placeholder="日期"
                  maxlength="10"
                  inputmode="numeric"
                  autocomplete="off"
                />
                <svg class="calendar-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" (click)="toggleStartDatePicker()">
                  <path d="M5 1V3M11 1V3M2 7H14M3 3H13C13.5523 3 14 3.44772 14 4V13C14 13.5523 13.5523 14 13 14H3C2.44772 14 2 13.5523 2 13V4C2 3.44772 2.44772 3 3 3Z" stroke="#808080" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="custom-calendar-dropdown" *ngIf="showStartDatePicker" (click)="$event.stopPropagation()">
                <div class="calendar-header">
                  <button class="nav-btn" (click)="navigateMonth(-1, 'start')" (mousedown)="$event.preventDefault()">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M10 4L6 8L10 12" stroke="#808080" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </button>
                  <span class="month-year">{{ startCalendarMonth.format('YYYY年M月') }}</span>
                  <button class="nav-btn" (click)="navigateMonth(1, 'start')" (mousedown)="$event.preventDefault()">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M6 4L10 8L6 12" stroke="#808080" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </button>
                </div>
                <div class="calendar-grid">
                  <div class="weekdays">
                    <div class="weekday" *ngFor="let day of weekdays">{{ day }}</div>
                  </div>
                  <div class="days">
                    <div 
                      class="day" 
                      *ngFor="let day of startCalendarDays"
                      [class.other-month]="day.otherMonth"
                      [class.today]="day.isToday"
                      [class.selected]="day.isSelected"
                      [class.disabled]="day.isDisabled"
                      (click)="selectDate(day, 'start')"
                    >
                      {{ day.day }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="input-separator">-</div>
            <div class="input-group">
              <label>结束日期</label>
              <div class="custom-date-input" [class.focused]="showEndDatePicker">
                <input 
                  type="text" 
                  class="date-input-field"
                  [value]="formatDateInput(endDate)" 
                  (input)="onEndDateInput($event)"
                  (focus)="onEndDateFocus()"
                  (blur)="onEndDateBlur()"
                  (keydown)="onDateKeydown($event, 'end')"
                  placeholder="日期"
                  maxlength="10"
                  inputmode="numeric"
                  autocomplete="off"
                />
                <svg class="calendar-icon" width="16" height="16" viewBox="0 0 16 16" fill="none" (click)="toggleEndDatePicker()">
                  <path d="M5 1V3M11 1V3M2 7H14M3 3H13C13.5523 3 14 3.44772 14 4V13C14 13.5523 13.5523 14 13 14H3C2.44772 14 2 13.5523 2 13V4C2 3.44772 2.44772 3 3 3Z" stroke="#808080" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="custom-calendar-dropdown" *ngIf="showEndDatePicker" (click)="$event.stopPropagation()">
                <div class="calendar-header">
                  <button class="nav-btn" (click)="navigateMonth(-1, 'end')" (mousedown)="$event.preventDefault()">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M10 4L6 8L10 12" stroke="#808080" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </button>
                  <span class="month-year">{{ endCalendarMonth.format('YYYY年M月') }}</span>
                  <button class="nav-btn" (click)="navigateMonth(1, 'end')" (mousedown)="$event.preventDefault()">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M6 4L10 8L6 12" stroke="#808080" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </button>
                </div>
                <div class="calendar-grid">
                  <div class="weekdays">
                    <div class="weekday" *ngFor="let day of weekdays">{{ day }}</div>
                  </div>
                  <div class="days">
                    <div 
                      class="day" 
                      *ngFor="let day of endCalendarDays"
                      [class.other-month]="day.otherMonth"
                      [class.today]="day.isToday"
                      [class.selected]="day.isSelected"
                      [class.disabled]="day.isDisabled"
                      (click)="selectDate(day, 'end')"
                    >
                      {{ day.day }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="validation-message" *ngIf="errorMessage">
            {{ errorMessage }}
          </div>
          
          <div class="quick-select">
            <h4>快速选择</h4>
            <div class="quick-options">
              <div 
                class="option-item" 
                *ngFor="let option of quickOptions"
                [class.selected]="option.selected"
                (click)="selectQuickOption(option)"
              >
                <div class="option-text">
                  <span class="start-date">{{ option.startDate }}</span>
                  <span class="separator">-</span>
                  <span class="end-date">{{ option.endDate }}</span>
                </div>
                <div class="check-icon">
                  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" *ngIf="option.selected">
                    <circle cx="9" cy="9" r="9" fill="#ff5e5e"/>
                    <path d="M5.5 9L8 11.5L12.5 6.5" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <div class="empty-circle" *ngIf="!option.selected"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="modal-actions">
          <button class="cancel-btn" (click)="onClose()">取消</button>
          <button class="confirm-btn" (click)="onConfirm()" [disabled]="!isValid">确认</button>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .date-picker-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: flex;
        align-items: flex-end;
        justify-content: center;
      }

      .date-picker-modal {
        background: #ffffff;
        width: 100%;
        height: auto;
        overflow-y: auto;
        position: relative;
        animation: slideUp 0.3s ease-out;
      }

      @keyframes slideUp {
        from {
          transform: translateY(100%);
          opacity: 0;
        }
        to {
          transform: translateY(0);
          opacity: 1;
        }
      }

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32px 24px 0;
        width: calc(100% - 48px);
        margin: 0 auto;
      }

      .modal-title {
        font-family: 'PingFang SC', sans-serif;
        font-size: 20px;
        font-weight: 600;
        color: #000000;
        margin: 0;
      }

      .close-btn {
        background: none;
        border: none;
        width: 24px;
        height: 24px;
        cursor: pointer;
        color: #000000;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .date-selection-area {
        padding: 32px 24px 0;
        width: calc(100% - 48px);
        margin: 0 auto;
      }

      .date-inputs {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 24px;
      }

      @media (max-width: 768px) {
        .date-inputs {
          flex-direction: row;
          gap: 2px;
          align-items: center;
          padding: 0 4px;
        }
      }

      .input-group {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      @media (max-width: 768px) {
        .input-group {
          gap: 2px;
          min-width: 0;
          flex: 1;
        }
      }

      .input-group label {
        font-family: 'PingFang SC', sans-serif;
        font-size: 14px;
        font-weight: 500;
        color: #808080;
      }

      @media (max-width: 768px) {
        .input-group label {
          font-size: 11px;
          white-space: nowrap;
        }
      }

      .input-group {
        position: relative;
      }

      .custom-date-input {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 1px solid #e6e6e6;
        border-radius: 8px;
        font-family: 'PingFang SC', sans-serif;
        font-size: 14px;
        background: #ffffff;
        transition: all 0.2s ease;
        min-height: 48px;
      }

      .custom-date-input:hover {
        border-color: #ff5e5e;
      }

      .custom-date-input.focused {
        border-color: #ff5e5e;
        box-shadow: 0 0 0 2px rgba(255, 94, 94, 0.1);
      }

      .date-input-field {
        flex: 1;
        border: none;
        outline: none;
        padding: 12px 40px 12px 16px;
        font-family: 'PingFang SC', sans-serif;
        font-size: 14px;
        font-weight: 500;
        color: #000000;
        background: transparent;
      }

      .date-input-field::placeholder {
        color: #c7c7c7;
        font-weight: 400;
      }

      @media (max-width: 768px) {
        .date-input-field::placeholder {
          font-size: 10px;
        }
      }

      .calendar-icon {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        transition: transform 0.2s ease;
        width: 16px;
        height: 16px;
      }

      .calendar-icon:hover {
        transform: scale(1.1);
      }

      .custom-calendar-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #ffffff;
        border: 1px solid #e6e6e6;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        margin-top: 4px;
        animation: slideDown 0.2s ease;
      }

      @keyframes slideDown {
        from {
          opacity: 0;
          transform: translateY(-4px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 移动端日历弹窗动画优化 */
      @media (max-width: 768px) {
        .custom-calendar-dropdown {
          animation: mobileSlideUp 0.2s ease-out;
        }

        @keyframes mobileSlideUp {
          from {
            opacity: 0;
            transform: translate(-50%, -45%) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
          }
        }
      }

      .calendar-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;
      }

      .nav-btn {
        background: none;
        border: none;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.2s ease;
      }

      .nav-btn:hover {
        background: #f9f9f9;
      }

      .month-year {
        font-family: 'PingFang SC', sans-serif;
        font-size: 16px;
        font-weight: 600;
        color: #000000;
      }

      .calendar-grid {
        padding: 16px;
      }

      .weekdays {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 4px;
        margin-bottom: 8px;
      }

      .weekday {
        text-align: center;
        font-family: 'PingFang SC', sans-serif;
        font-size: 12px;
        font-weight: 500;
        color: #808080;
        padding: 8px 0;
      }

      .days {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 4px;
      }

      .day {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'PingFang SC', sans-serif;
        font-size: 14px;
        font-weight: 500;
        color: #000000;
        cursor: pointer;
        border-radius: 6px;
        transition: all 0.2s ease;
        position: relative;
      }

      .day:hover:not(.disabled) {
        background: #fff5f5;
        color: #ff5e5e;
      }

      .day.other-month {
        color: #c7c7c7;
      }

      .day.today {
        background: #ff5e5e;
        color: #ffffff;
      }

      .day.selected {
        background: #ff5e5e;
        color: #ffffff;
      }

      .day.disabled {
        color: #e6e6e6;
        cursor: not-allowed;
      }

      .day.disabled:hover {
        background: none;
        color: #e6e6e6;
      }


      /* 移动端触摸优化 */
      @media (max-width: 768px) {
        .day:active {
          background: #ff5e5e !important;
          color: #ffffff !important;
          transform: scale(0.95);
        }

        .nav-btn:active {
          background: #f0f0f0;
          transform: scale(0.95);
        }

      }

      .input-separator {
        font-family: 'Montserrat', sans-serif;
        font-weight: bold;
        color: #808080;
        margin-top: 20px;
      }

      @media (max-width: 768px) {
        .input-separator {
          margin-top: 16px;
          text-align: center;
          font-size: 12px;
          padding: 0 2px;
          flex-shrink: 0;
        }
      }

      .validation-message {
        color: #ff5e5e;
        font-size: 14px;
        margin-bottom: 16px;
        text-align: center;
      }

      .quick-select h4 {
        font-family: 'PingFang SC', sans-serif;
        font-size: 16px;
        font-weight: 600;
        color: #000000;
        margin: 0 0 16px 0;
      }

      .quick-options {
        display: flex;
        flex-direction: column;
        gap: 0;
      }

      .option-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid #e6e6e6;
        cursor: pointer;
        transition: background-color 0.2s ease;
      }

      .option-item:hover {
        background-color: #f9f9f9;
      }

      .option-text {
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: 'PingFang SC', sans-serif;
        font-size: 14px;
        font-weight: 600;
        color: #808080;
      }

      .separator {
        font-family: 'Montserrat', sans-serif;
        font-weight: bold;
      }

      .check-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .empty-circle {
        width: 18px;
        height: 18px;
        border: 1px solid #e6e6e6;
        border-radius: 50%;
        background: #ffffff;
      }

      .modal-actions {
        display: flex;
        gap: 16px;
        padding: 32px 24px;
        width: calc(100% - 48px);
        margin: 0 auto;
      }

      .cancel-btn {
        flex: 1;
        padding: 8px 24px;
        border: 1px solid #ff5e5e;
        border-radius: 4px;
        background: #ffffff;
        color: #ff5e5e;
        font-family: 'PingFang SC', sans-serif;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .cancel-btn:hover {
        background: #fff5f5;
      }

      .confirm-btn {
        flex: 1;
        padding: 8px 24px;
        border: 1px solid #ff5e5e;
        border-radius: 4px;
        background: #ff5e5e;
        color: #ffffff;
        font-family: 'PingFang SC', sans-serif;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .confirm-btn:hover:not(:disabled) {
        background: #e54e4e;
        border-color: #e54e4e;
      }

      .confirm-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      /* Mobile styles */
      @media (max-width: 768px) {
        .date-picker-overlay {
          align-items: flex-end;
        }
        
        .date-picker-modal {
          border-radius: 24px 24px 0 0;
          width: 100%;
          max-width: none;
          height: auto;
          min-height: 60vh;
        }
        
        .modal-header {
          width: calc(100% - 48px);
        }
        
        .date-selection-area {
          width: calc(100% - 32px);
          padding: 24px 16px 0;
        }
        
        .modal-actions {
          width: calc(100% - 48px);
        }

        .custom-date-input {
          min-height: 36px;
          border-radius: 6px;
          position: relative;
        }

        .date-input-field {
          font-size: 12px;
          padding: 6px 24px 6px 8px;
        }

        .calendar-icon {
          width: 14px;
          height: 14px;
          right: 6px;
        }

        .custom-calendar-dropdown {
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 90vw;
          max-width: 320px;
          max-height: 70vh;
          border-radius: 12px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
          z-index: 1001;
          overflow-y: auto;
          opacity: 1;
          transition: opacity 0.15s ease-in-out;
        }

        .calendar-header {
          padding: 16px 12px;
        }

        .nav-btn {
          width: 36px;
          height: 36px;
          border-radius: 6px;
        }

        .month-year {
          font-size: 16px;
        }

        .calendar-grid {
          padding: 12px;
        }

        .weekday {
          font-size: 12px;
          padding: 8px 0;
        }

        .day {
          width: 36px;
          height: 36px;
          font-size: 14px;
          border-radius: 6px;
        }

      }

      /* PC styles */
      @media (min-width: 769px) {
        .date-picker-overlay {
          align-items: center;
        }
        
        .date-picker-modal {
          border-radius: 24px;
          width: 70%;
          max-width: 800px;
          min-width: 500px;
          height: auto;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .modal-header {
          width: calc(100% - 96px);
          padding: 32px 48px 0;
        }
        
        .date-selection-area {
          width: calc(100% - 96px);
          padding: 32px 48px 0;
        }
        
        .modal-actions {
          width: calc(100% - 96px);
          padding: 32px 48px;
        }
      }
    `,
  ],
})
export class DatePickerComponent implements OnInit {
  @Input() isVisible: boolean = false;
  @Input() initialStartDate?: string;
  @Input() initialEndDate?: string;
  
  @Output() dateSelected = new EventEmitter<DateRange>();
  @Output() closed = new EventEmitter<void>();

  startDate: string = '';
  endDate: string = '';
  errorMessage: string = '';
  quickOptions: any[] = [];
  
  showStartDatePicker: boolean = false;
  showEndDatePicker: boolean = false;
  startCalendarMonth: any;
  endCalendarMonth: any;
  startCalendarDays: any[] = [];
  endCalendarDays: any[] = [];
  weekdays: string[] = ['日', '一', '二', '三', '四', '五', '六'];
  startInputValue: string = '';
  endInputValue: string = '';
  
  get isValid(): boolean {
    return this.startDate && this.endDate && !this.errorMessage;
  }

  get maxStartDate(): string {
    return moment().format('YYYY-MM-DD');
  }

  get minEndDate(): string {
    return this.startDate || moment().subtract(1, 'month').format('YYYY-MM-DD');
  }

  get maxEndDate(): string {
    if (this.startDate) {
      return moment(this.startDate).add(1, 'month').format('YYYY-MM-DD');
    }
    return moment().format('YYYY-MM-DD');
  }

  ngOnInit() {
    this.initializeDates();
    this.generateQuickOptions();
    this.initializeCalendars();
  }

  initializeDates() {
    if (this.initialStartDate && this.initialEndDate) {
      this.startDate = moment(this.initialStartDate, 'YYYY.M.D').format('YYYY-MM-DD');
      this.endDate = moment(this.initialEndDate, 'YYYY.M.D').format('YYYY-MM-DD');
    } else {
      // 默认选中过去一个月：上个月的1号到最后一天
      const lastMonth = moment().subtract(1, 'month');
      this.startDate = lastMonth.clone().startOf('month').format('YYYY-MM-DD');
      this.endDate = lastMonth.clone().endOf('month').format('YYYY-MM-DD');
    }
    this.startInputValue = this.startDate;
    this.endInputValue = this.endDate;
  }

  generateQuickOptions() {
    const options = [];
    const today = moment();
    
    // 当前月的1号至今天
    options.push({
      startDate: today.clone().startOf('month').format('YYYY.M.D'),
      endDate: today.format('YYYY.M.D'),
      startDateISO: today.clone().startOf('month').format('YYYY-MM-DD'),
      endDateISO: today.format('YYYY-MM-DD'),
      selected: false
    });
    
    // 过去5个月，每个月的1号到最后一天
    for (let i = 1; i <= 5; i++) {
      const monthDate = today.clone().subtract(i, 'month');
      const startDate = monthDate.clone().startOf('month');
      const endDate = monthDate.clone().endOf('month');
      
      options.push({
        startDate: startDate.format('YYYY.M.D'),
        endDate: endDate.format('YYYY.M.D'),
        startDateISO: startDate.format('YYYY-MM-DD'),
        endDateISO: endDate.format('YYYY-MM-DD'),
        selected: false
      });
    }
    
    this.quickOptions = options;
    this.updateQuickOptionsSelection();
  }

  updateQuickOptionsSelection() {
    this.quickOptions.forEach(option => {
      option.selected = option.startDateISO === this.startDate && option.endDateISO === this.endDate;
    });
  }


  validateDateRange() {
    this.errorMessage = '';
    
    if (!this.startDate || !this.endDate) {
      return;
    }

    const start = moment(this.startDate);
    const end = moment(this.endDate);
    
    if (start.isAfter(end)) {
      this.errorMessage = '开始日期不能晚于结束日期';
      return;
    }

    const diffDays = end.diff(start, 'days');
    if (diffDays > 31) {
      this.errorMessage = '日期范围不能超过一个月';
      return;
    }

    if (end.isAfter(moment())) {
      this.errorMessage = '结束日期不能超过今天';
      return;
    }
  }

  selectQuickOption(option: any) {
    this.startDate = option.startDateISO;
    this.endDate = option.endDateISO;
    this.validateDateRange();
    this.updateQuickOptionsSelection();
  }

  onConfirm() {
    if (this.isValid) {
      const result: DateRange = {
        startDate: moment(this.startDate).format('YYYY.M.D'),
        endDate: moment(this.endDate).format('YYYY.M.D')
      };
      this.dateSelected.emit(result);
      this.onClose();
    }
  }

  onClose() {
    this.closed.emit();
  }

  onOverlayClick(event: Event) {
    if (event.target === event.currentTarget) {
      this.onClose();
    }
    this.showStartDatePicker = false;
    this.showEndDatePicker = false;
  }

  initializeCalendars() {
    this.startCalendarMonth = this.startDate ? moment(this.startDate) : moment();
    this.endCalendarMonth = this.endDate ? moment(this.endDate) : moment();
    this.generateCalendarDays();
  }

  generateCalendarDays() {
    this.startCalendarDays = this.getCalendarDays(this.startCalendarMonth, this.startDate);
    this.endCalendarDays = this.getCalendarDays(this.endCalendarMonth, this.endDate);
  }

  getCalendarDays(month: any, selectedDate: string): any[] {
    const days = [];
    const startOfMonth = month.clone().startOf('month');
    const endOfMonth = month.clone().endOf('month');
    const startOfWeek = startOfMonth.clone().startOf('week');
    const endOfWeek = endOfMonth.clone().endOf('week');
    
    let current = startOfWeek.clone();
    const today = moment();
    
    while (current.isSameOrBefore(endOfWeek, 'day')) {
      const dayData = {
        day: current.date(),
        date: current.format('YYYY-MM-DD'),
        otherMonth: !current.isSame(month, 'month'),
        isToday: current.isSame(today, 'day'),
        isSelected: selectedDate && current.format('YYYY-MM-DD') === selectedDate,
        isDisabled: current.isAfter(today, 'day')
      };
      days.push(dayData);
      current.add(1, 'day');
    }
    
    return days;
  }

  toggleStartDatePicker() {
    this.showStartDatePicker = !this.showStartDatePicker;
    this.showEndDatePicker = false;
    if (this.showStartDatePicker) {
      this.generateCalendarDays();
    }
  }

  toggleEndDatePicker() {
    this.showEndDatePicker = !this.showEndDatePicker;
    this.showStartDatePicker = false;
    if (this.showEndDatePicker) {
      this.generateCalendarDays();
    }
  }

  navigateMonth(direction: number, type: 'start' | 'end') {
    if (type === 'start') {
      this.startCalendarMonth.add(direction, 'month');
    } else {
      this.endCalendarMonth.add(direction, 'month');
    }
    this.generateCalendarDays();
    
    // 移动端点击导航按钮后防止自动关闭
    if (this.isMobileDevice()) {
      setTimeout(() => {
        if (type === 'start') {
          this.showStartDatePicker = true;
        } else {
          this.showEndDatePicker = true;
        }
      }, 10);
    }
  }

  selectDate(day: any, type: 'start' | 'end') {
    if (day.isDisabled) return;
    
    if (type === 'start') {
      this.startDate = day.date;
      this.showStartDatePicker = false;
    } else {
      this.endDate = day.date;
      this.showEndDatePicker = false;
    }
    
    this.validateDateRange();
    this.updateQuickOptionsSelection();
    this.generateCalendarDays();
  }

  formatDateDisplay(dateStr: string): string {
    if (!dateStr) return '';
    return moment(dateStr).format('YYYY年M月D日');
  }

  formatDateInput(dateStr: string): string {
    if (!dateStr) return '';
    return moment(dateStr).format('YYYY-MM-DD');
  }


  onStartDateInput(event: any) {
    this.startInputValue = event.target.value;
    this.parseAndValidateDate(event.target.value, 'start');
  }

  onEndDateInput(event: any) {
    this.endInputValue = event.target.value;
    this.parseAndValidateDate(event.target.value, 'end');
  }

  parseAndValidateDate(inputValue: string, type: 'start' | 'end') {
    // 支持多种日期格式
    const formats = ['YYYY-MM-DD', 'YYYY/MM/DD', 'YYYY.MM.DD', 'YYYY-M-D', 'YYYY/M/D', 'YYYY.M.D'];
    let validDate = null;
    
    for (const format of formats) {
      const parsed = moment(inputValue, format, true);
      if (parsed.isValid()) {
        validDate = parsed;
        break;
      }
    }
    
    if (validDate) {
      const dateStr = validDate.format('YYYY-MM-DD');
      if (type === 'start') {
        this.startDate = dateStr;
        this.startCalendarMonth = validDate.clone();
      } else {
        this.endDate = dateStr;
        this.endCalendarMonth = validDate.clone();
      }
      this.validateDateRange();
      this.updateQuickOptionsSelection();
      this.generateCalendarDays();
    }
  }

  onStartDateFocus() {
    this.showStartDatePicker = true;
    this.showEndDatePicker = false;
  }

  onEndDateFocus() {
    this.showEndDatePicker = true;
    this.showStartDatePicker = false;
  }

  onStartDateBlur() {
    // 移动端延迟更长时间关闭，桌面端正常延迟
    const delay = this.isMobileDevice() ? 300 : 200;
    setTimeout(() => {
      this.showStartDatePicker = false;
    }, delay);
  }

  onEndDateBlur() {
    // 移动端延迟更长时间关闭，桌面端正常延迟
    const delay = this.isMobileDevice() ? 300 : 200;
    setTimeout(() => {
      this.showEndDatePicker = false;
    }, delay);
  }

  isMobileDevice(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;
  }

  onDateKeydown(event: KeyboardEvent, type: 'start' | 'end') {
    if (event.key === 'Enter') {
      if (type === 'start') {
        this.showStartDatePicker = false;
      } else {
        this.showEndDatePicker = false;
      }
    } else if (event.key === 'Escape') {
      this.showStartDatePicker = false;
      this.showEndDatePicker = false;
    }
  }



  onStartDateChange(event: any) {
    this.startDate = event.target.value;
    this.validateDateRange();
    this.updateQuickOptionsSelection();
    this.generateCalendarDays();
  }

  onEndDateChange(event: any) {
    this.endDate = event.target.value;
    this.validateDateRange();
    this.updateQuickOptionsSelection();
    this.generateCalendarDays();
  }

  constructor() {}
}