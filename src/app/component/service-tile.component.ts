import { Component, Input, OnInit } from "@angular/core";
import { defaultBillingCycles, ServiceService } from "@flashvpn-io/web-core";
import { BuyType, DeductionType, RouterBuyType } from "@flashvpn-io/web-core";
import { Router } from "@angular/router";
import { APIManager } from "@flashvpn-io/web-core";
import { DialogsService } from "../utils/dialogs/dialogs.service";

@Component({
  selector: "app-service-tile",
  template: `
    <div class="serveItem">
      <div class="serveHead">
        <div *ngIf="!noService()" class="serveId">订阅中 | 服务ID: {{ (serviceService.currentService$ | async)?.id }}</div>
        <div *ngIf="noService()" class="serveId">未订阅</div>
        <div *ngIf="!noService()" class="serveExpire">
          {{ (serviceService.currentService$ | async)?.getNextduedate() }}
          到期
        </div>
      </div>
      <div class="serveContent">
        <div class="serveContentItem">
          <div class="serveContentItemLeft">
            <div class="serveContentProgress">
              <div class="progressContainer">
                <div class="progressRing" [style.--percentage]="(serviceService.currentService$ | async)?.remaining() || 0"></div>
                <div class="progressText"></div>
              </div>
              <div class="serveContentTitle">本月剩余流量</div>
            </div>
            <div class="serveContentTextList" *ngIf="!noService()">
              <span class="serveContentText1">{{ (serviceService.currentService$ | async)?.overUsedInGB() }}G</span>
              <span class="serveContentText2">/{{ (serviceService.currentService$ | async)?.usageCapInGB() }}G</span>
            </div>
            <div class="serveContentTextList" *ngIf="noService()">
              <span class="serveContentText2">暂无流量</span>
            </div>
          </div>
          <div *ngIf="!noService()" class="serveContentItemRight">
            <div class="serveContentTitle">流量不够？</div>
            <div class="serveContentLink" (click)="goToFlowData()">购买补充流量包&gt;</div>
          </div>
        </div>
        <div class="serveContentItem">
          <div class="serveContentItemLeft">
            <div class="serveContentProgress">
              <div class="progressContainer">
                <div class="progressRingYellow" [style.--percentage]="(serviceService.currentService$ | async)?.countResetDayPercentage() || 0"></div>
                <div class="progressText"></div>
              </div>
              <div class="serveContentTitle">本月流量重置时间</div>
            </div>
            <div class="serveContentTextList" *ngIf="!noService()">
              <span class="serveContentText1">{{ (serviceService.currentService$ | async)?.countRestTime() }}</span>
              <span class="serveContentText2">{{ (serviceService.currentService$ | async)?.getRegdate() }}</span>
            </div>
            <div class="serveContentTextList" *ngIf="noService()">
              <span class="serveContentText2">暂无重置时间</span>
            </div>
          </div>
          <div class="serveContentItemRight">
            <button mat-raised-button color="warn" (click)="goToPaymentMethods()">
              {{ !noService() ? "续费" : "订阅" }}
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .serveItem {
        height: 100%;
        box-shadow: 0px 0px 10px 0px rgba(255, 94, 94, 0.1);
        position: relative;
      }
      .progressContainer {
        position: relative;
        width: 14px;
        height: 14px;
        margin: 0 auto;
      }
      .progressRing {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: conic-gradient(rgba(255, 94, 94, 1) calc(var(--percentage) * 1%), rgba(255, 94, 94, 0.3) 0);
        transform: rotate(180deg);
      }
      .progressRingYellow {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: conic-gradient(rgba(241, 201, 84, 1) calc(var(--percentage) * 1%), rgba(241, 201, 84, 0.3) 0);
        transform: rotate(180deg);
      }

      .progressText {
        position: absolute;
        width: 7px;
        height: 7px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 5px;
        background-color: #ffffff;
      }

      .serveHead {
        display: grid;
        height: 25px;
        align-items: center;
        grid-template-columns: 1.2fr 1fr;
        background: rgba(241, 201, 84, 0.1);
      }
      .serveId {
        height: 100%;
        font-size: 14px;
        font-weight: 600;
        line-height: 14px;
        text-align: left;
        color: rgba(0, 0, 0, 1);
        background: linear-gradient(180deg, #fff59e 0%, #ffc149 60.42%, #ffbe17 100%);
        display: flex;
        align-items: center;
        padding-left: 20px;
        border-radius: 0 10px 10px 0;
      }
      .serveExpire {
        padding-left: 10px;
        font-size: 12px;
        font-weight: 600;
        line-height: 12px;
        text-align: left;
        color: rgba(128, 128, 128, 1);
      }
      .serveContent {
        display: grid;
        grid-template-rows: 1fr 1fr;
        height: 200px;
      }
      .serveContentItem {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 20px;
        align-items: center;
      }
      .serveContentItemLeft {
        text-align: left;
        display: flex;
        flex-direction: column;
        align-items: start;
        gap: 10px;
      }
      .serveContentItemRight {
        text-align: right;
        display: flex;
        flex-direction: column;
        align-items: end;
        gap: 15px;
      }
      .serveContentTitle {
        font-size: 14px;
        font-weight: 400;
        line-height: 14px;
        text-align: left;
        color: rgba(128, 128, 128, 1);
      }
      .serveContentTextList {
        display: flex;
        flex-direction: row;
        align-items: end;
        gap: 5px;
      }
      .serveContentProgress {
        display: flex;
        flex-direction: row;
        justify-content: end;
        align-items: end;
        gap: 5px;
      }
      .serveContentText1 {
        font-size: 20px;
        font-weight: 600;
        line-height: 20px;
        text-align: left;
        color: rgba(0, 0, 0, 1);
      }
      .serveContentText2 {
        font-size: 14px;
        font-weight: 600;
        line-height: 14px;
        text-align: left;
        color: rgba(128, 128, 128, 1);
      }
      .serveContentText3 {
        font-size: 14px;
        font-weight: 600;
        line-height: 14px;
        text-align: left;
        color: rgba(255, 94, 94, 1);
      }
      .serveContentLink {
        font-size: 14px;
        font-weight: 600;
        line-height: 14px;
        text-align: left;
        color: rgba(255, 94, 94, 1);
        cursor: pointer;
        text-decoration: underline;
      }

      @media (min-width: 767px) {
        .mobile {
          display: none
        }
        .pc {
          display: flex;
        }
      }

      @media (max-width: 767px) {
        .pc {
          display: none;
        }
        .mobile {
          display: flex;
        }
        .serve {
          grid-template-columns: 1fr;
          grid-template-rows: repeat(auto-fill, minmax(200px, 1fr));
          gap: 30px;
          padding: 10px 0 30px 0;
        }
        .resetTime {
          height: 30px;
        }
        .serveItemBox {
          padding: 20px;
        }
        .progressBox {
          box-shadow: none;
        }
        .inviteBox {
          display: none;
        }
        .g {
          width: 137px;
          position: relative;
          font-size: 14px;
          line-height: 14px;
          font-weight: 600;
          color: #000;
          display: flex;
          align-items: center;
        }

        .parent {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: flex-start;
          gap: 6px;
        }

        .div2 {
          position: relative;
          line-height: 20px;
        }

        .div1 {
          border-radius: 2px;
          border: 1px solid #ff5e5e;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          padding: 6px 12px;
          font-size: 14px;
          color: #ff5e5e;
        }

        .frameParent {
          align-self: stretch;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
        }

        .div3 {
          position: absolute;
          top: 0px;
          left: 0px;
          line-height: 100%;
          font-weight: 600;
        }

        .icon {
          position: absolute;
          top: 0px;
          left: 0px;
          width: 14px;
          height: 14px;
        }

        .g5mbps {
          position: absolute;
          top: 2px;
          left: 16px;
          line-height: 100%;
          width: 100%;
        }

        .iconParent {
          position: absolute;
          top: 0px;
          left: 0px;
          width: 186px;
          height: 14px;
        }

        .div4 {
          position: absolute;
          top: 1px;
          left: 16px;
          line-height: 100%;
        }

        .iconGroup {
          position: absolute;
          top: 18px;
          left: 0px;
          width: 148px;
          height: 14px;
        }

        .iconContainer {
          position: absolute;
          top: 36px;
          left: 0px;
          width: 108px;
          height: 14px;
        }

        .frameDiv {
          position: absolute;
          top: 54px;
          left: 0px;
          width: 88px;
          height: 14px;
        }

        .frameGroup {
          position: absolute;
          top: 20px;
          left: 0px;
          width: 186px;
          height: 69px;
        }

        .group {
          width: 186px;
          position: relative;
          height: 93px;
        }

        .mobile {
          width: 100%;
          position: relative;
          border-radius: 0px 0px 2px 2px;
          background-color: #fff;
          border-top: 1px dashed #e6e6e6;
          box-sizing: border-box;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: flex-start;
          padding: 24px 24px 28px;
          gap: 24px;
          cursor: pointer;
          text-align: left;
          font-size: 12px;
          color: #808080;
          font-family: 'PingFang SC';
        }
      }
    `,
  ],
})
export class ServiceTileComponent {
  protected readonly BuyType = BuyType;

  loading: boolean = false;
  upgradeable: boolean = false;
  deviceLimit: number = 5;

  constructor(public router: Router, public serviceService: ServiceService, public apiManager: APIManager, public dialogsService: DialogsService) {
    switch (this.serviceService.currentService$.value?.productId) {
      case 1:
        this.deviceLimit = 5
        break;
      case 2:
        this.deviceLimit = 8
        break;
      case 3:
        this.deviceLimit = 10
        break;
    }
  }

  async goToFlowData() {
    this.loading = true;
    let invoice = await this.apiManager.fetchLatestFlowInvoice(this.serviceService.currentService$.value).toPromise();
    this.loading = false;
    if (invoice && invoice.hadDiscount()) {
      this.dialogsService.openDialog("invoice-check-dialog", {
        invoiceId: invoice.id,
        buyType: BuyType.FLOWDATA,
      });
    } else {
      await this.router.navigate([`purchase-data`]);
    }
  }

  async goToPaymentMethods() {
    this.loading = true;
    let invoice = await this.apiManager.fetchLatestInvoice(this.serviceService.currentService$.value).toPromise();
    this.loading = false;
    if (invoice && invoice.hadDiscount()) {
      this.dialogsService.openDialog("invoice-check-dialog", {
        invoiceId: invoice.id,
        buyType: BuyType.SERVICE,
      });
    } else {
      if (!!this.serviceService.currentService$.value && !this.serviceService.currentService$.value.isDue()) {
        this.router.navigate([`payment-methods/${RouterBuyType.SERVICE}`]);
      } else {
        this.router.navigate([`repurchase`]);
      }
    }
  }

  noService() {
    const currentService = this.serviceService.currentService$.value;
    if (currentService) {
      return currentService.isDue();
    }
    return true;
  }

  upgradeService() {
    this.router.navigate(['/upgrade']);
  }

  getServiceBillingCycle() {
    const currentBillingCycle = defaultBillingCycles.find(cycle => cycle.id === this.serviceService.currentService$.value?.billingcycle);
    if (currentBillingCycle) {
      return currentBillingCycle.unit;
    }
  }

  isUpgradeable() {
    this.upgradeable = this.serviceService.currentService$.value?.productId !== 3;
    return this.upgradeable;
  }
}
