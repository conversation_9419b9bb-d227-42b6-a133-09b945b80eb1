import { Component, Input, Output, EventEmitter } from "@angular/core";
@Component({
  selector: "app-tips",
  template: `
    <div class="tips-container" [style]="{ width: width }">
      <div class="title">{{ title }}</div>
      <div class="content">
        {{ content }}
      </div>
      <div class="foot">
        <button mat-stroked-button color="warn" (click)="onCancelClick()">
          {{ cancelText }}
        </button>
        <button mat-raised-button color="warn" (click)="onOkClick()">
          {{ okText }}
        </button>
      </div>
    </div>
  `,
  styles: [
    `
      .tips-container {
        width: 100%;
      }
      .title {
        font-size: 18px;
        font-weight: 600;
        color: #000000;
        margin-bottom: 20px;
      }
      .content {
        width: 100%;
        margin: 20px 0;
      }
      .foot {
        display: flex;
        justify-content: end;
        align-items: center;
        gap: 10px;
      }
    `,
  ],
})
export class TipsComponent {
  @Input() title: string = "提示";

  @Input() content: string = "";

  @Input() okText: string = "确认";

  @Input() cancelText: string = "取消";

  @Input() width: string = "auto";

  @Output() onOk = new EventEmitter();

  @Output() onCancel = new EventEmitter();

  constructor() {}

  onOkClick() {
    this.onOk.emit();
  }

  onCancelClick() {
    this.onCancel.emit();
  }
}
