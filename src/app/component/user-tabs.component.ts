import { ChangeDetectorRef, Component, EventEmitter, Input, Output } from "@angular/core";
import { OauthType, PaymentMethodService } from "@flashvpn-io/web-core";
import { PayMethod, PayType } from "@flashvpn-io/web-core";
import { BehaviorSubject, merge, Subject } from "rxjs";
import { filter, map, switchMap } from "rxjs/operators";
import { Router } from "@angular/router";

@Component({
  selector: "app-user-tabs",
  template: `
    <div class="tab">
      <h2 i18n (mousedown)="linkTo(OauthType.SIGNIN, 'users/signin')" [class]="activatedType === OauthType.SIGNIN ? 'activated' : ''">login</h2>
      <h2 i18n (mousedown)="linkTo(OauthType.SIGNUP, 'users/signup')" [class]="activatedType === OauthType.SIGNUP ? 'activated' : ''">Sign Up</h2>
    </div>
  `,
  styles: [
    `
      .tab {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 32px;
      }
      .tab h2 {
        font-family: "Libre Franklin";
        font-style: normal;
        font-weight: 600;
        font-size: 28px;
        line-height: 34px;
        width: 160px;
        height: 46px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-bottom: 2px solid transparent;
        cursor: pointer;
        color: #999999;
      }
      .tab h2.activated {
        border-bottom: 2px solid #ff5e5e;
        color: #000000;
      }
    `,
  ],
})
export class UserTabsComponent {
  @Input() public activatedType: OauthType = OauthType.SIGNIN;

  constructor(private router: Router) {}

  linkTo(oauthType: OauthType, path: string) {
    if (oauthType !== this.activatedType) {
      this.router.navigate([path]);
    }
  }

  protected readonly OauthType = OauthType;
}
