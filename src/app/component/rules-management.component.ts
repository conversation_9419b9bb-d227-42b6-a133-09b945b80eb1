import { Component, Inject, <PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarConfig, MAT_SNACK_BAR_DATA } from '@angular/material/snack-bar';
import { RulesService, SystemRule } from '@flashvpn-io/web-core';
import { AddRuleDialogComponent } from './add-rule-dialog.component';

interface RuleWithPlatform extends SystemRule {
  domain: string;
  action: 'Directly' | 'Proxy' | 'Dropped';
  isPlatform?: boolean;
}

// Custom snackbar component
@Component({
  selector: 'custom-success-snackbar',
  template: `
    <div class="success-snackbar-content">
      <div class="content-wrapper">
        <img src="assets/images/icon-success.svg" class="success-icon" alt="Success">
        <span class="success-message">{{data}}</span>
      </div>
    </div>
  `,
  styles: [`
    .success-snackbar-content {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
    }
    .content-wrapper {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .success-icon {
      width: 14px;
      height: 14px;
      flex-shrink: 0;
    }
    .success-message {
      font-family: 'PingFang SC', sans-serif;
      font-size: 14px;
      font-weight: 400;
      line-height: 1em;
      color: #FFFFFF;
    }
  `]
})
export class SuccessSnackbarComponent {
  constructor(@Inject(MAT_SNACK_BAR_DATA) public data: any) { }
}

@Component({
  selector: 'app-rules-management',
  template: `
    <div class="rules-container">
      <!-- Warning Message -->
      <div class="warning-message">
        <img src="assets/images/hint.svg" class="warning-icon" alt="Warning icon">
        <span>已设定的规则将在当连接模式为「规则模式」时生效</span>
      </div>

      <!-- Search and Help -->
      <div class="search-section">
        <div class="custom-search-field">
          <img src="assets/images/search.svg" class="search-icon" alt="Search icon">
          <input type="text" placeholder="输入关键词搜索" [(ngModel)]="searchKeyword" (input)="onSearch()">
          <button *ngIf="searchKeyword" class="clear-button" (click)="clearSearch()">
            X
          </button>
        </div>
        <a href="http://156.255.2.7:9992/" class="help-link">前往「帮助中心」查看更多说明</a>
      </div>

      <!-- Custom Rules Section -->
      <div class="rules-section">
        <h3>自定义规则</h3>
        <div class="rules-list">
          <button mat-stroked-button color="primary" class="add-rule-btn" (click)="onAddRule()">
            <mat-icon>add</mat-icon>
            新增规则
          </button>
          <ng-container *ngFor="let rule of filteredRules">
            <app-rule-unit 
              [domain]="rule.domain" 
              [action]="rule.action"
              (onEdit)="onEditRule(rule)"
              (onDelete)="onDeleteRule(rule)">
            </app-rule-unit>
          </ng-container>
        </div>
      </div>

      <!-- Platform Rules Section -->
      <div class="rules-section" *ngIf="platformRules.length > 0">
        <h3>平台规则</h3>
        <div class="rules-list">
          <ng-container *ngFor="let rule of platformRules">
            <app-rule-unit 
              [domain]="rule.domain" 
              [action]="rule.action"
              [isPlatform]="true">
            </app-rule-unit>
          </ng-container>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .rules-container {
      display: flex;
      flex-direction: column;
      gap: 24px;
      width: 100%;
      max-width: 1001px;
    }

    .warning-message {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background-color: rgba(241, 201, 84, 0.15);
      color: #E9AD00;
      font-size: 12px;
      line-height: 1.5em;
      width: 100%;
    }

    .warning-icon {
      color: #E9AD00;
      font-size: 20px;
      width: 20px;
      height: 20px;
    }

    .search-section {
      display: flex;
      flex-direction: column;
      gap: 8px;
      width: 100%;
    }

    .custom-search-field {
      width: 100%;
      background: #F6F6F6;
      border-radius: 30px;
      display: flex;
      align-items: center;
      padding: 8px;
    }

    .search-icon {
      width: 14px;
      height: 14px;
      flex-shrink: 0;
      margin: 0 8px 0 8px;
    }

    .clear-button {
      background: none;
      border: none;
      padding: 0;
      margin-left: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: bold;
      color: #999;
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .clear-button:hover {
      color: #666;
    }

    input[type="text"] {
      flex: 1;
      border: none;
      background: transparent;
      font-size: 14px;
      padding: 8px 0;
      outline: none;
      color: #000000;
      font-family: 'PingFang SC', sans-serif;
    }

    input[type="text"]::placeholder {
      color: #999999;
      font-family: 'PingFang SC', sans-serif;
    }

    .help-link {
      color: #FF5E5E;
      text-decoration: underline;
      font-size: 14px;
      line-height: 1.4em;
    }

    .rules-section {
      display: flex;
      flex-direction: column;
      gap: 16px;
      width: 100%;
    }

    h3 {
      font-size: 16px;
      font-weight: 600;
      line-height: 1em;
      margin: 0;
      color: #000000;
      font-family: 'PingFang SC', sans-serif;
    }

    .add-rule-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 6px 12px;
      border-color: #FF5E5E;
      color: #FF5E5E;
      width: 100%;
      height: 52px;
      justify-content: center;
      margin: 0;
    }

    .rules-list {
      display: flex;
      flex-direction: column;
      gap: 10px;
      width: 100%;
    }

    ::ng-deep app-rule-unit {
      width: 100%;
      height: 52px;
    }

    ::ng-deep .mat-mdc-card {
      padding: 0;
    }

    @media (min-width: 768px) {
      .rules-list {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
      }

      ::ng-deep app-rule-unit {
        width: 327px;
      }

      .add-rule-btn {
        width: 327px;
      }
    }
  `]
})
export class RulesManagementComponent implements OnInit {
  rules: RuleWithPlatform[] = [];
  platformRules: RuleWithPlatform[] = [];
  filteredRules: RuleWithPlatform[] = [];
  searchKeyword: string = '';

  constructor(
    private rulesService: RulesService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private zone: NgZone
  ) { }

  ngOnInit() {
    this.loadRules();
  }

  loadRules() {
    this.rulesService.getRules().subscribe({
      next: (rules: any) => {
        const typedRules = rules as RuleWithPlatform[];
        this.rules = typedRules.filter(rule => !rule.isPlatform);
        this.filteredRules = this.rules;
        this.platformRules = typedRules.filter(rule => rule.isPlatform);
      },
      error: (error: any) => {
        console.error('Failed to load rules:', error);
        this.showSnackBar('加载规则失败', '关闭');
      }
    });
  }

  clearSearch() {
    this.searchKeyword = '';
    this.onSearch();
  }

  onSearch() {
    if (!this.searchKeyword) {
      this.filteredRules = this.rules;
      return;
    }
    this.filteredRules = this.rules.filter(rule =>
      rule.domain.toLowerCase().includes(this.searchKeyword.toLowerCase())
    );
  }

  onAddRule() {
    const dialogRef = this.dialog.open(AddRuleDialogComponent, {
      width: '375px',
      panelClass: 'add-rule-dialog'
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      if (result?.saved) {
        this.loadRules();
        this.showSuccessSnackBar('新规则已保存');
      }
    });
  }

  onEditRule(rule: RuleWithPlatform) {
    const dialogRef = this.dialog.open(AddRuleDialogComponent, {
      width: '375px',
      panelClass: 'add-rule-dialog',
      data: rule
    });

    dialogRef.afterClosed().subscribe((result: any) => {
      if (result?.saved || result?.deleted) {
        this.loadRules();
        const message = result.saved ? '规则已修改' : '规则已删除';
        this.showSuccessSnackBar(message);
      }
    });
  }

  onDeleteRule(rule: RuleWithPlatform) {
    this.rulesService.deleteRule(rule.id).subscribe({
      next: (response: any) => {
        this.loadRules();
        this.showSuccessSnackBar('规则已删除');
      },
      error: (error: any) => {
        console.error('Failed to delete rule:', error);
        this.showSnackBar('删除规则失败', '关闭');
      }
    });
  }

  private showSnackBar(message: string, action: string) {
    const config: MatSnackBarConfig = {
      duration: 3000,
      horizontalPosition: 'center' as const,
      verticalPosition: 'top' as const
    };

    this.zone.run(() => {
      this.snackBar.open(message, action, config);
    });
  }

  private showSuccessSnackBar(message: string) {
    const config: MatSnackBarConfig = {
      duration: 3000,
      horizontalPosition: 'center' as const,
      verticalPosition: 'top' as const,
      panelClass: ['custom-snackbar']
    };

    this.zone.run(() => {
      this.snackBar.openFromComponent(SuccessSnackbarComponent, {
        ...config,
        data: message
      });
    });
  }
}