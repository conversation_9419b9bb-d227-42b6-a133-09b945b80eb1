import { Component, OnDestroy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Device, DeviceService } from '@flashvpn-io/web-core';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';

interface DeviceViewModel {
  id: string;
  name: string;
  lastLoginTime: string;
  status: 'current' | 'normal' | 'disabled';
}

@Component({
  selector: 'app-device-management',
  template: `
    <div class="device-management">
      <div class="header">
        <h3 class="title">设备管理</h3>
        <div class="device-count">
          <div class="dot"></div>
          <span>已登录 {{ deviceViewModels.length }} 台设备</span>
        </div>
      </div>

      <div *ngIf="isLoading" class="loading-container">
        <div class="loading-spinner"></div>
        <span>加载中...</span>
      </div>

      <div *ngIf="!isLoading" class="device-list">
        <div *ngFor="let device of deviceViewModels" class="device-item">
          <div class="device-info">
            <div class="device-name-wrapper">
              <span class="device-name">{{ device.name }}</span>
              <div *ngIf="device.status === 'current'" class="device-tag">当前设备</div>
              <div *ngIf="device.status === 'disabled'" class="device-tag disabled">禁用中</div>
            </div>
            <span class="login-time">最近登录 {{ device.lastLoginTime }}</span>
          </div>
          <div class="device-actions">
            <button *ngIf="device.status !== 'current'" 
                    class="action-btn"
                    [class.disabled]="device.status === 'disabled' || actionInProgress"
                    [disabled]="actionInProgress"
                    (click)="toggleDeviceStatus(device)">
              {{ device.status === 'disabled' ? '解除禁用' : '禁用' }}
            </button>
            <button *ngIf="device.status !== 'current'"
                    class="action-btn delete-btn"
                    [disabled]="actionInProgress"
                    (click)="deleteDevice(device)">
              删除
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .device-management {
      background: #FFFFFF;
      border-radius: 4px;
      box-shadow: 0px 0px 10px 0px rgba(255, 94, 94, 0.1);
      padding: 32px 24px;
    }

    .header {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-bottom: 24px;
    }

    .title {
      font-family: 'PingFang SC';
      font-weight: 600;
      font-size: 16px;
      line-height: 1em;
      color: #000000;
      margin: 0;
    }

    .device-count {
      display: flex;
      align-items: center;
      width: fit-content;
      gap: 8px;
      padding: 8px 12px;
      height: 24px;
      background: rgba(9, 224, 160, 0.1);
      border-radius: 20px;
      font-family: 'PingFang SC';
      font-weight: 400;
      font-size: 14px;
      color: #09E0A0;
    }

    .dot {
      width: 6px;
      height: 6px;
      background: #09E0A0;
      border-radius: 50%;
    }

    .device-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .device-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
    }

    .device-info {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .device-name-wrapper {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-wrap: wrap;
    }

    .device-name {
      font-family: 'PingFang SC';
      font-weight: 600;
      font-size: 14px;
      line-height: 1em;
      color: #000000;
    }

    .device-tag {
      display: flex;
      align-items: center;
      padding: 4px 8px;
      background: rgba(128, 128, 128, 0.1);
      border-radius: 20px;
      font-family: 'PingFang SC';
      font-weight: 400;
      font-size: 12px;
      color: #808080;
    }

    .device-tag.disabled {
      background: rgba(128, 128, 128, 0.1);
    }

    .login-time {
      font-family: 'PingFang SC';
      font-weight: 400;
      font-size: 12px;
      line-height: 1em;
      color: #808080;
    }

    .action-btn {
      padding: 6px 12px;
      border: 1px solid #FF5E5E;
      border-radius: 2px;
      background: transparent;
      font-family: 'PingFang SC';
      font-weight: 400;
      font-size: 14px;
      line-height: 1.43;
      color: #FF5E5E;
      cursor: pointer;
      min-width: 80px;
      text-align: center;
      margin-right: 8px;
    }

    .delete-btn {
      border: 1px solid #808080;
      color: #808080;
    }

    .delete-btn:hover {
      background: rgba(128, 128, 128, 0.1);
    }

    .action-btn:hover {
      background: rgba(255, 94, 94, 0.1);
    }

    .action-btn.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 0;
      color: #808080;
      font-size: 14px;
      gap: 16px;
    }

    .loading-spinner {
      width: 30px;
      height: 30px;
      border: 3px solid rgba(255, 94, 94, 0.2);
      border-top-color: #FF5E5E;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    @media screen and (max-width: 599px) {
      .device-management {
        padding: 24px 16px;
      }

      .header {
        gap: 12px;
        margin-bottom: 16px;
      }

      .device-list {
        gap: 8px;
      }

      .device-item {
        padding: 8px 0;
      }

      .device-info {
        gap: 6px;
      }

      .action-btn {
        padding: 4px 10px;
        font-size: 13px;
      }
    }
  `]
})
export class DeviceManagementComponent implements OnInit, OnDestroy {
  deviceViewModels: DeviceViewModel[] = [];
  private subscriptions: Subscription = new Subscription();
  isLoading: boolean = true;
  actionInProgress: boolean = false;
  verificationCode: string = '';

  constructor(
    private deviceService: DeviceService,
    private dialog: MatDialog,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadDevices();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  loadDevices(): void {
    this.isLoading = true;
    this.subscriptions.add(
      this.deviceService.getAllDevices().pipe(
        finalize(() => this.isLoading = false)
      ).subscribe(
        (devices) => {
          this.mapToViewModel(devices);
        },
        (error) => {
          console.error('Error loading devices:', error);
          // Handle error scenario
        }
      )
    );
  }

  private mapToViewModel(devices: Device[]): void {
    // Get current device ID (could be stored in localStorage or determined by other means)
    const currentDeviceId = this.getCurrentDeviceId();

    this.deviceViewModels = devices.map(device => {
      return {
        id: device.id.toString(),
        name: device.deviceName,
        lastLoginTime: this.formatDate(device.updatedAt),
        status: this.getDeviceStatus(device, currentDeviceId)
      };
    });
  }

  private getCurrentDeviceId(): string {
    // This is a placeholder - implement according to your app's logic for identifying current device
    // For example, you might store this in localStorage when a user logs in
    return localStorage.getItem('currentDeviceId') || '';
  }

  private getDeviceStatus(device: Device, currentDeviceId: string): 'current' | 'normal' | 'disabled' {
    if (device.id.toString() === currentDeviceId) {
      return 'current';
    }
    return device.banned ? 'disabled' : 'normal';
  }

  private formatDate(date: Date): string {
    if (!date) return '';
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).replace(/\//g, '.');
  }

  toggleDeviceStatus(device: DeviceViewModel): void {
    if (this.actionInProgress) return;

    const deviceId = parseInt(device.id);
    const isBanning = device.status === 'normal';

    // Navigate to the appropriate page
    if (isBanning) {
      this.router.navigate(['/devices/ban', deviceId]);
    } else {
      this.router.navigate(['/devices/unban', deviceId]);
    }
  }

  requestVerificationCode(device: DeviceViewModel): void {
    this.subscriptions.add(
      this.deviceService.requestVerificationCode().pipe(
        finalize(() => this.actionInProgress = false)
      ).subscribe(
        (response) => {
          // Show dialog to enter verification code
          this.showVerificationDialog(device);
        },
        (error) => {
          console.error('Error requesting verification code:', error);
          this.showErrorDialog('无法获取验证码，请稍后重试');
        }
      )
    );
  }

  showVerificationDialog(device: DeviceViewModel): void {
    // Implement dialog to ask for verification code
    // This is a placeholder - implement with your UI framework
    const code = prompt('请输入发送到您邮箱的验证码');
    if (code) {
      this.verificationCode = code;
      this.toggleDeviceStatus(device);
    }
  }

  showErrorDialog(message: string): void {
    // Implement error dialog
    // This is a placeholder - implement with your UI framework
    alert(message);
  }

  deleteDevice(device: DeviceViewModel): void {
    if (this.actionInProgress) return;

    if (confirm('确定要删除此设备吗？此操作无法撤销。')) {
      this.actionInProgress = true;
      const deviceId = parseInt(device.id);

      this.subscriptions.add(
        this.deviceService.deleteDevice(deviceId).pipe(
          finalize(() => this.actionInProgress = false)
        ).subscribe(
          (result) => {
            if (result.success) {
              // Remove from local list
              this.deviceViewModels = this.deviceViewModels.filter(d => d.id !== device.id);
            } else {
              this.showErrorDialog('删除设备失败，请稍后重试');
            }
          },
          (error) => {
            console.error('Error deleting device:', error);
            this.showErrorDialog('删除设备失败，请稍后重试');
          }
        )
      );
    }
  }
} 