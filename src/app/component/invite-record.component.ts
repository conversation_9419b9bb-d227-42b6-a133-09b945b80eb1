import { Component, OnInit, ViewChild } from "@angular/core";
import { UsersService } from "@flashvpn-io/web-core";
import { Router } from "@angular/router";
import { NotificationService, WINDOW_SRCOLL } from "@flashvpn-io/web-core";
import { DialogsService } from "../utils/dialogs/dialogs.service";
import { APIManager } from "@flashvpn-io/web-core";
import * as _ from "lodash";
import { InviteRecord } from "@flashvpn-io/web-core";
import { RewardPageDto } from "@flashvpn-io/web-core";
import { AppService } from "../app.service";

@Component({
  selector: "app-invite-record",
  template: `
    <div class="invite-record">
      <mat-progress-bar *ngIf="loading" mode="indeterminate"></mat-progress-bar>
      <table mat-table [dataSource]="inviteRecords" class="record-table">
        <ng-container matColumnDef="invitTime">
          <th mat-header-cell *matHeaderCellDef>邀请时间</th>
          <td mat-cell *matCellDef="let element">{{ element.invitTime }}</td>
        </ng-container>
        <ng-container matColumnDef="inviteeEmail">
          <th mat-header-cell *matHeaderCellDef>好友邮箱</th>
          <td mat-cell *matCellDef="let element">{{ element?.inviteeEmail }}</td>
        </ng-container>
        <ng-container matColumnDef="award">
          <th mat-header-cell *matHeaderCellDef>获得奖励</th>
          <td mat-cell *matCellDef="let element">
            <span class="award">+$ {{ element.award }}</span>
          </td>
        </ng-container>
        <ng-container matColumnDef="expireDate">
          <th mat-header-cell *matHeaderCellDef>奖励后到期日</th>
          <td mat-cell *matCellDef="let element">{{ element.expireDate }}</td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <div class="no-data" *ngIf="!inviteRecords || inviteRecords.length === 0">无数据</div>
      <mat-paginator
        [length]="paginator.length"
        [pageSize]="paginator.pageSize"
        [pageSizeOptions]="[3, 5, 10, 25, 100]"
        (page)="pageChange($event)"></mat-paginator>
    </div>
    <div class="mobile-box">
      <div class="invite-record-mobile" *ngFor="let inviteItem of inviteRecordsAll">
        <div class="record-item">
          <div class="item-head">
            <div class="item-head-left">{{ inviteItem?.inviteeEmail }}</div>
            <div class="item-head-right">{{ inviteItem?.invitTime }}</div>
          </div>
          <div class="item-content">
            <div class="item-content-left">
              <div>奖励后到期日</div>
              <div class="item-expire-date">
                {{ inviteItem.expireDate }}
              </div>
            </div>
            <div class="item-content-right">+$ {{ inviteItem.award }}</div>
          </div>
        </div>
      </div>
      <div class="mobile-foot" *ngIf="inviteLoading">加载中...</div>
      <div class="mobile-foot" *ngIf="!inviteLoading && (!inviteRecordsAll || inviteRecordsAll.length < 1)">无数据</div>
    </div>
  `,
  styles: [
    `
      .invite-record {
        width: 100%;
      }
      .record-table {
        width: 100%;
      }
      ::ng-deep .mat-header-cell {
        background-color: #f2f2f2;
      }
      ::ng-deep th.mat-header-cell {
        text-align: center !important;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 800;
        font-size: 14px;
        line-height: 20px;
        color: #333333;
      }
      ::ng-deep td.mat-cell {
        text-align: center !important;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #808080;
      }
      .award {
        font-family: "Libre Franklin";
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 19px;
        color: #3fe07f;
      }
      .invite-record-mobile {
        display: none;
        width: 100%;
      }
      .record-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 15px;
        gap: 20px;
        margin: 15px 0;
        width: 100%;
        height: 100px;
        background: #ffffff;
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.05);
        border-radius: 2px;
      }
      .item-head {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .item-head-left {
        width: 200px;
        font-family: "PingFang SC";
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #000000;
      }
      .item-head-right {
        width: 50%;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 17px;
        color: #c4c4c4;
        text-align: right;
      }
      .item-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
      }
      .item-content-left {
        width: 50%;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #808080;
      }
      .item-expire-date {
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 30px;
        color: #000000;
      }
      .item-content-right {
        width: 50%;
        text-align: right;
        font-family: "Libre Franklin";
        font-style: normal;
        font-weight: 500;
        font-size: 24px;
        line-height: 29px;
        color: #21d37e;
      }
      .mobile-foot {
        width: 100%;
        height: 100px;
        display: none;
        align-items: center;
        justify-content: center;
      }
      .mobile-box {
        min-height: calc(100vh - 130px);
      }
      .no-data {
        display: flex;
        width: 100%;
        justify-content: center;
        align-items: center;
        height: 100px;
        color: #999;
      }
      @media screen and (max-width: 599px) {
        .invite-record {
          display: none;
          margin: 0;
        }
        .invite-record-mobile {
          display: flex;
        }
        .mobile-foot {
          display: flex;
        }
      }
    `,
  ],
})
export class InviteRecordComponent implements OnInit {
  public loading = false;
  public inviteRecords: InviteRecord[] = [];
  public inviteRecordsAll: InviteRecord[] = [];
  public displayedColumns: string[] = ["invitTime", "inviteeEmail", "award", "expireDate"];
  public paginator = {
    length: 0,
    pageSize: 5,
    pageIndex: 0,
  };
  public inviteLoading = false;

  constructor(
    public usersService: UsersService,
    private appService: AppService,
    private router: Router,
    private notificationService: NotificationService,
    public dialogsService: DialogsService,
    private apiManager: APIManager
  ) { }

  async ngOnInit() {
    window.scrollTo();
    await this.getRewards();
    this.notificationService.register(WINDOW_SRCOLL).subscribe(async (value) => {
      if (this.dialogsService.isMobile() || window.innerWidth <= 599) {
        this.inviteLoading = true;
        const paginator = this.paginator;
        paginator.pageIndex++;
        this.paginator = { ...paginator };
        await this.getRewards();
      }
    });
  }

  async getRewards() {
    this.loading = true;
    const pageInfo = new RewardPageDto();
    pageInfo.limitnum = this.paginator.pageSize;
    pageInfo.limitstart = this.paginator.pageIndex * this.paginator.pageSize;
    pageInfo.orderby = "createdAt";
    pageInfo.order = "descend";
    this.apiManager.getInvitRewardsByPage(pageInfo).subscribe(
      (res) => {
        this.inviteRecords = res.rewards;
        this.paginator = { ...this.paginator, length: res.total };
        this.inviteRecordsAll.push(...this.inviteRecords);
        _.uniqBy(this.inviteRecordsAll, "id");
        if (this.inviteRecordsAll.length === 0 || this.inviteRecords.length === 0 || this.inviteRecordsAll.length === res.total) {
          this.inviteLoading = false;
        }
      },
      (error) => {
        this.loading = false;
        this.appService.snackUp(error?.error?.message);
      },
      () => (this.loading = false)
    );
  }

  pageChange(pageInfo) {
    this.paginator = { ...pageInfo };
    this.getRewards();
  }
}
