import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CreateRuleRequest, RulesService, SystemRule } from '@flashvpn-io/web-core';

export type ConnectionMode = 'Directly' | 'Proxy' | 'Dropped';

interface EditableRule extends SystemRule {
  domain: string;
  action: ConnectionMode;
  isPlatform?: boolean;
}

@Component({
  selector: 'app-add-rule-dialog',
  template: `
    <div class="dialog-container">
      <div class="dialog-header">
        <h2>{{ isEditing ? '编辑规则' : '新增规则' }}</h2>
        <button mat-icon-button (click)="onClose()">
          <img src="assets/images/x-circle-contained.svg" alt="Close" class="close-icon">
        </button>
      </div>

      <form [formGroup]="ruleForm" (ngSubmit)="onSubmit()">
        <div class="form-field">
          <label>域名</label>
          <div class="custom-input-field">
            <input type="text" formControlName="domain" placeholder="输入域名">
            <button *ngIf="ruleForm.get('domain')?.value" type="button" class="clear-button" (click)="clearDomain()">
              X
            </button>
          </div>
        </div>

        <div class="connection-mode">
          <label>选择连接模式</label>
          <div class="mode-buttons">
            <button 
              type="button"
              [class.active]="selectedMode === 'Directly'"
              (click)="selectMode('Directly')">
              Directly
              <mat-icon *ngIf="selectedMode === 'Directly'" class="check-icon directly-icon">check</mat-icon>
            </button>
            <button 
              type="button"
              [class.active]="selectedMode === 'Proxy'"
              (click)="selectMode('Proxy')">
              Proxy
              <mat-icon *ngIf="selectedMode === 'Proxy'" class="check-icon proxy-icon">check</mat-icon>
            </button>
            <button 
              type="button"
              [class.active]="selectedMode === 'Dropped'"
              (click)="selectMode('Dropped')">
              Dropped
              <mat-icon *ngIf="selectedMode === 'Dropped'" class="check-icon droped-icon">check</mat-icon>
            </button>
          </div>
        </div>

        <div class="dialog-actions">
          <button mat-raised-button color="primary" type="submit" [disabled]="!ruleForm.valid || isSubmitting">
            {{ isSubmitting ? (isEditing ? '更新中...' : '创建中...') : (isEditing ? '确认更新' : '确认新增') }}
          </button>
        </div>
      </form>
    </div>
  `,
  styles: [`
    .dialog-container {
      padding: 32px 24px 48px;
      width: 100%;
      background: #FFFFFF;
      border-radius: 16px 16px 0px 0px;
    }

    .dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32px;
    }

    h2 {
      font-family: 'PingFang SC', sans-serif;
      font-weight: 500;
      font-size: 20px;
      line-height: 1em;
      margin: 0;
      color: #000000;
    }

    ::ng-deep .mat-dialog-container {
      padding: 0;
    }

    ::ng-deep .mat-icon-button {
      width: 24px;
      height: 24px;
      line-height: 24px;
    }

    .close-icon {
      width: 24px;
      height: 24px;
    }

    .form-field {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 24px;
    }

    .custom-input-field {
      display: flex;
      align-items: center;
      padding: 16px 12px;
      background: #F6F6F6;
      border-radius: 2px;
      height: 56px;
      width: 100%;
    }

    .custom-input-field input {
      flex: 1;
      border: none;
      background: transparent;
      font-family: 'PingFang SC', sans-serif;
      font-size: 14px;
      line-height: 1em;
      color: #000000;
      height: 24px;
      padding: 0;
      outline: none;
    }

    .custom-input-field input::placeholder {
      color: #999999;
      font-family: 'PingFang SC', sans-serif;
      font-weight: 400;
      font-size: 14px;
      line-height: 1em;
    }

    .clear-button {
      background: none;
      border: none;
      padding: 0;
      margin-left: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: bold;
      color: #999;
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .clear-button:hover {
      color: #666;
    }

    label {
      font-family: 'PingFang SC', sans-serif;
      font-weight: 600;
      font-size: 14px;
      line-height: 1.4em;
      color: #000000;
    }

    .connection-mode {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-bottom: 32px;
    }

    .mode-buttons {
      display: flex;
      gap: 12px;
    }

    .mode-buttons button {
      padding: 4px 8px;
      border: none;
      border-radius: 4px;
      font-family: 'PingFang SC', sans-serif;
      font-weight: 600;
      font-size: 12px;
      line-height: 1em;
      cursor: pointer;
      background: transparent;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .mode-buttons button:nth-child(1) {
      background: rgba(9, 224, 160, 0.1);
      color: #09E0A0;
    }

    .mode-buttons button:nth-child(2) {
      background: rgba(255, 192, 57, 0.1);
      color: #FFC039;
    }

    .mode-buttons button:nth-child(3) {
      background: rgba(255, 94, 94, 0.1);
      color: #FF5E5E;
    }

    .check-icon {
      width: 14px;
      height: 14px;
      font-size: 14px;
      line-height: 14px;
    }

    .directly-icon {
      color: #09E0A0;
    }

    .proxy-icon {
      color: #FFC039;
    }

    .droped-icon {
      color: #FF5E5E;
    }

    .dialog-actions {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    button[type="submit"] {
      width: 100%;
      height: 48px;
      background: #FF5E5E;
      color: white;
      font-family: 'PingFang SC', sans-serif;
      font-weight: 500;
      font-size: 20px;
      line-height: 1.4em;
      border: none;
      border-radius: 2px;
      cursor: pointer;
      padding: 10px 24px;
    }

    button[type="submit"]:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  `]
})
export class AddRuleDialogComponent {
  ruleForm: FormGroup;
  selectedMode: ConnectionMode = 'Directly';
  isSubmitting = false;
  isEditing = false;

  constructor(
    private dialogRef: MatDialogRef<AddRuleDialogComponent>,
    private fb: FormBuilder,
    private rulesService: RulesService,
    private snackBar: MatSnackBar,
    @Inject(MAT_DIALOG_DATA) public data?: EditableRule
  ) {
    this.isEditing = !!data;
    this.ruleForm = this.fb.group({
      domain: [data?.domain || '', [Validators.required]],
      action: [data?.action || 'Directly', [Validators.required]]
    });
    if (data) {
      this.selectedMode = data.action;
    }
  }

  selectMode(mode: ConnectionMode) {
    this.selectedMode = mode;
  }

  onClose() {
    this.dialogRef.close();
  }

  onSubmit() {
    if (this.ruleForm.valid) {
      this.isSubmitting = true;
      const request: CreateRuleRequest = {
        domain: this.ruleForm.value.domain,
        action: this.selectedMode
      };

      const operation = this.isEditing
        ? this.rulesService.updateRule(this.data!.id, request)
        : this.rulesService.createRule(request);

      operation.subscribe({
        next: () => {
          this.dialogRef.close({ saved: true });
        },
        error: (error) => {
          console.error('Failed to save rule:', error);
          this.snackBar.open('保存规则失败', '', {
            duration: 3000,
            horizontalPosition: 'center',
            verticalPosition: 'top',
          });
          this.isSubmitting = false;
        }
      });
    }
  }

  clearDomain() {
    this.ruleForm.get('domain')?.setValue('');
  }
} 