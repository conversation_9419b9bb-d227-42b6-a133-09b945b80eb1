import { Component, Input, Output, EventEmitter, AfterViewInit } from "@angular/core";
import { PaymentMethodService } from "@flashvpn-io/web-core";
import { MatBottomSheet } from "@angular/material/bottom-sheet";
import { MobilePaymethodComponent } from "./mobile-paymethod.component";
import { BehaviorSubject } from "rxjs/internal/BehaviorSubject";
import { PayMethod, PayType } from "@flashvpn-io/web-core";
import { filter, map } from "rxjs/operators";
import { DynamicDialogService } from "@flashvpn-io/web-core";
@Component({
  selector: "app-payment-methods",
  template: `
    <div>
      <div [class]="showTitle ? 'padding-methods' : 'methods'">
        <div class="methods-title" *ngIf="showTitle">支付方式</div>
        <mat-radio-group
          aria-labelledby="methods-radio-group-label"
          class="methods-radio-group"
          [value]="paymentMethodService.currentMethod$.value"
          (change)="setCurrentMethod($event)">
          <mat-radio-button
            class="methods-radio-button"
            (change)="this.paymentMethodService.methodOnChange($event)"
            *ngFor="let payMethod of this.filteredPayMethods$ | async"
            [value]="payMethod">
            <div class="methods-radio-option">
              <img [src]="payMethod.imgUrl" alt="" />
              <span style="margin-left: 10px">{{ payMethod.payName }}</span>
            </div>
          </mat-radio-button>
        </mat-radio-group>
      </div>
      <div class="mobile-methods">
        <div class="methods-title">选择支付方式:</div>
        <div class="select-pay-method" (click)="openSelectPayMethod()">
          <div class="select-pay-method-box">
            <div class="select-pay-method-title" i18n>支付方式</div>
            <div class="select-pay-method-info">
              <img [src]="paymentMethodService.currentMethod$.value?.imgUrl" alt="" />
              <span style="margin-left: 3px">{{ paymentMethodService.currentMethod$.value?.payName }}</span>
            </div>
          </div>
          <div class="select-pay-method-arrow">
            <svg width="9" height="13" viewBox="0 0 9 13" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 1L7 6.5L1 12" stroke="black" stroke-width="2" />
            </svg>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .padding-methods {
        padding: 30px 0px 0px;
      }

      .methods {
        padding: 0px;
      }

      .methods-title {
        margin-top: 10px;
        font-size: 16px;
        font-weight: 600;
        line-height: 16px;
        text-align: left;
        margin-bottom: 10px;
      }

      .methods-radio-group {
        display: flex;
        flex-direction: row;
        margin: 20px 20px 20px 0;
        align-items: center;
      }

      .methods-radio-button {
        margin: 10px;
      }

      .methods-radio-option {
        display: flex;
        align-items: center;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #000000;
      }

      .mobile-methods {
        display: none;
      }

      .select-pay-method {
        display: flex;
        align-items: center;
        padding: 8px 14px;
        gap: 8px;
        height: 67px;
        background: #ffffff;
        position: relative;
        margin-top: 20px;
        width: auto;
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
      }

      .select-pay-method-box {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }

      .select-pay-method-title {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 17px;
        color: #808080;
      }

      .select-pay-method-info {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 17px;
        color: #000000;
        margin-top: 10px;
        display: flex;
        align-items: center;
      }

      .select-pay-method-arrow {
        position: absolute;
        right: 20px;
      }

      .mobile-methods {
          display: none;
      }

      @media screen and (max-width: 599px) {
        .padding-methods, .methods {
          display: none;
        } 
        .mobile-methods {
          display: block;
        }
        .methods {
          display: none;
        }
        .methods-title {
          font-style: normal;
          font-weight: 400;
          font-size: 12px;
          line-height: 17px;
          color: #999999;
        }
      }
    `,
  ],
})
export class PaymentMethodsComponent implements AfterViewInit {
  @Input() excludePayType: PayType[] = [];

  @Input() showTitle: boolean = true;

  filteredPayMethods$: BehaviorSubject<PayMethod[]> = new BehaviorSubject<PayMethod[]>([]);

  public cardErrorMsg: string = "";

  constructor(public paymentMethodService: PaymentMethodService, private customOverlayService: DynamicDialogService) { }
  ngAfterViewInit(): void {
    this.paymentMethodService.paymentMethods$
      .pipe(
        filter((payMethods) => payMethods !== null && payMethods.length > 0),
        map((payMethods) => payMethods.filter((method) => !this.excludePayType.includes(method.payType)))
      )
      .subscribe((filteredMethods) => {
        this.filteredPayMethods$.next(filteredMethods as PayMethod[]);
      });
  }

  setCurrentMethod($event: any) {
    this.paymentMethodService.currentMethod$.next($event.value);
  }

  openSelectPayMethod = () => {
    const mobilePayMethodComponent = this.customOverlayService.open(MobilePaymethodComponent);
    mobilePayMethodComponent.excludePayType = this.excludePayType;
    mobilePayMethodComponent.payMethodSelected.subscribe((payMethod) => {
      this.customOverlayService.close();
    });
  };
  protected readonly PayType = PayType;
}
