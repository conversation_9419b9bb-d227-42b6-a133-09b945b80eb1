import { Component, Input, OnInit } from "@angular/core";
import { IVoucher, VoucherType } from "./voucher.component";
import { BillingCycles, defaultBillingCycles, Product, ServiceService, UsersService } from "@flashvpn-io/web-core";
import { BehaviorSubject, combineLatest } from "rxjs";
import { filter } from "rxjs/operators";

@Component({
  selector: "app-buy-products",
  template: `
    <div class="buy-products">
      <app-digital-title [num]="num" [title]="title"></app-digital-title>
      <div class="buy-products-list">
        <div
          [class]="(currentProduct$ | async)?.id === product?.id ? 'buy-products-item active' : 'buy-products-item'"
          *ngFor="let product of serviceService.products$ | async"
          (click)="selectProduct(product)">
          <div class="select"><img src="assets/images/select.svg" alt="" /></div>
          <div class="product-head">
            <div class="product-label-box">
              <div class="product-label">{{ product?.name }}</div>
              <div class="product-label">
                {{ productFlow[product.id] }}
                <span class="product-text">/ 每月重置</span>
              </div>
            </div>
            <img src="assets/images/package.svg" alt="" />
          </div>
          <div class="product-body" *ngIf="!!product?.description">
            <div class="product-body-item" *ngFor="let descriptionItem of product?.description.split('\\n')">
              <img src="assets/images/tick.svg" alt="" />
              <div class="product-text">{{ descriptionItem }}</div>
            </div>
          </div>
          <div class="product-foot">
            <div class="product-amount" *ngIf="currentBillingCycle">
              {{ product?.pricing?.HKD[currentBillingCycle?.id] }}/{{ currentBillingCycle?.unit }}
            </div>
            <div class="discount-box">
              <div class="product-text line-through">{{ product?.getMonthlyAmount(currentBillingCycle?.id) }}港币/月</div>
              <div class="discount" *ngIf="currentBillingCycle">节省 {{ product?.getSaveAmount(currentBillingCycle?.id) }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .buy-products {
        width: 100%;
      }
      .buy-products-list {
        width: 100%;
        display: grid;
        flex-direction: row;
        gap: 40px;
        margin-top: 20px;
        grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
      }
      .buy-products-item {
        display: grid;
        flex-direction: column;
        grid-template-rows: 1fr 1.5fr 1fr;
        box-shadow: 0px 2px 10px 0px rgba(255, 94, 94, 0.1);
        padding: 30px;
        border-radius: 5px;
        cursor: pointer;
        position: relative;
        border: 1px solid transparent;
        transition: border-color 0.3s ease-in-out;
      }
      .buy-products-item::after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-width: 0 44px 44px 0;
        border-style: solid;
        border-color: transparent rgba(255, 94, 94, 1) transparent transparent;
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
      }
      .select {
        position: absolute;
        top: 8px;
        right: 4px;
        z-index: 9;
      }
      .active {
        border-color: rgba(255, 94, 94, 1);
      }
      .active::after {
        opacity: 1;
      }
      .product-head {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 0.5px solid rgba(242, 242, 242, 1);
      }
      .product-label {
        font-size: 20px;
        font-weight: 500;
        line-height: 28px;
        text-align: left;
        color: rgba(0, 0, 0, 1);
      }
      .product-label-box {
        display: flex;
        flex-direction: column;
        gap: 3px;
        margin-bottom: 10px;
      }
      .product-text {
        font-size: 14px;
        font-weight: 400;
        line-height: 19.6px;
        text-align: left;
        color: rgba(153, 153, 153, 1);
      }
      .product-body {
        display: flex;
        flex-direction: column;
        justify-content: end;
        gap: 10px;
      }
      .product-body-item {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .product-foot {
        margin-top: 20px;
        display: flex;
        flex-direction: column;
        justify-content: end;
        gap: 8px;
      }
      .product-amount {
        font-size: 20px;
        font-weight: 600;
        line-height: 14px;
        text-align: left;
        color: rgba(255, 94, 94, 1);
      }
      .discount-box {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .discount {
        background: rgba(255, 94, 94, 0.1);
        padding: 2px 5px;
        font-size: 12px;
        font-weight: 400;
        line-height: 14px;
        text-align: left;
        color: rgba(255, 94, 94, 1);
      }
      @media screen and (max-width: 599px) {
        .buy-products-list {
          display: flex;
          flex-direction: column;
        }
      }
    `,
  ],
})
export class BuyProductsComponent implements OnInit {
  productFlow = {
    1: "50G",
    2: "100G",
    3: "200G",
  };

  @Input() public num: number = 2;

  @Input() public title: string = "选择套餐";

  @Input() public currentBillingCycle: BillingCycles;

  public currentProduct$: BehaviorSubject<Product> = new BehaviorSubject<Product>(null);

  constructor(public serviceService: ServiceService, private userService: UsersService) {}

  async ngOnInit() {
    if (await this.userService.isLoggedIn()) {
      combineLatest([
        this.serviceService.products$.pipe(filter((products) => !!products)),
        this.serviceService.currentService$.pipe(filter((service) => !!service)),
      ]).subscribe(([products, service]) => {
        let currentProduct = products.find((p) => p.id === service.productId);
        if (!currentProduct) {
          currentProduct = products[0];
        }
        this.currentProduct$.next(currentProduct);
      });
    } else {
      this.serviceService.products$.subscribe((products) => {
        if (products && products.length > 0) {
          this.currentProduct$.next(products[0]);
        }
      });
    }
  }

  async selectProduct(product: Product) {
    this.currentProduct$.next(product);
  }
}
