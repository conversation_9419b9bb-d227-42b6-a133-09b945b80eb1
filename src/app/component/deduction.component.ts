import { AfterViewInit, Component, Input, OnInit, ViewChild } from "@angular/core";
import { IVoucher, VoucherType } from "./voucher.component";
import { BillingCycles, DeductionType, Product, Reward, Service } from "@flashvpn-io/web-core";
import { BehaviorSubject } from "rxjs";
import { SelectCouponComponent } from "./select-coupon.component";
import { SelectBalanceComponent } from "./select-balance.component";
import { SelectCodeComponent } from "./select-code.component";
import { Breakpoints } from "@angular/cdk/layout";
import { ActivatedRoute } from "@angular/router";

@Component({
  selector: "app-deduction",
  template: `
    <div class="deduction">
      <div class="methods-title">优惠折扣</div>
      <div class="deduction-container">
        <mat-radio-group aria-labelledby="methods-radio-group-label" class="methods-radio-group" [(ngModel)]="currentDeduction">
          <div class="deduction-title" *ngIf="!excludeDeduction.includes(DeductionType.balance)">
            <mat-radio-button (change)="setCurrentDeduction($event.value)" [value]="deductionType.balance">使用余额抵扣</mat-radio-button>
          </div>
          <app-select-balance
            *ngIf="!excludeDeduction.includes(DeductionType.balance)"
            #selectBalanceComponent
            [enabled]="currentDeduction === deductionType.balance"
            [price]="getOrigialAmount()"
            (deductionAmountChange)="calculateDeductionAmount()"></app-select-balance>
          <div class="deduction-title" *ngIf="!excludeDeduction.includes(DeductionType.discount_code)">
            <mat-radio-button disabled="{{ loading }}" (change)="setCurrentDeduction($event.value)" [value]="deductionType.discount_code">
              使用折扣码
            </mat-radio-button>
          </div>
          <app-select-code
            *ngIf="!excludeDeduction.includes(DeductionType.discount_code)"
            #selectCodeComponent
            [enabled]="currentDeduction === deductionType.discount_code"
            [billingCycle]="currentBillingCycle?.id"
            [serviceId]="service?.id.toString()"
            (discountAmountChange)="calculateDeductionAmount()"></app-select-code>

          <div class="deduction-title" *ngIf="!excludeDeduction.includes(DeductionType.voucher)">
            <mat-radio-button disabled="{{ loading }}" (change)="setCurrentDeduction($event.value)" [value]="deductionType.voucher">
              使用优惠券折扣
            </mat-radio-button>
          </div>
          <app-select-coupon
            *ngIf="!excludeDeduction.includes(DeductionType.voucher)"
            #selectCouponComponent
            [enabled]="currentDeduction === deductionType.voucher"
            (couponSelected)="calculateDeductionAmount()"></app-select-coupon>
        </mat-radio-group>
      </div>
    </div>
  `,
  styles: [
    `
      .deduction {
        width: 100%;
      }
      .deduction-container {
        margin-left: 10px;
      }
      .methods-title {
        font-size: 16px;
        font-weight: 600;
        line-height: 16px;
        text-align: left;
        margin-bottom: 30px;
      }
      .deduction-title {
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #000000;
      }
      @media screen and (max-width: 599px) {
        .methods-title {
          font-style: normal;
          font-weight: 400;
          font-size: 12px;
          line-height: 17px;
          color: #999999;
          margin-top: 40px;
        }
        .deduction-container {
          margin-left: 0;
        }
      }
    `,
  ],
})
export class DeductionComponent implements AfterViewInit {
  @ViewChild("selectCouponComponent") selectCouponComponent: SelectCouponComponent;

  @ViewChild("selectBalanceComponent") selectBalanceComponent: SelectBalanceComponent;

  @ViewChild("selectCodeComponent") selectCodeComponent: SelectCodeComponent;

  @Input() service: Service;

  private _product: Product;

  private _currentBillingCycle: BillingCycles;

  @Input()
  set product(value: Product) {
    this._product = value;
    const price = value?.calculateSaved(this.currentBillingCycle?.id)?.price;
    this.origialAmount = Number(price);
    if (value) {
      this.calculateDeductions();
    }
  }

  get product(): Product {
    return this._product;
  }

  @Input()
  set currentBillingCycle(value: BillingCycles) {
    this._currentBillingCycle = value;
    this.origialAmount = null
  }

  get currentBillingCycle(): BillingCycles {
    return this._currentBillingCycle;
  }

  @Input() loading: boolean = false;

  @Input() excludeDeduction: DeductionType[] = [];

  @Input() origialAmount: number;

  protected readonly deductionType = DeductionType;

  public currentDeduction: DeductionType = DeductionType.voucher;

  public deductionAmount: number = 0;

  public deductionAmount$: BehaviorSubject<number> = new BehaviorSubject<number>(0);

  public amountAfterDeduction$: BehaviorSubject<number> = new BehaviorSubject<number>(0);

  public currentDeduction$: BehaviorSubject<DeductionType> = new BehaviorSubject<DeductionType>(DeductionType.voucher);

  constructor(private route: ActivatedRoute) { }

  ngAfterViewInit(): void {
    this.route.queryParamMap.subscribe((params) => {
      const currentCoupon = params.get("currentCoupon");
      if (currentCoupon) {
        this.selectCouponComponent.selectCoupon(JSON.parse(currentCoupon));
      }
    });
    this.selectCouponComponent.couponSelected.subscribe((coupon: Reward) => {
      this.calculateDeductionAmount();
    });
  }

  async setCurrentDeduction(currentDeduction: DeductionType) {
    this.currentDeduction = currentDeduction;
    this.currentDeduction$.next(currentDeduction);
    this.calculateDeductionAmount();
  }

  async calculateDeductionAmount() {
    if (this.selectCouponComponent) {
      switch (this.currentDeduction) {
        case DeductionType.voucher:
          this.deductionAmount = this.selectCouponComponent.currentCoupon?.award ?? 0;
          break;
        case DeductionType.balance:
          this.deductionAmount = this.selectBalanceComponent?.deductionAmount ?? 0;
          break;
        case DeductionType.discount_code:
          this.deductionAmount = this.selectCodeComponent?.deductionAmount ?? 0;
          break;
        default:
          this.deductionAmount = 0;
          break;
      }
      this.deductionAmount$.next(this.deductionAmount);
    }
  }

  getOrigialAmount(): number {
    if (!!this.origialAmount) {
      return this.origialAmount;
    }

    // we may change the product price in the future, but the recurring amount is the price of the product
    if (!!this.service && !this.service.isDue() && !!this.currentBillingCycle && this.service.billingcycle === this.currentBillingCycle.id) {
      return this.service.recurringamount;
    }

    if (!!this.currentBillingCycle && !!this.product) {
      const { price } = this.product.calculateSaved(this.currentBillingCycle.id);
      return Number(price);
    }

    return 0;
  }

  deDutiontoString(): string {
    switch (this.currentDeduction) {
      case DeductionType.balance:
        return "余额抵扣";
      case DeductionType.voucher:
        return "优惠券";
      case DeductionType.discount_code:
        return "折扣码抵扣";
      case DeductionType.none:
        return "无抵扣";
      default:
        return "未知抵扣类型";
    }
  }

  getAmountAfterDeduction(): number {
    return this.getOrigialAmount() - this.deductionAmount;
  }

  protected readonly DeductionType = DeductionType;

  public calculateDeductions() {
    this.calculateDeductionAmount();
  }
}
