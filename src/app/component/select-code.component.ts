import { Component, Input, Output, EventEmitter } from "@angular/core";
import { FormControl, Validators } from "@angular/forms";
import { APIManager } from "@flashvpn-io/web-core";
import { AppService } from "../app.service";

@Component({
  selector: "app-select-code",
  template: `
    <div class="deduction-bottom-box">
      <mat-form-field appearance="outline" class="example-full-width">
        <mat-label>输入抵扣码</mat-label>
        <input
          class="deduction-code-input"
          id="deduction-code"
          type="text"
          name="deduction-code"
          matInput
          i18n-placeholder
          [formControl]="enabled ? discountCodeControl : deductionDisableControl"
          [(ngModel)]="discountCode"
          required />
        <mat-error *ngIf="deductionDisableControl.invalid">{{ "请输入抵扣码" }}</mat-error>
      </mat-form-field>
      <button *ngIf="!dcCodeVerifying && !disableBtn" class="deduction-code-btn" mat-raised-button color="warn" (click)="verifyDcCode()">验证</button>
      <app-load-botton class="deduction-code-btn" *ngIf="dcCodeVerifying"></app-load-botton>
      <div *ngIf="dcCodePass != undefined" class="deduction-code-msg" [style]="{ color: dcCodePass ? '#3FE07F' : '#FF5E5E' }">
        <svg *ngIf="dcCodePass" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16ZM12 5.81018L10.9073 4.79998L6.16984 9.17959L4.29274 7.44428L3.20001 8.45447L6.16984 11.2L12 5.81018Z"
            fill="#3FE07F" />
        </svg>
        <svg *ngIf="!dcCodePass" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16ZM4 11.071L6.96985 8.1012L4 5.13135L5.13135 4L8.1012 6.96985L11.071 4L12.2025 5.13135L9.23254 8.1012L12.2024 11.071L11.071 12.2025L8.1012 9.23254L5.13135 12.2024L4 11.071Z"
            fill="#FF5E5E" />
        </svg>
        <span style="margin-left: 5px">{{ dcCodePass ? "抵扣码验证成功" : "抵扣码验证失败" }}</span>
      </div>
    </div>
  `,
  styles: [
    `
      .deduction-bottom-box {
        display: flex;
        align-items: center;
      }
      .deduction-code-btn {
        margin-left: 30px;
        margin-bottom: 1.34375em;
      }
      .discount-code-info p {
        margin: 5px 0;
        color: #666;
        font-size: 0.9em;
      }
      .example-full-width {
        width: 384px;
      }
      .deduction-code-msg {
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.34375em;
        margin-left: 30px;
      }
      @media screen and (max-width: 599px) {
        .example-full-width {
          width: 100%;
        }
        .deduction-bottom-box {
          margin-top: 6px;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          margin-bottom: 30px;
        }
        .deduction-code-btn {
          margin-left: 0;
          margin-bottom: 0;
          width: 50%;
        }
        .deduction-code-msg {
          margin-left: 0;
          margin-top: 20px;
          align-items: start;
        }
      }
    `,
  ],
})
export class SelectCodeComponent {
  @Input() enabled: boolean = false;
  @Input() disableBtn: boolean = false;
  @Input() maxLength: number = 20;

  @Input() set billingCycle(value: string) {
    if (!!value && this._billingCycle !== value) {
      this._billingCycle = value;
      this.verifyDcCode();
    }
  }
  get billingCycle(): string {
    return this._billingCycle;
  }
  private _billingCycle: string;
  @Input() serviceId: string;
  @Output() discountAmountChange = new EventEmitter<number>();

  public discountCode;
  public dcCodePass;
  public dcCodeVerifying = false;
  public deductionAmount = 0;

  discountCodeControl = new FormControl("", [Validators.required, Validators.maxLength(this.maxLength)]);
  deductionDisableControl = new FormControl({ value: undefined, disabled: true });

  constructor(private apiManager: APIManager, private appService: AppService) { }

  getErrorMessage() {
    if (this.discountCodeControl.hasError("required")) {
      return "请输入折扣码";
    }
    if (this.discountCodeControl.hasError("maxlength")) {
      return `折扣码不能超过 ${this.maxLength} 个字符`;
    }
    return "";
  }
  verifyDcCode() {
    this.deductionDisableControl?.markAsTouched();
    if (this.deductionDisableControl?.invalid || !this.discountCode) {
      return;
    }
    this.dcCodeVerifying = true;
    this.apiManager.verifyDcCode(this.discountCode, this.billingCycle, this.serviceId).subscribe(
      (res) => {
        if (res) {
          this.deductionAmount = res?.deductionAmount;
          this.dcCodePass = res?.pass;
          this.discountAmountChange.emit(this.deductionAmount);
        }
        this.dcCodeVerifying = false;
      },
      (res) => {
        this.appService.snackUp(res?.error?.message);
        this.dcCodeVerifying = false;
      }
    );
  }
}
