import { Component, OnInit } from "@angular/core";
import { InvoiceService, ServiceService } from "@flashvpn-io/web-core";
import { Invoice, InvoicePage } from "@flashvpn-io/web-core";
import * as moment from "moment";

@Component({
  selector: "invoice-history",
  template: `
    <div class="invoice-history">
      <div *ngIf="isLoading" class="loading-overlay">
        <div class="loading-spinner">
          <mat-spinner diameter="30"></mat-spinner>
        </div>
      </div>
      <table aria-label="Invoice history table">
        <thead>
          <tr>
            <th style="width: 40%">支付项</th>
            <th style="width: 25%">支付时间</th>
            <th style="width: 20%">支付方式</th>
            <th style="width: 15%">金额</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="!isLoading && invoices.length === 0">
            <td colspan="4" class="no-data">无数据</td>
          </tr>
          <tr *ngFor="let invoice of invoices">
            <td>{{ invoice.description }}</td>
            <td>{{ formatDate(invoice.datepaid) }}</td>
            <td>
              <div class="payment-method">
                <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M0.5 2.7432C0.5 1.50386 1.50297 0.5 2.74265 0.5L2.7428 0.500047H12.2579C13.4966 0.500047 14.5 1.5039 14.5 2.74324V10.0004C14.4324 9.99247 13.9088 9.91607 12.0404 9.29087C11.6787 9.16975 11.2373 9.00708 10.7445 8.82549C10.474 8.72582 10.188 8.62045 9.89131 8.51312C10.3945 7.63918 10.7953 6.64363 11.0593 5.56208H8.30045V4.56793H11.6804V4.01301H8.30045V2.35595H6.9205C6.67842 2.35595 6.67842 2.59485 6.67842 2.59485V4.01301H3.25948V4.56793H6.67842V5.56208H3.8556V6.11653H9.32999C9.12976 6.80679 8.86094 7.45509 8.5414 8.04375C6.76496 7.45752 4.86962 6.9822 3.67867 7.27481C2.91736 7.46241 2.42684 7.79753 2.13848 8.14828C0.815999 9.75741 1.76446 12.2018 4.55746 12.2018C6.20916 12.2018 7.79997 11.281 9.03332 9.7632C10.8614 10.6423 14.4581 12.1425 14.4998 12.1599V12.2572C14.4998 13.4956 13.4965 14.5 12.2578 14.5H2.74265C1.50297 14.5 0.5 13.4956 0.5 12.2572V2.7432ZM3.91628 8.10518C4.03761 8.09322 4.15804 8.0867 4.27792 8.08531L4.27791 8.0853C5.43682 8.07186 6.54025 8.53573 7.82131 9.16084C6.82763 10.4568 5.56271 11.2662 4.29673 11.2662C2.1189 11.2662 1.47488 9.55065 2.5509 8.61222C2.90989 8.2947 3.56655 8.14035 3.91628 8.10518Z"
                    fill="#00A1E9" />
                </svg>
                <span style="margin-left: 5px">支付宝</span>
              </div>
            </td>
            <td class="amount">{{ invoice.total }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div *ngIf="totalPages > 0" class="pagination">
      <mat-paginator
        [length]="total"
        [pageSize]="rowsPerPage"
        [pageIndex]="page - 1"
        (page)="onPageChange($event.pageIndex + 1)"
        aria-label="Select page"></mat-paginator>
    </div>
  `,
  styles: [
    `
      .invoice-history {
        position: relative;
        width: 100%;
      }

      table {
        width: 100%;
        border-collapse: collapse;
      }

      th,
      td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #e0e0e0;
      }

      th {
        background-color: #f5f5f5;
        font-weight: bold;
      }

      .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }

      .loading-spinner {
        position: relative;
      }

      .no-data {
        text-align: center;
        padding: 20px;
      }

      .payment-method {
        display: flex;
        align-items: center;
      }

      .amount {
        font-weight: bold;
      }

      .pagination {
        display: flex;
        justify-content: center;
        margin-top: 20px;
      }
    `,
  ],
})
export class InvoiceHistoryComponent implements OnInit {
  invoices: Invoice[] = [];
  total: number = 0;
  page: number = 1;
  rowsPerPage: number = 5;
  isLoading: boolean = false;
  productList = []

  constructor(
    private invoiceService: InvoiceService,
    private serviceService: ServiceService
  ) { }

  ngOnInit(): void {
    this.serviceService.products$.subscribe((products) => {
      if (products && products.length > 0) {
        this.productList = products
      }
    });
    this.loadInvoices();
  }

  loadInvoices(): void {
    this.isLoading = true;
    this.invoiceService
      .refreshInvoiceInfo({
        limitnum: this.rowsPerPage,
        limitstart: (this.page - 1) * this.rowsPerPage,
        orderby: "datepaid",
        order: "descend",
        status: "Paid",
      })
      .subscribe(
        (data: InvoicePage) => {
          this.invoices = data.invoices;
          this.updateInvoiceDescription()
          this.total = data.total;
          this.isLoading = false;
        },
        (error) => {
          console.error("Error fetching invoices:", error);
          this.isLoading = false;
        }
      );
  }

  onPageChange(page: number): void {
    this.page = page;
    this.loadInvoices();
  }

  formatDate(date: string): string {
    return moment(new Date(date)).format("YYYY-MM-DD HH:mm:ss");
  }

  get totalPages(): number {
    return Math.ceil(this.total / this.rowsPerPage);
  }

  updateInvoiceDescription() {
    if (this.productList.length > 0 && this.invoices.length > 0) {
      this.invoices.forEach(invoice => {
        if (invoice.description.includes('Upgrade')) {
          const match = invoice.description.match(/Upgrade from (\d+) to (\d+)/);
          if (match) {
            const fromProductId = parseInt(match[1]);
            const toProductId = parseInt(match[2]);
            const fromProduct = this.productList.find(p => p.id === fromProductId);
            const toProduct = this.productList.find(p => p.id === toProductId);
            if (fromProduct && toProduct) {
              invoice.description = `套餐升级：${fromProduct.name} > ${toProduct.name}`;
            }
          }
        }
      })
    }
  }
}
