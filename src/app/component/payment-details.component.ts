import { AfterViewInit, Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { defaultBillingCycles, PaymentMethodService, Product, ServiceService } from "@flashvpn-io/web-core";
import { Router } from "@angular/router";
import { APIManager } from "@flashvpn-io/web-core";
import { BreakpointObserver, Breakpoints } from "@angular/cdk/layout";
import { Service } from "@flashvpn-io/web-core";
import { BillingCycles } from "@flashvpn-io/web-core";
import { DeductionType } from "@flashvpn-io/web-core";
import { IN_PAYMENT } from "../services/payment.service";
import { BuyType } from "@flashvpn-io/web-core";
import { AppService } from "../app.service";
import { combineLatest } from "rxjs";
import { filter } from "rxjs/operators";

@Component({
  selector: "app-payment-details",
  template: `
    <div id="to-pay-footer" class="footer no-mobile">
      <div class="to-pay-footer">
        <div class="content">
          <div class="left-box" [style]="{ width: buyType !== BuyType.TRIAL ? '65%' : '50%' }">
            <div class="left-content">
              <div class="footer-title">金额明细</div>
              <div class="content-item">
                <span class="content-item-title">{{ getContentTitle() }}</span>
                <span style="color: #505050">{{ currentBillingCycle?.description }}</span>
              </div>
              <div class="content-item">
                <span class="content-item-title">总价</span>
                <span style="color: #ff5e5e">{{ origialAmount }}港币</span>
              </div>
            </div>
            <div class="left-content" *ngIf="buyType !== BuyType.TRIAL">
              <div class="title">&nbsp;</div>
              <div class="content-item">
                <span class="content-item-title">共减</span>
                <span style="color: #ff5e5e">减{{ deductionAmount || 0 }}港币</span>
              </div>
              <div class="content-item">
                <span class="content-item-title">{{ deDutiontoString }}</span>
                <span style="color: #ff5e5e">减{{ deductionAmount || 0 }}港币</span>
              </div>
            </div>
          </div>
          <div class="right-box">
            <div class="payment-button">
              <div class="amount-info" *ngIf="buyType !== BuyType.TRIAL">
                <div>
                  <span>合计：</span>
                  <span class="amount-info-value">{{ amountAfterDeduction }}港币</span>
                </div>
                <div>
                  <span>已抵扣：</span>
                  <span class="amount-info-value">{{ deductionAmount || 0 }}港币</span>
                </div>
              </div>
              <button *ngIf="!submitted" [disabled]="loading" class="payment-btn" mat-raised-button color="warn" id="order" (click)="buy.emit()">
                支付{{ amountAfterDeduction }}港币
              </button>
              <app-load-botton *ngIf="submitted"></app-load-botton>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="showMobilePayDialog" [class]="showMobilePayInfo ? 'to-pay-mobile-info bounce-in-bottom mobile' : 'to-pay-mobile-info slide-out-bottom mobile'">
      <div class="mobile-title-box">
        <div class="footer-title">金额明细</div>
        <span class="material-icons" (click)="closeMobilePayInfo()" style="cursor: pointer;">close</span>
      </div>
      <div class="pay-info-box">
        <div class="pay-info-item">
          <div class="content-item">{{ getContentTitle() }}</div>
          <div class="content-item" style="color: #505050">{{ currentBillingCycle?.description }}</div>
        </div>
        <div class="pay-info-item" *ngIf="buyType !== BuyType.TRIAL">
          <div class="content-item">总价</div>
          <div class="content-item" style="color: #FF5E5E">{{ origialAmount }}港币</div>
        </div>
        <div class="pay-info-item" *ngIf="buyType !== BuyType.TRIAL">
          <div class="content-item-title-2">{{ deDutiontoString }}</div>
          <div class="content-item-value">减{{ deductionAmount }}港币</div>
        </div>
        <div class="pay-info-item">
          <div class="content-item">合计</div>
          <div class="content-item" style="color: #FF5E5E">{{ amountAfterDeduction }}港币</div>
        </div>
      </div>
    </div>

    <div *ngIf="showMobileBillingCycleDialog" [class]="showMobileBillingCycle ? 'to-pay-mobile-info bounce-in-bottom' : 'to-pay-mobile-info slide-out-bottom mobile'">
      <div class="mobile-title-box" style="position: sticky; top: 0; background-color: #ffffff; z-index: 1;">
        <div class="footer-title">更换服务</div>
        <span class="material-icons" (click)="closeMobilePayInfo()" style="cursor: pointer;">close</span>
      </div>
      <div class="mobile-cycle-box-container">

        <div class="mobile-cycle-box" [class]="currentBillingCycle.id === billingCycle.id ? 'activeClick' : 'noActive'"
          (click)="setBillingCycle.emit(billingCycle); closeMobilePayInfo()" *ngFor="let billingCycle of billingCycles">
          <img class="child" *ngIf="currentBillingCycle.id === billingCycle.id" alt=""
            src="assets/images/products/checked.png">
          <div class="frame-parent">
            <div class="wrapper">
              <div class="div1">{{ billingCycle.description }}</div>
            </div>
            <div class="parent">
              <div class="div2">{{ product?.pricing?.HKD[billingCycle.id] }}港币/{{ billingCycle.unit }}</div>
              <div class="frame-group">
                <div class="group">
                  <div class="div3">{{ product?.pricing?.HKD["monthly"] }}港币/月</div>
                  <div class="div4">{{ product?.getMonthlyAmount(billingCycle.id) }}港币/月</div>
                </div>
                <div class="rectangle-parent">
                  <div class="frame-child">
                  </div>
                  <div class="div5">节省{{ product?.getSaveAmount(billingCycle.id) }}%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="to-pay-mobile-botton mobile" [style]="{ 'border-top': showMobilePayInfo ? '0' : '1px solid rgba(0, 0, 0, 0.12)' }">
      <div class="payment-button">
        <div class="amount-info">
          <div>
            <span>合计：</span>
            <span class="amount-info-value">{{ amountAfterDeduction }}港币</span>
          </div>
          <div *ngIf="buyType !== BuyType.TRIAL">
            <span>已抵扣：</span>
            <span class="amount-info-value">{{ deductionAmount || 0 }}港币</span>
          </div>
          <div *ngIf="!showMobilePayInfo" class="mobile-amount-info" (click)="openMobilePayInfo()">
            <span style="margin-right: 5px">查看详情</span>
            <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 5L5 1L9 5" stroke="#FF5E5E" />
            </svg>
          </div>
        </div>
        <button *ngIf="!submitted" [disabled]="loading" class="payment-btn" mat-raised-button color="warn" id="mobile-order" (click)="buy.emit()">
          支付{{ amountAfterDeduction }}港币
        </button>
        <app-load-botton *ngIf="submitted"></app-load-botton>
      </div>
    </div>
    <div
      *ngIf="showMobilePayInfo || showMobileBillingCycle"
      id="Mask"
        style="overflow: hidden; z-index: 2; width: 100%; height: 1600px; position: fixed; top: 0px; left: 0px; background: rgb(0, 0, 0); opacity: 0.6;">
    </div>
  `,
  styles: [
    `
      .footer {
        position: fixed;
        bottom: 0;
        height: 260px;
        left: 0;
        right: 0;
        border-top: 1px solid rgba(0, 0, 0, 0.12);
        background-color: #ffffff;
        border-radius: 20px;
      }

      .to-pay-footer {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        padding: 20px;
        box-sizing: border-box;
        z-index: 1100;
        transform: translateZ(1px);
      }

      .content {
        width: 100%;
        height: 183px;
        display: flex;
      }

      .left-box {
        width: 65%;
        justify-content: space-around;
        display: flex;
        align-items: center;
      }

      .left-content {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }

      .right-box {
        width: 35%;
      }

      .footer-title {
        display: flex;
        align-items: center;
        font-size: 20px;
        font-weight: 500;
        line-height: 28px;
        letter-spacing: 0em;
        text-align: left;
        margin-bottom: 10px;
      }

      .content-item {
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        display: flex;
        align-items: center;
        text-align: right;
        color: #000;
      }

      .content-item-title {
        display: flex;
        align-items: center;
        width: 130px;
        color: #000;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }

      .payment-button {
        padding-top: 10px;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      .amount-info {
        margin: 20px 0;
        width: 300px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #a0aec0;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }

      .amount-info-value {
        color: #000;
        text-align: right;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 20px;
      }

      .payment-btn {
        width: 300px;
        height: 40px;
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
      }

      .to-pay-mobile-info {
        position: fixed;
        bottom: 150px;
        height: auto;
        left: 0;
        right: 0;
        border-top: 1px solid rgba(0, 0, 0, 0.12);
        background-color: #ffffff;
        border-radius: 20px 20px 0 0;
        z-index: 3;
        transform: translateZ(1px);
        width: 100%;
        display: flex;
        flex-direction: column;
        max-height: 60%;
        overflow: scroll;
      }

      .to-pay-mobile-botton {
        grid-area: footer;
        position: fixed;
        bottom: 0;
        height: 150px;
        left: 0;
        right: 0;
        border-top: 1px solid rgba(0, 0, 0, 0.12);
        background-color: #ffffff;
        margin: 0;
        z-index: 4;
        transform: translateZ(1px);
        -o-transform: translateZ(1px);
        -ms-transform: translateZ(1px);
        -moz-transform: translateZ(1px);
        -webkit-transform: translateZ(1px);
      }

      .mobile {
        overflow: visible;
        display: none;
      }

      .mobile-title-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
      }

      .pay-info-box {
        margin: 20px;
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .pay-info-item {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
      }

      .bounce-in-bottom {
        -webkit-animation: bounce-in-bottom 1.1s both;
        animation: bounce-in-bottom 1.1s both;
      }

      .mobile-cycle-box-container {
        align-items: center;
        display: flex;
        flex-direction: column;
      }

      .child {
          position: absolute;
          top: 0px;
          right: 0px;
          width: 44px;
          height: 44px;
      }
      .div1 {
          position: relative;
          line-height: 14px;
          font-weight: 600;
      }
      .wrapper {
          align-self: stretch;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;
          padding: 3px 0px;
      }
      .div2 {
          position: relative;
          line-height: 20px;
          font-weight: 600;
      }
      .div3 {
          position: relative;
          text-decoration: line-through;
          line-height: 14px;
      }
      .div4 {
          position: relative;
          line-height: 14px;
          color: #000;
      }
      .group {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;
          gap: 4px;
      }
      .frame-child {
          position: absolute;
          top: 0px;
          left: 0px;
          border-radius: 2px;
          background-color: rgba(255, 94, 94, 0.1);
          width: 64px;
          height: 18px;
      }
      .div5 {
          position: absolute;
          top: 2px;
          left: 7px;
          line-height: 14px;
      }
      .rectangle-parent {
          width: 64px;
          position: relative;
          height: 18px;
          font-size: 12px;
          color: #ff5e5e;
      }
      .frame-group {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;
          gap: 12px;
          font-size: 14px;
          color: #808080;
      }
      .parent {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: flex-start;
          gap: 8px;
          font-size: 20px;
          color: #ff5e5e;
      }
      .frame-parent {
          position: absolute;
          top: 32px;
          left: 16px;
          width: 295px;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: flex-start;
          gap: 12px;
      }
      .activeClick {
        border: 1px solid #ff5e5e;
      }
      .non-active {
        border: unset;
      }
      .mobile-cycle-box {
        margin-bottom: 17px;
          width: 95%;
          position: relative;
          box-shadow: 0px 2px 10px rgba(255, 94, 94, 0.1);
          border-radius: 2px;
          background-color: #fff;
          box-sizing: border-box;
          height: 142px;
          text-align: left;
          font-size: 14px;
          color: #000;
          font-family: 'PingFang SC';
          align-items: center;
      }




      @-webkit-keyframes bounce-in-bottom {
        0% {
          -webkit-transform: translateY(500px);
          transform: translateY(500px);
          -webkit-animation-timing-function: ease-in;
          animation-timing-function: ease-in;
          opacity: 0;
        }
        38% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
          opacity: 1;
        }
        55% {
          -webkit-transform: translateY(65px);
          transform: translateY(65px);
          -webkit-animation-timing-function: ease-in;
          animation-timing-function: ease-in;
        }
        72% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
        }
        81% {
          -webkit-transform: translateY(28px);
          transform: translateY(28px);
          -webkit-animation-timing-function: ease-in;
          animation-timing-function: ease-in;
        }
        90% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
        }
        95% {
          -webkit-transform: translateY(8px);
          transform: translateY(8px);
          -webkit-animation-timing-function: ease-in;
          animation-timing-function: ease-in;
        }
        100% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
        }
      }
      @keyframes bounce-in-bottom {
        0% {
          -webkit-transform: translateY(500px);
          transform: translateY(500px);
          -webkit-animation-timing-function: ease-in;
          animation-timing-function: ease-in;
          opacity: 0;
        }
        38% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
          opacity: 1;
        }
        55% {
          -webkit-transform: translateY(65px);
          transform: translateY(65px);
          -webkit-animation-timing-function: ease-in;
          animation-timing-function: ease-in;
        }
        72% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
        }
        81% {
          -webkit-transform: translateY(28px);
          transform: translateY(28px);
          -webkit-animation-timing-function: ease-in;
          animation-timing-function: ease-in;
        }
        90% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
        }
        95% {
          -webkit-transform: translateY(8px);
          transform: translateY(8px);
          -webkit-animation-timing-function: ease-in;
          animation-timing-function: ease-in;
        }
        100% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
        }
      }

      .slide-out-bottom {
        -webkit-animation: slide-out-bottom 0.5s cubic-bezier(0.55, 0.085, 0.68, 0.53) both;
        animation: slide-out-bottom 0.5s cubic-bezier(0.55, 0.085, 0.68, 0.53) both;
      }

      @-webkit-keyframes slide-out-bottom {
        0% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          opacity: 1;
        }
        100% {
          -webkit-transform: translateY(1000px);
          transform: translateY(1000px);
          opacity: 0;
        }
      }
      @keyframes slide-out-bottom {
        0% {
          -webkit-transform: translateY(0);
          transform: translateY(0);
          opacity: 1;
        }
        100% {
          -webkit-transform: translateY(1000px);
          transform: translateY(1000px);
          opacity: 0;
        }
      }

      @media screen and (max-width: 599px) {
        .footer-title {
          margin-bottom: 0;
        }
        .content-item-title-2 {
          width: auto;
        }
        .payment-btn {
          width: 90%;
        }
        .mobile {
          display: flex;
        }
        .no-mobile {
          display: none;
        }
        .amount-info {
          width: 90%;
        }
        .payment-button {
          padding-top: 0;
        }
      }
    `,
  ],
})
export class PaymentDetailsComponent implements AfterViewInit, OnInit {
  @Input() buyType: BuyType;
  @Input() currentBillingCycle: BillingCycles;
  @Input() deductionAmount?: number;
  @Input() origialAmount: number;
  @Input() deDutiontoString: string;
  @Input() amountAfterDeduction: number;
  @Input() loading: boolean;
  @Input() submitted: boolean = false;
  @Input() set openBillingCyclesChange(value: boolean) {
    this.showMobileBillingCycle = value;
    if (this.showMobileBillingCycle) {
      this.showMobileBillingCycleDialog = this.showMobileBillingCycle || true;
    }
  }

  @Output() buy = new EventEmitter();

  @Output() setBillingCycle = new EventEmitter();

  protected readonly BuyType = BuyType;

  public showMobilePayInfo = false;

  public showMobilePayDialog = false;

  public showMobileBillingCycle = false;

  public showMobileBillingCycleDialog = false;

  public isLarge = true;

  public billingCycles: BillingCycles[];

  public product: Product

  public service: Service;

  constructor(
    public appService: AppService,
    public apiManager: APIManager,
    private paymentMethodService: PaymentMethodService,
    private breakpointObserver: BreakpointObserver,
    public router: Router,
    private serviceService: ServiceService,
  ) { }


  async ngOnInit() {
    combineLatest([
      this.serviceService.products$.pipe(filter((products) => !!products)),
      this.serviceService.currentService$.pipe(filter((service) => !!service)),
    ]).subscribe(([products, service]) => {
      this.product = products.find((p) => p.id === service.productId);
      this.service = service;
      this.billingCycles = defaultBillingCycles;
      this.currentBillingCycle = this.billingCycles.find((bc) => bc.id === service.billingcycle);
    });
  }

  ngAfterViewInit(): void {
    this.breakpointObserver.observe([Breakpoints.Medium, Breakpoints.Large, Breakpoints.XLarge]).subscribe((x) => {
      this.isLarge = x.matches;
      const footer = document.getElementById("to-pay-footer") as HTMLElement;
      if (footer) {
        if (this.isLarge) {
          footer.style.left = "258px";
        } else {
          footer.style.left = "0";
        }
      }
    });
  }

  openMobilePayInfo = () => {
    this.showMobilePayInfo = true;
    this.showMobilePayDialog = this.showMobilePayInfo || true;
  };

  closeMobilePayInfo = () => {
    this.showMobilePayInfo = false;
    this.showMobileBillingCycle = false;
  };

  getContentTitle() {
    switch (this.buyType) {
      case BuyType.FLOWDATA:
        return "流量包";
      case BuyType.SERVICE:
        return "服务周期";
      case BuyType.TRIAL:
        return "支付项目";
    }
  }
}
