import { Component, On<PERSON><PERSON>roy, OnInit, Output } from "@angular/core";
import { IVoucher } from "./voucher.component";
import { RewardService, WalletsService, WelfareService } from "@flashvpn-io/web-core";

@Component({
  selector: "app-wait-collection",
  template: `
    <div class="couponsBox" *ngIf="!!waitCollectionVouchers && waitCollectionVouchers.length>0">
      <img class="serviceCoupons" src="assets/images/wait-collection-voucher.png" alt="wait-collection-voucher" />
      <div class="head-title-1">Flash {{waitCollectionVouchers?.[0]?.title}}</div>
      <div class="head-title-2">{{waitCollectionVouchers?.[0]?.note}}</div>
    </div>
    <div class="couponsBox" *ngIf="!waitCollectionVouchers || waitCollectionVouchers.length<1">
      <img class="serviceCoupons" src="assets/images/no-collection-voucher.png" alt="wait-collection-voucher" />
      <div class="head-title-1">暂无可用优惠券</div>
      <div class="head-title-2" style="text-decoration: underline;">前往福利中心></div>
    </div>
  `,
  styles: [
    `
      .couponsBox {
        height: 100%;
        position: relative;
        box-shadow: 0px 0px 10px 0px rgba(255, 94, 94, 0.1);
        background: linear-gradient(45deg, rgba(255, 239, 239, 1), rgba(255, 253, 239, 0.8));
        cursor: pointer;
      }
      .serviceCoupons {
        width: 100%;
      }
      .head-title-1 {
        font-size: 28px;
        font-weight: 600;
        color: #ff5e5e;
        position: absolute;
        bottom: 110px;
        left: 65px;
      }
      .head-title-2 {
        font-size: 18px;
        font-weight: 600;
        color: #ff5e5e;
        position: absolute;
        bottom: 90px;
        left: 65px;
      }
      @media screen and (max-width: 599px) {
        .head-title-1 {
          bottom: 82px;
          left: 62px;
        }
        .head-title-2 {
          bottom: 62px;
          left: 62px;
        }
      }
    `,
  ],
})
export class WaitCollectionComponent implements OnInit {
  public waitCollectionVouchers: IVoucher[] = [];

  constructor(private welfareService: WelfareService) { }

  ngOnInit() {
    this.getClientWelfareRule();
    this.getMileageWelfareRule();
    this.getCommunityRule();
  }

  getClientWelfareRule = () => {
    this.welfareService.clientWelfareRule$.subscribe((rules) => {
      this.waitCollectionVouchers = this.waitCollectionVouchers.concat(...rules.filter((item) => !item.receive && item.achieve));
    });
  };

  getMileageWelfareRule = () => {
    this.welfareService.mileageWelfareRule$.subscribe((rules) => {
      this.waitCollectionVouchers = this.waitCollectionVouchers.concat(...rules.filter((item) => !item.receive && item.achieve));
    });
  };

  getCommunityRule = () => {
    this.welfareService.communityRule$.subscribe((rules) => {
      this.waitCollectionVouchers = this.waitCollectionVouchers.concat(...rules.filter((item) => !item.receive && item.achieve));
    });
  };
}
