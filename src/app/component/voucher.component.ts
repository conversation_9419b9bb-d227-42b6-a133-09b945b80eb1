import { Component, Input, OnInit } from "@angular/core";
import { AppService } from "../app.service";
import { Router } from "@angular/router";
import { APIManager } from "@flashvpn-io/web-core";

export interface IVoucher {
  id: string;
  title: string;
  award: number;
  achieve: boolean;
  receive: boolean;
  type: VoucherType;
  note?: string;
  waitDays?: number;
  expireDays?: number;
  days?: number;
}

export enum VoucherType {
  CLIENT = "CLIENT",
  MILEAGE = "MILEAGE",
  BIND_TG = "BIND_TG",
  ENABLE_FCM = "ENABLE_FCM",
  QUESTIONNAIRE = "QUESTIONNAIRE",
}

@Component({
  selector: "app-voucher",
  template: `
    <div class="voucher-list">
      <div
        class="voucher-card"
        [style]="{ 'box-shadow': voucher.achieve ? '0px 0px 10px rgba(255, 94, 94, 0.1)' : '0px 0px 10px rgba(0, 0, 0, 0.1)' }"
        *ngFor="let voucher of vouchers">
        <div [class]="voucher.achieve ? 'voucher-card-left' : 'voucher-card-left-black'">
          <svg *ngIf="voucher.achieve && !voucher.receive" width="34" height="36" viewBox="0 0 34 36" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="34" height="36" rx="2" fill="#FF5E5E" />
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M0 5.88818V34C0 35.1046 0.89543 36 2 36H32C33.1046 36 34 35.1046 34 34V5.38513C29.0598 8.59819 23.064 10.4806 16.6 10.4806C10.4811 10.4806 4.78159 8.79378 0 5.88818Z"
              fill="#D24B4B" />
          </svg>
          <svg *ngIf="!voucher.achieve" width="34" height="36" viewBox="0 0 34 36" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="34" height="36" rx="2" fill="#999999" />
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M0 5.88818V34C0 35.1046 0.89543 36 2 36H32C33.1046 36 34 35.1046 34 34V5.38513C29.0598 8.59819 23.064 10.4806 16.6 10.4806C10.4811 10.4806 4.78159 8.79378 0 5.88818Z"
              fill="#808080" />
          </svg>
          <svg *ngIf="voucher.achieve && voucher.receive" width="34" height="40" viewBox="0 0 34 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M34 5.00984V38C34 39.1046 33.1046 40 32 40H2C0.89543 40 0 39.1046 0 38V5.01003C4.86236 1.84551 10.7096 0 17.0001 0C23.2906 0 29.1377 1.84544 34 5.00984Z"
              fill="#B42E2E" />
            <rect x="5" y="8" width="24" height="22" rx="4" fill="#FFD439" />
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M34 15C29.0598 18.2131 23.064 20.0955 16.6 20.0955C10.4811 20.0955 4.78159 18.4086 0 15.503V37.6149C0 38.7194 0.89543 39.6149 2 39.6149H32C33.1046 39.6149 34 38.7194 34 37.6149V15Z"
              fill="#D24B4B" />
          </svg>
          <div *ngIf="voucher.receive" class="award-mask" style="color: #D24B4B"></div>
          <div *ngIf="voucher.receive" class="award" style="color: #FFFFFF;margin-top: -7px">\${{ voucher.award }}</div>
          <div *ngIf="!voucher.receive" class="award" [style]="{ color: voucher.achieve && !voucher.receive ? '#FFD439' : '#FFFFFF' }">
            \${{ voucher.award }}
          </div>
          <div id="bottomCover"></div>
          <div id="topCover"></div>
        </div>
        <div class="voucher-card-right">
          <div class="voucher-card-text">
            <span>{{ voucher.title }}</span>
            <div *ngIf="voucher.type === voucherType.CLIENT" class="voucher-card-note">
              <span *ngIf="!voucher.achieve">
                客户端未安装，
                <span style="color: #FF5E5E;cursor: pointer;" (click)="goToApps()">立即下载</span>
              </span>
              <span *ngIf="voucher.achieve && !voucher.receive">客户端已安装，请领取红包</span>
              <span *ngIf="voucher.achieve && voucher.receive">红包已领取</span>
            </div>
            <div *ngIf="voucher.type === voucherType.MILEAGE" class="voucher-card-note">
              <span>{{ voucher.note }}</span>
            </div>
          </div>
          <div class="receive-botton">
            <button
              *ngIf="!voucher.receive"
              mat-raised-button
              class="voucher-button"
              color="warn"
              (click)="trigger(voucher)"
              [disabled]="!voucher.achieve">
              {{ !voucher.achieve && voucher.expireDays > 0 ? "失效" : "领取" }}
            </button>
            <div *ngIf="!voucher.receive && voucher.waitDays" class="waitDays">{{ voucher.waitDays }}天后可领取</div>
            <div class="received" *ngIf="voucher.receive">{{ "已领取" }}</div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .voucher-list {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        flex-direction: initial;
        gap: 24px;
      }
      .voucher-card {
        width: 343px;
        height: 84px;
        background: #ffffff;
        box-shadow: 0px 0px 10px rgba(255, 94, 94, 0.1);
        display: flex;
        align-items: center;
        border-radius: 8px;
      }
      .voucher-card-right {
        display: flex;
        justify-content: space-between;
        align-items: center;
        float: right;
        width: 100%;
      }
      .voucher-card-text {
        display: flex;
        flex-direction: column;
        margin-left: 10px;
        gap: 10px;
        width: 60%;
        color: #505050;
        font-size: 14px;
        font-weight: 500;
        line-height: 14px;
      }
      @media screen and (max-width: 599px) {
        .voucher-card {
          width: 100%;
        }
      }
      .voucher-card-left,
      .voucher-card-left-black {
        width: 90px;
        height: 84px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        border-right: 1px solid transparent;
        background: linear-gradient(white, white) padding-box, repeating-linear-gradient(-45deg, #999999 0, #ccc 0.1em, white 0, white 0.3em);
        border-radius: 8px;
      }

      .voucher-card-left::before,
      .voucher-card-left::after,
      .voucher-card-left-black::before,
      .voucher-card-left-black::after {
        content: "";
      }

      .voucher-card-left::before,
      #bottomCover {
        position: absolute;
        right: 0;
        bottom: -8px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #ffffff;
        z-index: 1;
        box-shadow: inset 0 0px 10px rgba(255, 94, 94, 0.1);
      }

      .voucher-card-left-black::before,
      #bottomCover {
        position: absolute;
        right: 0;
        bottom: -8px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #ffffff;
        z-index: 1;
        box-shadow: inset 0 0px 10px rgba(0, 0, 0, 0.1);
      }

      .voucher-card-left::after,
      #topCover {
        position: absolute;
        top: -8px;
        right: 0;
        bottom: 0;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #ffffff;
        z-index: 1;
        box-shadow: inset 0 0px 10px rgba(255, 94, 94, 0.1);
      }

      .voucher-card-left-black::after,
      #topCover {
        position: absolute;
        top: -8px;
        right: 0;
        bottom: 0;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #ffffff;
        z-index: 1;
        box-shadow: inset 0 0px 10px rgba(0, 0, 0, 0.1);
      }

      .voucher-card-left::after,
      #topCover {
        left: inherit;
        right: -8px;
        box-shadow: inset 0 0px 10px rgba(255, 94, 94, 0.1);
      }

      .voucher-card-left::before,
      #bottomCover {
        left: inherit;
        right: -8px;
        box-shadow: inset 0 0px 10px rgba(255, 94, 94, 0.1);
      }

      .voucher-card-left-black::after,
      #topCover {
        left: inherit;
        right: -8px;
        box-shadow: inset 0 0px 10px rgba(0, 0, 0, 0.1);
      }

      .voucher-card-left-black::before,
      #bottomCover {
        left: inherit;
        right: -8px;
        box-shadow: inset 0 0px 10px rgba(0, 0, 0, 0.1);
      }

      #topCover {
        right: -15px;
        top: -20px;
      }

      #bottomCover {
        right: -15px;
        bottom: -20px;
      }

      #bottomCover,
      #topCover {
        box-shadow: none;
        width: 24px;
        height: 24px;
        filter: blur(3px);
        z-index: 2;
      }

      .received {
        font-style: normal;
        font-weight: 600;
        font-size: 14px;
        line-height: 15px;
        color: #ff5e5e;
        padding: 0 16px;
      }
      .voucher-card-note {
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 14px;
        color: #505050;
      }
      .receive-botton {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 40%;
      }
      .waitDays {
        position: absolute;
        margin-bottom: -50px;
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 24px;
        color: #ff5e5e;
      }
      .voucher-button {
        line-height: 24px;
        padding: 0 8px;
      }
      .award {
        position: absolute;
        margin-top: 5px;
        font-style: normal;
        font-weight: 600;
        font-size: 14px;
        line-height: 14px;
      }
      .award-mask {
        position: absolute;
        width: 34px;
        height: 34px;
        background: radial-gradient(circle at 50% -23px, transparent 0, transparent 40px, #d24b4b 40px, #d24b4b);
        z-index: 9;
      }
    `,
  ],
})
export class VoucherComponent implements OnInit {
  @Input() public vouchers: IVoucher[] = [];
  @Input() public trigger: any;
  public voucherType = VoucherType;

  constructor(private appService: AppService, private router: Router, private apiManager: APIManager) {}

  async ngOnInit() {}

  goToApps() {
    this.router.navigate(["apps"]);
  }
}
