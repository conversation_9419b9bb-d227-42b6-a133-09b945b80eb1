import { Component, Input, OnInit } from "@angular/core";
import { IVoucher, VoucherType } from "./voucher.component";

@Component({
  selector: "app-digital-title",
  template: `
    <div class="digital-title">
      <div class="title">
        <div class="num">{{ num }}</div>
        <span>{{ title }}</span>
      </div>
    </div>
  `,
  styles: [
    `
      .digital-title {
        width: 100%;
      }
      .num {
        width: 22px;
        height: 22px;
        border-radius: 11px;
        background-color: rgba(255, 94, 94, 1);
        font-size: 16px;
        font-weight: 600;
        line-height: 14px;
        color: rgba(255, 255, 255, 1);
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 600;
        line-height: 14px;
        text-align: left;
        color: rgba(0, 0, 0, 1);
        gap: 5px;
      }
    `,
  ],
})
export class DigitalTitleComponent {
  @Input() num: number = 1;

  @Input() title: string;

  constructor() {}
}
