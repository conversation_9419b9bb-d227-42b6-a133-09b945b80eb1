import { Component, Input, OnInit } from "@angular/core";
import { IVoucher, VoucherType } from "./voucher.component";
import { BillingCycles, defaultBillingCycles, PayMethod, Product, ServiceService } from "@flashvpn-io/web-core";
import { BehaviorSubject } from "rxjs";

@Component({
  selector: "app-billing-cycles",
  template: `
    <div class="billing-cycles">
      <app-digital-title [num]="num" [title]="title"></app-digital-title>
      <div class="billing-cycles-list">
        <div
          [class]="(currentBillingCycle$ | async).id == billingCycle.id ? 'billing-cycles-item active' : 'billing-cycles-item'"
          *ngFor="let billingCycle of billingCycles"
          (click)="selectBillingCycle(billingCycle)">
          <span>{{ billingCycle.description }}</span>
          <span class="discount" *ngIf="currentProduct?.getSaveAmount(billingCycle.id) > 0">
            -{{ currentProduct?.getSaveAmount(billingCycle.id) }}%
          </span>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .billing-cycles {
        width: 100%;
      }
      .billing-cycles-list {
        width: 100%;
        display: grid;
        flex-direction: row;
        gap: 20px;
        margin-top: 20px;
        grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
      }
      .billing-cycles-item {
        font-size: 16px;
        font-weight: bold;
        padding: 15px 30px;
        box-shadow: 0px 2px 10px 0px rgba(255, 94, 94, 0.1);
        cursor: pointer;
        position: relative;
        border: 1px solid transparent;
        border-radius: 5px;
        transition: border-color 0.3s ease-in-out;
      }
      .active {
        border-color: rgba(255, 94, 94, 1);
      }
      .active::after {
        opacity: 1;
      }
      .discount {
        font-size: 16px;
        font-weight: bold;
        margin-left: 10px;
        color: rgba(255, 94, 94, 1);
      }
      @media screen and (max-width: 599px) {
        .billing-cycles-list {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
        }
        .billing-cycles-item {
          width: calc(50% - 55px);
          padding: 15px 20px;
        }
      }
    `,
  ],
})
export class BillingCyclesComponent {
  public billingCycles: BillingCycles[] = defaultBillingCycles;

  public currentBillingCycle$: BehaviorSubject<BillingCycles> = new BehaviorSubject<BillingCycles>(this.billingCycles[0]);

  @Input() public num: number = 1;

  @Input() public title: string = "选择支付周期";

  @Input() public currentProduct: Product;

  constructor() { }

  async selectBillingCycle(billingCycle: BillingCycles) {
    this.currentBillingCycle$.next(billingCycle);
  }
}
