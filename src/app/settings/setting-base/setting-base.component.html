<style>
  @media screen and (min-width: 599px){
    section {
      max-width: 250px;
    }
  }
  h3 {
    font-weight: bold;
  }
</style>
<div fixFill class="setting" fxLayout="column" fxLayoutAlign="space-between start">
  <div class="main" fxLayout="row" fxLayoutGap="50px" fxLayout.lt-md="column">
    <section>
      <h3 i18n>Account</h3>
      <div i18n>Manage your account settings</div>
      <mat-divider style="margin-top: 18px;"></mat-divider>
      <a id="settings-reset-password" class="highlighted" routerLink="/settings/reset-password" i18n>Reset Password</a>
    </section>

    <section *ngIf="appService.isExtension() || appService.isElectron()">
      <h3 i18n>General</h3>
      <div i18n>Manage general setting of app</div>
      <mat-divider style="margin-top: 18px;"></mat-divider>
      <h4  class="highlighted" (click)="checkUpdates()"><span i18n>Check Updates</span> (<span>{{version}}</span>)</h4>
      <mat-progress-bar *ngIf="checkingUpdates" mode="indeterminate"></mat-progress-bar>
    </section>
  </div>
  <div>
    <section style="margin-top: 32px;">
      <h3 i18n>About</h3>
      <p style="margin-top: 14px;" class="text" i18n>FlashVPN is published by FlashVPN Network. We aim to provide the best networking facility tools to help customers to reach their desired content.</p>
    </section>
  </div>
</div>
