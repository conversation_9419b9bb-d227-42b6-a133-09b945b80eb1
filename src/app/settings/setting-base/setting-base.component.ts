import { Component, OnInit } from "@angular/core";
import { AppRelease, VersionsService } from "../../versions/versions.service";
import { AppService } from "src/app/app.service";
import { DialogsService } from "../../utils/dialogs/dialogs.service";
import { take } from "rxjs/operators";

declare var chrome;
@Component({
  selector: "app-setting-base",
  templateUrl: "./setting-base.component.html",
  styleUrls: ["./setting-base.component.css"],
})
export class SettingBaseComponent {
  version = "--";
  platform = "chrome";
  checkingUpdates = false;

  constructor(public appService: AppService, private versionsService: VersionsService, public dialogsService: DialogsService) {}

  checkUpdates() {
    this.checkingUpdates = true;
    this.versionsService
      .latestRelease(this.platform)
      .pipe(take(1))
      .subscribe(
        (value) => {
          this.checkingUpdates = false;
          const release = value as AppRelease;
          const ignoredVersion = localStorage.getItem("ignoredVersion");
          const localVersion = !!ignoredVersion ? ignoredVersion : this.version;
          if (release.version > localVersion) {
            this.dialogsService.openDialog("update", { platform: "chrome", release });
          } else {
            this.appService.snackUp($localize`:@@AppIsLatestVersionHint:The app is the latest version.`);
          }
        },
        (error) => {
          this.checkingUpdates = false;
        }
      );
  }
}
