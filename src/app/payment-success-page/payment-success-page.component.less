.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px;
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  
  @media (min-width: 768px) {
    // PC端样式
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
  }
  
  @media (max-width: 767px) {
    // 移动端样式
    min-height: 100vh;
    height: auto;
    position: relative;
  }
}

.payTitle {
  font-size: 24px;
  font-weight: bold;
  margin-top: 20px;
}

.paySubTitle {
  font-size: 14px;
  margin-top: 10px;
  color: #C4C4C4
}

.instruction {
  font-size: 16px;
  margin-top: 20px;
}

