import { Component, OnInit } from "@angular/core";
import { Location } from "@angular/common";

@Component({
  selector: "app-play-status",
  template: `
    <div class="payStatus">
      <section fxLayoutAlign="space-between center" class="header">
        <div fxLayoutAlign="space-between center" (click)="back()">
          <div class="back" fxLayoutAlign="center center">
            <svg width="30" height="29" viewBox="0 0 30 29" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20.67 6.72531L18.89 4.95746L9 14.8454L18.9 24.7334L20.67 22.9656L12.54 14.8454L20.67 6.72531Z" fill="#333333" />
            </svg>
          </div>
          <h2>返回</h2>
        </div>
      </section>
    </div>

    <div *ngIf="true">
      <div class="content">
        <svg width="104" height="104" viewBox="0 0 104 104" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M52 91C73.5391 91 91 73.5391 91 52C91 30.4609 73.5391 13 52 13C30.4609 13 13 30.4609 13 52C13 73.5391 30.4609 91 52 91ZM71.5004 41.3246L66.1734 36.3999L43.0783 57.7505L33.9275 49.2909L28.6004 54.2156L43.0783 67.5999L71.5004 41.3246Z"
            fill="#FF5E5E" />
        </svg>
        <div class="payTitle" i18n>Successful Recharge</div>
      </div>
    </div>
  `,
  styles: [
    `
      .payStatus {
        color: #000;
        min-height: 100%;
        display: grid;
        grid-template-rows: 100px auto 60px;
        grid-template-columns: 100%;
        grid-template-areas:
          "header"
          "main"
          "footer";
      }
      .header {
        grid-area: header;
      }
      .btn {
        border-radius: 0.08rem;
        width: 100%;
        height: 0.62rem;
        font-weight: 500;
        font-size: 0.2rem;
        line-height: 0.28rem;
        background: #3963ef;
        color: #fff;
      }
      .content {
        text-align: center;
        position: absolute;
        top: 35%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      .payTitle {
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 500;
        font-size: 32px;
        line-height: 32px;
        color: #022247;
        flex: none;
        order: 1;
        flex-grow: 0;
        margin-top: 16px;
      }
      .paySubTitle {
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        text-align: center;
        color: #a0aec0;
        margin-top: 16px;
      }
      .footer_title {
        width: 100%;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        text-align: center;
        color: #a0aec0;
        display: flex;
        align-items: flex-end;
      }
    `,
  ],
})
export class PayStatusComponent implements OnInit {
  constructor(private location: Location) {}

  /**
   * 回退
   */
  back = () => {
    this.location.back();
  };

  ngOnInit(): void {}
}
