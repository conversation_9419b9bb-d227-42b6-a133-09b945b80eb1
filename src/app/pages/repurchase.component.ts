import { AfterViewInit, Component, ElementRef, Input, OnInit, ViewChild } from "@angular/core";
import { Location } from "@angular/common";
import { MatBottomSheet } from "@angular/material/bottom-sheet";
import { ActivatedRoute, NavigationEnd, Router } from "@angular/router";
import { APIManager, BillingCycles, BuyType, DeductionType, defaultBillingCycles, Product, Service, ServiceService } from "@flashvpn-io/web-core";
import { AppService } from "src/app/app.service";
import { combineLatest, interval } from "rxjs";
import { filter, flatMap, take, tap } from "rxjs/operators";
import { Rule } from "@flashvpn-io/web-core";
import { WalletsService } from "@flashvpn-io/web-core";
import { PaymentMethodService } from "@flashvpn-io/web-core";
import { PayType } from "@flashvpn-io/web-core";
import { SelectCouponComponent } from "../component/select-coupon.component";
import { SelectBalanceComponent } from "../component/select-balance.component";
import { SelectCodeComponent } from "../component/select-code.component";
import { PaymentDetailsComponent } from "../component/payment-details.component";
import { DeductionComponent } from "../component/deduction.component";
import { BillingCyclesComponent } from "../component/billing-cycles.component";
import { BuyProductsComponent } from "../component/buy-products.component";
import { Utils } from "../utils/utils";
import { IN_PAYMENT } from "../services/payment.service";

@Component({
  selector: "app-repurchase",
  template: `
    <div class="repurchase">
      <app-billing-cycles #billingCyclesComponent [currentProduct]="buyProductsComponent.currentProduct$ | async"></app-billing-cycles>
      <div class="item-box">
        <app-buy-products #buyProductsComponent [currentBillingCycle]="billingCyclesComponent.currentBillingCycle$ | async"></app-buy-products>
      </div>
      <app-payment-methods></app-payment-methods>
      <app-deduction
        #deductionComponent
        [service]="serviceService.currentService$ | async"
        [product]="buyProductsComponent.currentProduct$ | async"
        [currentBillingCycle]="billingCyclesComponent.currentBillingCycle$ | async"></app-deduction>
      <app-payment-details
        #paymentDetailsComponent
        [buyType]="BuyType.SERVICE"
        [loading]="loading"
        [currentBillingCycle]="billingCyclesComponent.currentBillingCycle$ | async"
        [deductionAmount]="deductionComponent.deductionAmount$ | async"
        [origialAmount]="deductionComponent.getOrigialAmount()"
        [deDutiontoString]="deductionComponent.deDutiontoString()"
        [amountAfterDeduction]="deductionComponent.getAmountAfterDeduction()"
        [submitted]="submitted"
        (buy)="buy()"></app-payment-details>
    </div>
  `,
  styles: [
    `
      .repurchase {
        width: 100%;
        max-width: 1300px;
        margin: 0 auto 250px auto;
      }
      .item-box {
        margin-top: 50px;
      }
      .deduction {
        background: #ffffff;
        border-radius: 2px;
        padding: 20px 10px 10px 10px;
      }
    `,
  ],
})
export class RepurchaseComponent {
  @ViewChild("billingCyclesComponent") billingCyclesComponent: BillingCyclesComponent;

  @ViewChild("buyProductsComponent") buyProductsComponent: BuyProductsComponent;

  @ViewChild("deductionComponent") deductionComponent: DeductionComponent;

  @ViewChild("paymentDetailsComponent") paymentDetailsComponent: PaymentDetailsComponent;

  protected readonly BuyType = BuyType;

  public loading = false;

  public submitted = false;

  constructor(
    public router: Router,
    public serviceService: ServiceService,
    private paymentMethodService: PaymentMethodService,
    private apiManager: APIManager,
    private appService: AppService
  ) { }

  async buy() {
    Utils.openPayWindow();
    this.submitted = true;
    const currentMethod = this.paymentMethodService.currentMethod$.value;
    const currentService = this.serviceService.currentService$.value;
    const currentProduct = this.buyProductsComponent.currentProduct$.value;
    const currentBillingCycle = this.billingCyclesComponent.currentBillingCycle$.value;
    const deductionAmount = this.deductionComponent?.deductionAmount$.value;
    const deductionType = this.deductionComponent?.currentDeduction$.value;
    const currentCoupon = this.deductionComponent?.selectCouponComponent?.currentCoupon?.id?.toString();
    const discountCode = this.deductionComponent?.selectCodeComponent?.discountCode;
    try {
      if (currentService?.productId !== currentProduct?.id) {
        await this.apiManager
          .upgradeService(currentService?.id, {
            productId: currentProduct?.id,
            billingCycle: currentBillingCycle?.id,
          })
          .toPromise();
      } else if (currentService?.billingcycle !== currentBillingCycle?.id) {
        await this.apiManager.updateBillingCycle(currentService?.id, { billingCycle: currentBillingCycle?.id }).toPromise();
      }
      const invoice = await this.apiManager.fetchLatestInvoice(currentService).toPromise();
      if (deductionAmount > 0) {
        if (deductionType === DeductionType.voucher) {
          await this.apiManager.deductionInvoice(invoice, deductionAmount, currentCoupon, undefined, currentBillingCycle?.id).toPromise();
        } else if (deductionType === DeductionType.balance) {
          await this.apiManager.deductionInvoice(invoice, deductionAmount, undefined, undefined, currentBillingCycle?.id).toPromise();
        } else if (deductionType === DeductionType.discount_code) {
          await this.apiManager.deductionInvoice(invoice, deductionAmount, undefined, discountCode, currentBillingCycle?.id).toPromise();
        }
      }
      const data = await this.apiManager.payInvoice(invoice, currentMethod.payType, "").toPromise();
      if (data.redirect_url !== undefined) {
        if (this.appService.isElectron() || this.appService.isExtension()) {
          window.open(data.redirect_url);
          Utils.closePayWindow();
        } else {
          // window.location = data.redirect_url;
          Utils.sendPayUrl(data.redirect_url);
          await this.router.navigate(["payment-status"], { queryParams: { status: IN_PAYMENT, id: invoice.id } });
        }
      }
    } catch (e) {
      Utils.closePayWindow();
      this.appService.snackUp(e?.error?.message);
    } finally {
      this.submitted = false;
    }
  }
}
