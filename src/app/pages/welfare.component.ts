import { Component, OnInit, ViewChild } from "@angular/core";
import { APIManager, DynamicDialogService, UsersService, WelfareService } from "@flashvpn-io/web-core";
import { AppService } from "../app.service";
import { Router } from "@angular/router";
import { PopularityComponent } from "../component/popularity.component";
import { CommunityComponent } from "../component/community.component";
import { IVoucher, VoucherComponent, VoucherType } from "../component/voucher.component";
import { ConfettiService } from "../confetti.service";
import { DialogsService } from "../utils/dialogs/dialogs.service";
import { RewardService } from "@flashvpn-io/web-core";
import { WalletsService } from "@flashvpn-io/web-core";
import { TipsComponent } from "../component/tips.component";
import * as moment from "moment";
import { CouponsCountdownComponent } from "../component/coupons-countdown.component";
import { WaitCollectionComponent } from "../component/wait-collection.component";
@Component({
  selector: "app-welfare",
  template: `
    <div fxLayout="row" fxLayoutAlign="start center" fxLayout.lt-sm="column" fxLayoutGap.lt-sm="5px" fxLayoutAlign.lt-sm="start start">
      <div class="welfare">
        <mat-progress-bar *ngIf="loading" mode="indeterminate"></mat-progress-bar>
        <div class="head" *ngIf="(rewardService.availableCoupons$ | async)?.length > 0 || waitCollectionVouchers?.length > 0">
          <div class="head-box" *ngIf="(rewardService.availableCoupons$ | async)?.length > 0; else waitCollectionVoucherTemplate">
            <div (click)="couponsCountdownComponent.goToPaymentMethods()" class="coupons-countdown">
              <app-coupons-countdown #couponsCountdownComponent></app-coupons-countdown>
            </div>
          </div>
          <ng-template #waitCollectionVoucherTemplate>
            <div (click)="openTips(waitCollectionVouchers?.[0])" class="head-box">
              <div class="coupons-countdown"><app-wait-collection #waitCollectionComponent></app-wait-collection></div>
            </div>
          </ng-template>
        </div>
        <div
          class="voucher-center"
          [style]="{
            'border-radius': (rewardService.availableCoupons$ | async)?.length > 0 || waitCollectionVouchers?.length > 0 ? '0 0 8px 8px' : '8px'
          }">
          <svg width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M0 6C0 2.68629 2.68629 0 6 0H16C19.3137 0 22 2.68629 22 6V7C20.3431 7 19 8.34315 19 10C19 11.6569 20.3431 13 22 13V14C22 17.3137 19.3137 20 16 20H6C2.68629 20 0 17.3137 0 14V13C1.65685 13 3 11.6569 3 10C3 8.34315 1.65685 7 0 7V6ZM10.5471 3.57141H11.9534V15.7143H10.5471V3.57141ZM9.84375 4.43428C9.33324 4.55724 8.88188 4.7572 8.48724 5.03661C7.7163 5.55644 7.3451 6.27843 7.3451 7.18813C7.3451 8.09783 7.73057 8.79094 8.51579 9.26745C8.7246 9.38813 9.16886 9.56485 9.84375 9.7976V8.1968C9.79806 8.17622 9.75989 8.15765 9.7293 8.14115C9.2439 7.88124 9.01547 7.52024 9.01547 7.08705C9.01547 6.5961 9.21534 6.23511 9.64364 6.01851C9.70564 5.98454 9.77232 5.95389 9.84375 5.92645V4.43428ZM12.6562 9.16789V10.7322C12.7381 10.7646 12.7998 10.7914 12.8416 10.8125C13.4698 11.1302 13.7981 11.5634 13.7981 12.1121C13.7981 12.5453 13.5697 12.8774 13.1414 13.1373C12.9984 13.2193 12.8364 13.2883 12.6562 13.344V14.8958C13.3578 14.7721 13.9395 14.5395 14.3978 14.2058C15.1116 13.6716 15.4685 12.9351 15.4685 12.011C15.4685 11.058 15.0259 10.3216 14.1408 9.78728C13.8701 9.63081 13.3767 9.42136 12.6562 9.16789ZM9.84375 14.8515V13.2518C9.7215 13.1975 9.61204 13.1351 9.51515 13.0651C9.07258 12.733 8.80132 12.1698 8.68711 11.3901H7.03102C7.1024 12.7041 7.57353 13.6571 8.43013 14.2636C8.81549 14.534 9.28749 14.73 9.84375 14.8515ZM12.6562 6.06556V4.435C13.1892 4.55803 13.6421 4.75775 14.0123 5.03661C14.7261 5.57088 15.1402 6.40838 15.2401 7.53468H13.584C13.4412 6.89933 13.17 6.43726 12.7988 6.16291C12.7541 6.12818 12.7066 6.09575 12.6562 6.06556Z"
              fill="#FF5E5E" />
          </svg>
          <span class="voucher-center-text-1">
            我的券包
            <span style="margin: 0 10px">|</span>
          </span>
          <span class="voucher-center-text-2">
            当前
            <span style="color: #FF5E5E">{{ (rewardService.availableCoupons$ | async)?.length }}</span>
            张可用
            <span style="color: #FF5E5E;cursor: pointer;margin-left: 10px" (click)="goToMyVoucher()">查看>></span>
          </span>
        </div>
        <div class="content">
          <div class="content-item">
            <div class="content-title">任务奖励</div>
            <div class="welfare-item">
              <div class="welfare-item-title">
                <div class="title-num">1</div>
                <div class="title-text">安装客户端，领红包奖励</div>
              </div>
              <app-voucher *ngIf="!clientWelfareLoading" [vouchers]="clientVouchers" [trigger]="trigger"></app-voucher>
              <ngx-skeleton-loader *ngIf="clientWelfareLoading" [theme]="{ height: '50px' }" count="3"></ngx-skeleton-loader>
            </div>
            <div class="welfare-item">
              <div class="welfare-item-title">
                <div class="title-num">2</div>
                <div class="title-text">加入Flash社区</div>
              </div>
              <app-community *ngIf="!communityWelfareLoading" [communityList]="communityVouchers" [trigger]="communityTrigger"></app-community>
              <ngx-skeleton-loader *ngIf="communityWelfareLoading" [theme]="{ height: '50px' }" count="3"></ngx-skeleton-loader>
            </div>
          </div>
          <div class="content-item">
            <div class="content-title">个人成就</div>
            <div class="welfare-item">
              <div class="welfare-item-title">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24ZM12 5C8.13401 5 5 8.13401 5 12C5 12.5523 4.55228 13 4 13C3.44772 13 3 12.5523 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 12.5523 20.5523 13 20 13C19.4477 13 19 12.5523 19 12C19 8.13401 15.866 5 12 5ZM14.1399 16.2635C14.6719 16.8048 15 17.5471 15 18.3661C15 20.0229 13.6569 21.3661 12 21.3661C10.3431 21.3661 9 20.0229 9 18.3661C9 16.7092 10.3431 15.3661 12 15.3661C12.1336 15.3661 12.2653 15.3748 12.3943 15.3918C12.4162 15.3025 12.451 15.2148 12.4992 15.1312L15.7425 9.50099C16.0181 9.02243 16.6296 8.85796 17.1081 9.13364C17.5867 9.40932 17.7512 10.0208 17.4755 10.4993L14.2322 16.1295C14.2046 16.1773 14.1737 16.222 14.1399 16.2635ZM11 18.3661C11 17.8138 11.4477 17.3661 12 17.3661C12.5523 17.3661 13 17.8138 13 18.3661C13 18.9184 12.5523 19.3661 12 19.3661C11.4477 19.3661 11 18.9184 11 18.3661Z"
                    fill="#FF5E5E" />
                </svg>
                <div class="title-text">Flash里程</div>
              </div>
              <div class="prompt">
                <div>
                  Flash 已守护您的网络
                  <span style="color: #FF5E5E">{{ welfareService.serviceDays$ | async }}</span>
                  天，感恩一路同行！
                </div>
              </div>
              <div *ngIf="!mileageWelfareLoading && mileageVouchers.length == 0">您已完成所有里程任务，没有更多里程红包可以领取，去看看其他的活动吧～</div>
              <app-voucher *ngIf="!mileageWelfareLoading" [vouchers]="mileageVouchers" [trigger]="openTips"></app-voucher>
              <ngx-skeleton-loader *ngIf="mileageWelfareLoading" [theme]="{ height: '50px' }" count="3"></ngx-skeleton-loader>
            </div>
            <div class="welfare-item">
              <div class="welfare-item-title">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M12 24.0001C18.6274 24.0001 24 18.6275 24 12.0001C24 5.37264 18.6274 6.10352e-05 12 6.10352e-05C5.37258 6.10352e-05 0 5.37264 0 12.0001C0 18.6275 5.37258 24.0001 12 24.0001ZM12 12.0001C13.6569 12.0001 15 10.6569 15 9.00006C15 7.34321 13.6569 6.00006 12 6.00006C10.3431 6.00006 9 7.34321 9 9.00006C9 10.6569 10.3431 12.0001 12 12.0001ZM17 9.00006C17 10.5203 16.3215 11.8821 15.2509 12.7991C17.4798 13.97 19 16.3075 19 19.0001C19 19.5523 18.5523 20.0001 18 20.0001C17.4477 20.0001 17 19.5523 17 19.0001C17 16.2386 14.7614 14.0001 12 14.0001C9.23858 14.0001 7 16.2386 7 19.0001C7 19.5523 6.55228 20.0001 6 20.0001C5.44772 20.0001 5 19.5523 5 19.0001C5 16.3075 6.52021 13.97 8.74913 12.7991C7.67847 11.8821 7 10.5203 7 9.00006C7 6.23864 9.23858 4.00006 12 4.00006C14.7614 4.00006 17 6.23864 17 9.00006Z"
                    fill="#FF5E5E" />
                </svg>
                <div class="title-text">Flash人气</div>
              </div>
              <app-popularity></app-popularity>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="welfare-claim" class="welfare-claim scale-in-center">
      <span class="material-icons claim-close" (click)="closeClaim()">close</span>
      <img class="welfare-claim-image" src="assets/images/welfare-claim.png" />
      <div class="claim-title">恭喜获得</div>
      <div class="claim-voucher-title" id="claim-voucher-title"></div>
      <div class="claim-voucher-amount">
        <span id="claim-voucher-amount"></span>
        <span style="font-size: 14px">元</span>
      </div>
      <div class="claim-voucher-btn" (click)="goToMyVoucher()">查看红包</div>
      <div class="claim-voucher-foot">立即存入您的券包</div>
    </div>
    <div id="welfare-claim-mask" class="welfare-claim-mask"></div>
  `,
  styles: [
    `
      .welfare {
        width: 100%;
      }
      .head {
        width: 100%;
        display: flex;
        position: relative;
        align-items: center;
        cursor: pointer;
        margin-top: 5px;
      }
      .head-box {
        box-shadow: 0px 0px 10px 0px rgba(255, 94, 94, 0.1);
        background: linear-gradient(45deg, rgba(255, 239, 239, 1), rgba(255, 253, 239, 0.8));
        width: 100%;
        cursor: pointer;
      }
      .coupons-countdown {
        width: 366px;
        height: 225px;
      }
      .voucher-center {
        width: auto;
        margin-bottom: 32px;
        border-radius: 0 0 8px 8px;
        background: #fff;
        box-shadow: 0px 0px 4px 0px rgba(196, 196, 196, 0.5);
        display: flex;
        padding: 20px 24px;
        align-items: center;
      }
      .voucher-center-text-1 {
        color: #484652;
        font-size: 16px;
        font-weight: 500;
        margin-left: 10px;
      }
      .voucher-center-text-2 {
        color: #484652;
        font-size: 16px;
      }
      @media screen and (max-width: 599px) {
        .head-title-1 {
          bottom: 82px;
          left: 62px;
        }
        .head-title-2 {
          bottom: 62px;
          left: 62px;
        }
        .voucher-center {
          padding: 10px 12px;
        }
        .coupons-countdown {
          width: 100%;
          height: auto;
        }
      }
      .content {
        width: 100%;
      }
      .content-item {
        width: 100%;
        margin-bottom: 32px;
      }
      .content-title {
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 600;
        font-size: 20px;
        line-height: 28px;
        color: #484652;
      }
      .welfare-item {
        width: 100%;
      }
      .welfare-item-title {
        display: flex;
        align-items: center;
        margin: 24px 0;
      }
      .title-num {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: #ff5e5e;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #ffffff;
      }
      .title-text {
        margin-left: 10px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
        color: #484652;
      }
      .prompt {
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 22px;
        color: #616577;
        margin-bottom: 20px;
      }
      .welfare-claim {
        width: 300px;
        height: 422px;
        position: fixed;
        left: 50%;
        top: 50%;
        margin-top: -211px;
        margin-left: -150px;
        z-index: 999;
        display: none;
        align-items: center;
        justify-content: center;
      }
      .welfare-claim-image {
        width: 300px;
        height: 422px;
      }
      .welfare-claim-mask {
        display: none;
        overflow: hidden;
        z-index: 990;
        width: 100%;
        height: 1600px;
        position: fixed;
        top: 0px;
        left: 0px;
        background: rgb(0, 0, 0);
        opacity: 0.6;
      }
      .claim-title {
        color: #ffffff;
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 16px;
        position: absolute;
        top: 120px;
      }
      .claim-voucher-title {
        color: #876e4c;
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 16px;
        position: absolute;
        top: 210px;
      }
      .claim-voucher-amount {
        color: #ff5e5e;
        font-style: normal;
        font-weight: 600;
        font-size: 20px;
        line-height: 20px;
        position: absolute;
        top: 240px;
      }
      .claim-voucher-btn {
        color: #fceabe;
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 16px;
        position: absolute;
        top: 328px;
        margin-left: 10px;
        cursor: pointer;
      }
      .claim-voucher-foot {
        color: #876e4c;
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 12px;
        position: absolute;
        top: 360px;
        margin-left: 10px;
      }
      .claim-close {
        position: absolute;
        top: 70px;
        right: 20px;
        color: #ffffff;
        cursor: pointer;
      }
      .scale-in-center {
        -webkit-animation: scale-in-center 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
        animation: scale-in-center 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
      }
      @-webkit-keyframes scale-in-center {
        0% {
          -webkit-transform: scale(0);
          transform: scale(0);
          opacity: 1;
        }
        100% {
          -webkit-transform: scale(1);
          transform: scale(1);
          opacity: 1;
        }
      }
      @keyframes scale-in-center {
        0% {
          -webkit-transform: scale(0);
          transform: scale(0);
          opacity: 1;
        }
        100% {
          -webkit-transform: scale(1);
          transform: scale(1);
          opacity: 1;
        }
      }
    `,
  ],
})

export class WelfareComponent implements OnInit {
  @ViewChild("popularityComponent") popularityComponent: PopularityComponent;

  @ViewChild("communityComponent") communityComponent: CommunityComponent;

  @ViewChild("voucherComponent") voucherComponent: VoucherComponent;

  @ViewChild("couponsCountdownComponent") couponsCountdownComponent: CouponsCountdownComponent;

  @ViewChild("waitCollectionComponent") waitCollectionComponent: WaitCollectionComponent;

  loading = false;

  clientWelfareLoading = false;

  mileageWelfareLoading = false;

  communityWelfareLoading = false;

  submitted = false;

  amount = 0;

  gift = 0;

  public clientVouchers: IVoucher[] = [];

  public mileageVouchers: IVoucher[] = [];

  public communityVouchers: IVoucher[] = [];

  voucherNum = 0;

  public waitCollectionVouchers: IVoucher[] = [];


  tests: PaidInfoDto = {
    invoiceId: 1,
    transactionId: "1234567890",
    amount: 100,
    fees: 10,
    date: moment.utc().toDate(),
    gateway: "alipay"
  }

  constructor(
    public usersService: UsersService,
    private appService: AppService,
    private apiManager: APIManager,
    private router: Router,
    private confettiService: ConfettiService,
    private dialogsService: DialogsService,
    public rewardService: RewardService,
    private walletsService: WalletsService,
    public welfareService: WelfareService,
    private customOverlayService: DynamicDialogService
  ) { }

  ngOnInit() {
    this.getCountByUser();
    this.getWalletsRule();
    this.getClientWelfareRule();
    this.getMileageWelfareRule();
    this.getCommunityRule();
  }

  goToWallet() {
    this.router.navigate(["wallet"]);
  }

  getWalletsRule = () => {
    this.loading = true;
    this.walletsService.rechargeList$.subscribe({
      next: (rules) => {
        // if (rules && rules.length > 0) {
        //   const filterList = rules.filter((rule) => rule?.gift > 0);
        //   if (filterList.length > 0) {
        //     const sortList = filterList.sort((a, b) => b.amount - a.amount);
        //     this.amount = sortList[0].amount;
        //     this.gift = sortList[0].gift;
        //     this.showBanner = true;
        //   }
        // }
        this.loading = false;
      },
      error: (res) => {
        this.loading = false;
        this.appService.snackUp(res?.error?.message);
      },
    });
  };

  getClientWelfareRule = () => {
    this.clientWelfareLoading = true;
    this.welfareService.clientWelfareRule$.subscribe((rules) => {
      this.clientVouchers = rules;
      this.waitCollectionVouchers = this.waitCollectionVouchers.concat(...rules.filter((item) => !item.receive && item.achieve));
      this.clientWelfareLoading = false;
    });
  };

  getMileageWelfareRule = () => {
    this.mileageWelfareLoading = true;
    this.welfareService.mileageWelfareRule$.subscribe((rules) => {
      this.mileageVouchers = rules.filter(rule => !rule?.expireDays || rule?.expireDays <= 0);
      this.waitCollectionVouchers = this.waitCollectionVouchers.concat(...rules.filter((item) => !item.receive && item.achieve && (!item?.expireDays || item?.expireDays <= 0)));
      this.mileageWelfareLoading = false;
    });
  };

  getCommunityRule = () => {
    this.communityWelfareLoading = true;
    this.welfareService.communityRule$.subscribe((rules) => {
      this.communityVouchers = rules;
      this.waitCollectionVouchers = this.waitCollectionVouchers.concat(...rules.filter((item) => !item.receive && item.achieve));
      this.communityWelfareLoading = false;
    });
  };

  communityTrigger = (voucher: IVoucher) => {
    if (voucher.achieve) {
      this.trigger(voucher);
    } else {
      switch (voucher.type) {
        case VoucherType.BIND_TG:
          this.router.navigate(["tg"]);
          break;
        case VoucherType.ENABLE_FCM:
          this.router.navigate(["fcm"]);
          break;
        case VoucherType.QUESTIONNAIRE:
          window.open(`https://forms.gle/sBJ28MqhvmKYNEyz9`);
          break;
        default:
          break;
      }
    }
  };

  openTips = (voucher: IVoucher) => {
    const tips = this.customOverlayService.open(TipsComponent);
    tips.title = "确认";
    tips.content = `领取红包以后，过期时间内请务必使用，当前红包过期时间为 ${moment.utc().add(30, "days").format("YYYY年MM月DD号")}`;
    tips.onOk.subscribe(() => {
      this.trigger(voucher);
      this.customOverlayService.close();
    });
    tips.onCancel.subscribe(() => {
      this.customOverlayService.close();
    });
  };

  trigger = (voucher: IVoucher) => {
    this.apiManager.receiveWelfare(voucher.id, voucher.type).subscribe(
      (data) => {
        if (this.dialogsService.isMobile() || window.innerWidth <= 599) {
          this.confettiService.canon(300);
        } else {
          this.confettiService.fire();
          const matDrawerMask = document.getElementById("mat-drawer-mask");
          matDrawerMask.style.display = "block";
        }
        const welfareClaim = document.getElementById("welfare-claim");
        welfareClaim.style.display = "flex";
        const welfareClaimMask = document.getElementById("welfare-claim-mask");
        welfareClaimMask.style.display = "block";
        const claimVoucherTitle = document.getElementById("claim-voucher-title");
        claimVoucherTitle.innerText = voucher.title;
        const claimVoucherAmount = document.getElementById("claim-voucher-amount");
        claimVoucherAmount.innerText = String(voucher.award);
      },
      (res) => {
        this.appService.snackUp(res?.error?.message);
      },
      () => {
        this.waitCollectionVouchers = [];
        switch (voucher.type) {
          case VoucherType.CLIENT:
            this.welfareService.getClientWelfareRule();
            break;
          case VoucherType.MILEAGE:
            this.welfareService.getMileageWelfareRule();
            break;
          case VoucherType.BIND_TG:
          case VoucherType.ENABLE_FCM:
          case VoucherType.QUESTIONNAIRE:
            this.welfareService.getCommunityRule();
            break;
        }
        this.rewardService.getRewards();
      }
    );
  };

  goToMyVoucher = async () => {
    await this.closeClaim();
    this.router.navigate(["my-voucher"]);
  };

  getCountByUser() {
    this.rewardService.countByUser$.subscribe((num) => {
      this.voucherNum = num;
    });
  }

  async closeClaim() {
    const welfareClaim = document.getElementById("welfare-claim");
    const welfareClaimMask = document.getElementById("welfare-claim-mask");
    const matDrawerMask = document.getElementById("mat-drawer-mask");
    welfareClaim.style.display = "none";
    welfareClaimMask.style.display = "none";
    matDrawerMask.style.display = "none";
  }
}


interface PaidInfoDto {
  invoiceId: number;
  transactionId: string;
  amount: number;
  fees: number;
  date: Date;
  gateway: string;
}
