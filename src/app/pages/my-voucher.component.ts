import { Component, OnInit, ViewChild } from "@angular/core";
import { AppService } from "../app.service";
import { Router } from "@angular/router";
import { APIManager } from "@flashvpn-io/web-core";
import { CouponListComponent } from "../component/coupon-list.component";
import { Reward, RewardStatus } from "@flashvpn-io/web-core";
import { RewardService } from '@flashvpn-io/web-core';
@Component({
  selector: 'app-my-voucher',
  template: `
  <div class="my-voucher">
  <mat-tab-group>
    <mat-tab label="未使用">
        <app-my-voucher-item [vouchers] = "claimedList"></app-my-voucher-item>
    </mat-tab>
    <mat-tab label="已使用">
        <app-my-voucher-item [vouchers] = "usedList"></app-my-voucher-item>
    </mat-tab>
    <mat-tab label="已失效">
        <app-my-voucher-item [vouchers] = "expiredList"></app-my-voucher-item>
    </mat-tab>
  </mat-tab-group>
</div>
<div class="my-voucher-mobile">
  <mat-tab-group mat-stretch-tabs>
    <mat-tab label="未使用">
      <app-my-voucher-item [vouchers] = "claimedList"></app-my-voucher-item>
    </mat-tab>
    <mat-tab label="已使用">
      <app-my-voucher-item [vouchers] = "usedList"></app-my-voucher-item>
    </mat-tab>
    <mat-tab label="已失效">
      <app-my-voucher-item [vouchers] = "expiredList"></app-my-voucher-item>
    </mat-tab>
  </mat-tab-group>
</div>

  `,
  styles: [`
.my-voucher{
  width: 100%;
  display: block;
}
.card-list{
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-direction: initial;
  gap: 24px;
}
.my-voucher-mobile{
  width: 100%;
  display: none;
}
@media screen and (max-width: 599px){
  .my-voucher{
    display: none;
  }
  .my-voucher-mobile{
    display: block;
  }
}

  `]
})
export class MyVoucherComponent implements OnInit {

  @ViewChild('MyVoucherItemComponent') voucherComponent: CouponListComponent;

  public claimedList: Reward[] = [];

  public usedList: Reward[] = [];

  public expiredList: Reward[] = [];

  public loading = false;

  constructor(
    private appService: AppService,
    private router: Router,
    private apiManager: APIManager,
    public rewardService: RewardService
  ) { }

  async ngOnInit() {
    this.getRewards();
  }

  getRewards() {
    this.loading = true;
    this.rewardService.coupons$.subscribe(
      rewards => {
        if (rewards){
          this.claimedList = rewards.filter(r => r.status === RewardStatus.CLAIMED);
          this.usedList = rewards.filter(r => r.status === RewardStatus.USED);
          this.expiredList = rewards.filter(r => r.status === RewardStatus.EXPIRED);
        }
      },
      res => {
        this.loading = false;
        this.appService.snackUp(res?.error?.message);
      },
      () => this.loading = false);
  }

}
