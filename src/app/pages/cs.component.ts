import { Component, OnInit } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { UsersService } from "@flashvpn-io/web-core";
import { NotificationService, OPEN_DRAWER } from "@flashvpn-io/web-core";
import { AppService } from "../app.service";

@Component({
  selector: "app-cs",
  template: `
    <div id="cs">
      <div id="csHeader" fxLayout.gt-sm="row" fxLayoutAlign="space-between center">
        <div fxLayout="row" fxLayoutGap="20px" fxLayoutAlign.gt-md="space-between center">
          <div class="mobile-cs-header" (click)="openDrawer()" fxHide.gt-sm fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="start center">
            <mat-icon svgIcon="bars" style="margin-right: 10px;"></mat-icon>
            <a class="navi-link" id="brand" i18n routerLink="/">FlashVPN</a>
          </div>
          <div class="desktop-cs-header" fxHide.sm fxHide.xs fxLayout.gt-sm="row" fxLayoutGap.gt-sm="20px" fxLayoutAlign.gt-md="start center">
            <a class="navi-link" id="brand" i18n routerLink="/">FlashVPN</a>
            <a id="landing-trial" class="navi-link" *ngIf="!isLogged" routerLink="/trial" routerLinkActive="navi-link-active">试用</a>
            <a id="landing-sign-up" class="navi-link" *ngIf="!isLogged" routerLink="/users/signup" routerLinkActive="navi-link-active" i18n>
              Sign Up
            </a>
            <a id="landing-sign-in" class="navi-link" *ngIf="!isLogged" routerLink="/users/signin" routerLinkActive="navi-link-active" i18n>
              Sign In
            </a>
            <a id="landing-services" class="navi-link" routerLink="/services/dashboard" routerLinkActive="navi-link-active" i18n>Services</a>
            <a id="landing-wallets" class="navi-link" *ngIf="isLogged" routerLink="/wallet" routerLinkActive="navi-link-active" i18n>Wallet</a>
            <a id="landing-welfare" class="navi-link" *ngIf="isLogged" routerLink="/welfare" routerLinkActive="navi-link-active" i18n>
              Welfare Center
            </a>
            <a id="landing-apps" class="navi-link" routerLink="/apps" routerLinkActive="navi-link-active" i18n>Apps</a>
            <a id="landing-help" class="navi-link" target="_blank" href="{{ appService.guidesURL() }}" i18n>Help Center</a>
          </div>
        </div>
      </div>
      <div id="csBody">
        <div style="height: 100%;display: flex;align-items: center;justify-content: center;">请等待客服界面打开。。。</div>
      </div>
    </div>
  `,
  styles: [
    `
      #cs {
        max-width: 1200px;
        height: 100%;
        margin: 0 auto;
        display: grid;
        grid-template-rows: 100px auto;
        grid-template-columns: 0 auto 0;
        grid-template-areas: ". header ." ". main . ";
      }

      #csHeader {
        grid-area: header;
        flex-direction: row;
        place-content: center space-between;
        align-items: center;
        box-sizing: border-box;
        display: flex;
      }

      #csBody {
        grid-area: main;
      }

      .navi-link {
        font-family: "Libre Franklin", sans-serif;
        font-style: normal;
        font-weight: normal;
        font-size: 24px;
        line-height: 29px !important;
        color: #000000;
        text-decoration-line: none;
        margin-right: 20px;
      }

      #brand {
        font-size: 30px;
        font-family: Merriweather, sans-serif;
        font-style: italic;
        text-decoration-line: none;
        color: black;
        margin-right: 60px !important;
      }

      #sellingPoint img {
        width: 52px;
        height: 54px;
      }

      .mobile-cs-header {
        display: none;
      }
      .desktop-cs-header {
        display: flex;
      } 

      @media screen and (max-width: 599px) {
        .mobile-cs-header {
          display: flex;
        }
        .desktop-cs-header {
          display: none;
        } 
      }
    `,
  ],
})
export class CsComponent implements OnInit {
  private userId: string;
  private userEmail: string;
  isLogged = false;
  constructor(
    public route: ActivatedRoute,
    public usersService: UsersService,
    public appService: AppService,
    private notificationService: NotificationService
  ) { }

  async ngOnInit(): Promise<void> {
    this.route.queryParams.subscribe((queryParams) => {
      const { id, email } = queryParams;
      this.userEmail = email;
      this.userId = id;
    });

    window.addEventListener("chatwoot:ready", () => {
      if (this.userId && this.userEmail) {
        // @ts-ignore
        window.$chatwoot.setUser(this.userId, {
          name: this.userEmail, // Name of the user
          email: this.userEmail, // Email of the user
        });
      }
      const dom = document.getElementsByClassName("woot-widget-bubble")[0] as HTMLElement;
      if (dom) {
        dom.click();
        const iframe = document.getElementById("chatwoot_live_chat_widget");
      }
    });
    this.isLogged = await this.usersService.isLoggedIn();
  }

  openDrawer() {
    this.notificationService.post(OPEN_DRAWER);
  }
}
