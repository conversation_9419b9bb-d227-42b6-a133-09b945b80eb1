import { Component, OnInit } from '@angular/core';
import { ServiceService } from '@flashvpn-io/web-core';

@Component({
    selector: 'app-myservices',
    templateUrl: './myservices.component.html',
    styleUrls: ['./myservices.component.css']
})
export class MyservicesComponent implements OnInit {

    constructor(private serviceService: ServiceService) { }

    async ngOnInit() {
        // 確保服務數據已加載
        await this.serviceService.refreshServiceInfo();
    }

} 