.container {
  padding: 20px;
}

.serve-layout {
  margin: 0 auto;
  max-width: 1300px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.serve {
  width: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: space-evenly;
  gap: 10px;
  padding: 0 0 40px 0;
}

.appServiceTile {
  flex: 0 0 327px;
  height: 100%;
}

.serviceTitle {
  font-size: 16px;
  font-weight: 600;
  line-height: 16px;
  text-align: left;
  color: rgba(0, 0, 0, 1);
  padding: 0 0 20px 0;
}

.serviceUpgrade {
  flex: 1 1 327px;
  height: 100%;
}

.rules-section {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
}

.rules-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #808080;
  font-size: 14px;
}

.rules-container {
  box-shadow: 0px 0px 10px 0px rgba(255, 94, 94, 0.1);
  padding: 32px 24px;
}

.device-container {
  margin-top: 32px;
}

@media screen and (max-width: 599px) {
  .serve {
    display: block;
    gap: 30px;
    padding: 10px 0 30px 0;
  }

  .appServiceTile {
    width: 100%;
  }

  .serviceUpgrade {
    width: 100%;
  }
}
