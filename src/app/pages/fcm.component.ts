import { Component, OnInit } from "@angular/core";
import { environment } from "../../environments/environment";
import { Router } from "@angular/router";
import { MatSnackBar } from "@angular/material/snack-bar";
import { APIManager } from "@flashvpn-io/web-core";
import { UsersService } from "@flashvpn-io/web-core";
import { AppService } from "../app.service";

@Component({
  selector: "app-fcm",
  template: `
    <div class="container" fxFill>
      <div class="title" i18n>第一次接收通知以获取 5HKD 余额</div>
      <div class="content">
        <div class="desc" i18n>
          为庆祝推送消息功能上线，首次启用通知功能的用户可获得5HKD余额作为奖励。
        </div>
        <div class="desc" i18n>
          怕被打扰？ 别担心，我们只会向您发送重要信息，您可以随时在设置中将其关闭
        </div>
        <div class="desc">
          <button *ngIf="isLoggedIn" id="enableNotification" mat-raised-button color="warn" (click)="enableNotification()" i18n>
            启用通知
          </button>
          <button *ngIf="!isLoggedIn" id="goToSignPage" mat-raised-button color="warn" (click)="goToSignIn()" i18n>前往登录</button>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .container {
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 100%;
        width: 100%;

        .title {
          width: 100%;
          text-align: center;
          font-style: normal;
          font-weight: 600;
          font-size: 20px;
          line-height: 28px;
          color: #ff5e5e;
        }

        .content {
          width: 100%;
          margin-top: 20px;
          .desc {
            margin-top: 10px;
            font-style: normal;

            font-size: 16px;
            line-height: 30px;
            color: #000000;
          }

          .strong {
            font-weight: 600;
          }
        }
      }
    `,
  ],
})
export class FcmComponent implements OnInit {
  isLoggedIn = false;

  constructor(
    private router: Router,
    private snackBar: MatSnackBar,
    private appService: AppService,
    private apiManager: APIManager,
    private usersService: UsersService
  ) { }

  async ngOnInit(): Promise<void> {
    this.isLoggedIn = await this.usersService.isLoggedIn();
  }

  goToSignIn = () => {
    this.router.navigate(["users", "signin"], { queryParams: { url: this.router.url } });
  };

  async enableNotification() {
    const token = localStorage.getItem("token");
    if (!token) {
      this.snackBar.open("请在浏览器设置中启用通知功能！", "好的", { duration: 3000, verticalPosition: "top" });
      return;
    }
    this.apiManager.uploadFcmToken(token).subscribe(
      (res) => {
        this.snackBar.open("通知功能已启用！", "好的", { duration: 3000, verticalPosition: "top" });
      },
      (err) => {
        this.snackBar.open(err.error.message, "好的", { duration: 3000, verticalPosition: "top" });
      }
    );
  }
}
