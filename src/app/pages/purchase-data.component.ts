import { AfterViewInit, Component, OnInit, ViewChild } from "@angular/core";
import { DeductionType, FlowDataRule, PaymentMethodService } from "@flashvpn-io/web-core";
import { FlowDataService } from "@flashvpn-io/web-core";
import { ServiceService } from "@flashvpn-io/web-core";
import { APIManager } from "@flashvpn-io/web-core";
import { Router } from "@angular/router";
import { IN_PAYMENT } from "../services/payment.service";
import { AppService } from "src/app/app.service";
import { PaymentDetailsComponent } from "../component/payment-details.component";
import { BuyType } from "@flashvpn-io/web-core";
import * as moment from "moment/moment";
import { filter, tap } from "rxjs/operators";
import { DeductionComponent } from "../component/deduction.component";
import { BehaviorSubject } from "rxjs";

@Component({
  selector: "app-purchase-data",
  template: `
    <div class="purchase-data">
      <div class="subTitle">
        <div>流量包仅限流量重置时间之前使用，请按需购买</div>
        <div>当前购买流量包有效日期：{{ formatDate((serviceService.currentService$ | async)?.resetDate) }}</div>
      </div>

      <div class="data-plans" *ngIf="flowDataService.flowData$ | async as flowDataRules">
        <div [class]="selectedPlan?.id === rule.id ? 'data-plan active' : 'data-plan'" *ngFor="let rule of flowDataRules" (click)="selectPlan(rule)">
          <div class="select"><img src="assets/images/select.svg" alt="" /></div>
          <div class="data-amount">{{ rule.data }} GB</div>
          <div class="data-price">$ {{ rule.amount }}</div>
          <div class="data-validity">
            <span class="data-validity-day">{{ calculateDaysFromToday((serviceService.currentService$ | async)?.resetDate) }}</span>
            天 有效期
          </div>
        </div>
      </div>

      <app-payment-methods></app-payment-methods>

      <app-deduction
        #deductionComponent
        [service]="serviceService.currentService$ | async"
        [currentBillingCycle]="{ description: selectedPlan?.description, title: '', unit: '', id: selectedPlan?.id }"
        [excludeDeduction]="[DeductionType.discount_code, DeductionType.voucher]"
        [origialAmount]="(selectedPlan$ | async)?.amount"></app-deduction>
      <app-payment-details
        #paymentDetailsComponent
        [buyType]="BuyType.FLOWDATA"
        [loading]="loading"
        [currentBillingCycle]="{ description: selectedPlan?.description, title: '', unit: '', id: selectedPlan?.id }"
        [deductionAmount]="deductionComponent.deductionAmount$ | async"
        [origialAmount]="(selectedPlan$ | async)?.amount"
        [deDutiontoString]="deductionComponent.deDutiontoString()"
        [amountAfterDeduction]="deductionComponent.getAmountAfterDeduction()"
        [submitted]="submitted"
        (buy)="buy()"></app-payment-details>
    </div>
  `,
  styles: [
    `
      .purchase-data {
        max-width: 1300px;
        margin: 0 auto 250px auto;
      }
      .subTitle {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: gray;
      }
      .data-plans {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 40px;
        padding-top: 10px;
      }
      .data-plan {
        width: 100%;
        box-shadow: 0px 2px 10px 0px rgba(255, 94, 94, 0.1);
        padding: 30px 20px;
        cursor: pointer;
        position: relative;
        border: 1px solid transparent;
        transition: border-color 0.3s ease-in-out;
        text-align: center;
        border-radius: 5px;
      }
      .select {
        position: absolute;
        top: 8px;
        right: 4px;
        z-index: 9;
      }
      .data-plan:after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-width: 0 44px 44px 0;
        border-style: solid;
        border-color: transparent rgba(255, 94, 94, 1) transparent transparent;
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
      }
      .active {
        border-color: rgba(255, 94, 94, 1);
      }
      .active::after {
        opacity: 1;
      }
      .data-amount {
        font-style: normal;
        font-weight: 500;
        font-size: 24px;
        line-height: 29px;
        color: #000000;
      }
      .data-price {
        font-size: 20px;
        color: #ff5e5e;
        margin: 10px 0;
        font-weight: 600;
      }
      .data-validity {
        font-size: 14px;
        color: #666;
      }
      .data-validity-day {
        font-size: 16px;
        font-weight: 500;
        color: #ff5e5e;
      }
      .methods-title {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #000000;
      }
      .deduction {
        background: #ffffff;
        border-radius: 2px;
        padding: 20px 10px 20px 10px;
      }
      .deduction-title {
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #000000;
        margin-bottom: 10px;
      }
      @media screen and (max-width: 599px) {
        .methods-title {
          font-style: normal;
          font-weight: 400;
          font-size: 12px;
          line-height: 17px;
          color: #999999;
          margin-top: 20px;
        }
        .methods-radio-group {
          width: 100%;
        }
        .deduction {
          padding: 20px 0px 20px 0px;
        }
        .purchase-data {
          padding: 0;
        }
        .data-plans {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 10px;
          margin: 10px 20px 20px 20px;
        }
        .data-plan {
          padding: 20px 20px;
        }
      }
    `,
  ],
})
export class PurchaseDataComponent implements AfterViewInit {
  protected readonly DeductionType = DeductionType;

  public selectedPlan: FlowDataRule | null = null;

  public selectedPlan$: BehaviorSubject<FlowDataRule> = new BehaviorSubject<FlowDataRule>(null);

  public loading = false;

  public submitted = false;

  @ViewChild("deductionComponent") deductionComponent: DeductionComponent;

  @ViewChild("paymentDetailsComponent") paymentDetailsComponent: PaymentDetailsComponent;

  protected readonly BuyType = BuyType;

  constructor(
    public flowDataService: FlowDataService,
    public serviceService: ServiceService,
    public apiManager: APIManager,
    private paymentMethodService: PaymentMethodService,
    public router: Router,
    public appService: AppService
  ) { }

  async ngAfterViewInit() {
    this.deductionComponent.setCurrentDeduction(DeductionType.balance);
    // only allow balance deduction for data purchase
    this.flowDataService.flowData$
      .pipe(
        filter((data) => data.length > 0),
        tap((data) => this.selectPlan(data[0]))
      )
      .subscribe();
  }

  selectPlan(plan: FlowDataRule) {
    this.selectedPlan = plan;
    this.selectedPlan$.next(plan);
  }

  formatDate(date: string): string {
    return date ? moment(new Date(date)).format("yyyy-MM-DD HH:mm:ss") : "";
  }

  calculateDaysFromToday(targetDate: string): number {
    if (!targetDate) {
      return 0;
    }
    const today = new Date();
    const target = new Date(targetDate);
    const diffInTime = target.getTime() - today.getTime();
    const diffInDays = Math.floor(diffInTime / (1000 * 60 * 60 * 24));
    return diffInDays < 0 ? 0 : diffInDays;
  }

  async buy() {
    this.submitted = true;
    const currentMethod = this.paymentMethodService.currentMethod$.value;
    try {
      const postRule = {
        type: "flow",
        amount: this.selectedPlan$.value.amount,
        ruleId: this.selectedPlan?.id,
        serviceId: this.serviceService.currentService$.value.id,
      };
      const invoice = await this.apiManager.createFlowInvoice(postRule).toPromise();

      if (this.deductionComponent.deductionAmount$.value > 0) {
        if (this.deductionComponent.currentDeduction$.value === DeductionType.balance) {
          await this.apiManager.deductionInvoice(invoice, this.deductionComponent.deductionAmount$.value, null, null, null, true).toPromise();
        }
      }

      const data = await this.apiManager.payInvoice(invoice, currentMethod.payType, "").toPromise();
      if (data.redirect_url !== undefined) {
        if (this.appService.isElectron() || this.appService.isExtension()) {
          window.open(data.redirect_url);
        } else {
          window.location = data.redirect_url;
        }
      }
      this.router.navigate(["payment-status"], { queryParams: { status: IN_PAYMENT, id: invoice.id } });
    } catch (e) {
      this.appService.snackUp(e?.error?.message);
      this.submitted = false;
    }
  }
}
