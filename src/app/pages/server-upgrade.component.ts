import { AfterViewInit, Component, ElementRef, Input, OnInit, ViewChild } from "@angular/core";
import { Location } from "@angular/common";
import { MatBottomSheet } from "@angular/material/bottom-sheet";
import { ActivatedRoute, NavigationEnd, Router } from "@angular/router";
import { APIManager, BillingCycles, BuyType, DeductionType, defaultBillingCycles, Product, Service, ServiceService } from "@flashvpn-io/web-core";
import { AppService } from "src/app/app.service";
import { combineLatest, interval } from "rxjs";
import { filter, flatMap, take, tap } from "rxjs/operators";
import { Rule } from "@flashvpn-io/web-core";
import { WalletsService } from "@flashvpn-io/web-core";
import { PaymentMethodService } from "@flashvpn-io/web-core";
import { PayType } from "@flashvpn-io/web-core";
import { SelectCouponComponent } from "../component/select-coupon.component";
import { SelectBalanceComponent } from "../component/select-balance.component";
import { SelectCodeComponent } from "../component/select-code.component";
import { PaymentDetailsComponent } from "../component/payment-details.component";
import { DeductionComponent } from "../component/deduction.component";
import { BuyProductsComponent } from "../component/buy-products.component";
import { BehaviorSubject } from "rxjs";
import { UpgradeProductsComponent } from "../component/upgrade-products.component";

@Component({
  selector: "server-upgrade",
  template: `
    <div class="repurchase">
      <div class="item-box">
        <app-upgrade-products #upgradeProductsComponent [currentBillingCycle]="currentBillingCycle$ | async"></app-upgrade-products>
      </div>
      <app-payment-methods></app-payment-methods>
      <app-deduction
        #deductionComponent
        [service]="serviceService.currentService$ | async"
        [product]="selectedProduct$ | async"
        [origialAmount]="origialAmount$ | async"
        [currentBillingCycle]="currentBillingCycle$ | async"></app-deduction>
      <app-payment-details
        #paymentDetailsComponent
        [buyType]="BuyType.SERVICE"
        [loading]="loading"
        [currentBillingCycle]="currentBillingCycle$ | async"
        [deductionAmount]="loading ? 0 : (deductionComponent.deductionAmount$ | async)"
        [origialAmount]="loading ? 0 : deductionComponent.getOrigialAmount()"
        [deDutiontoString]="deductionComponent.deDutiontoString()"
        [amountAfterDeduction]="loading ? 0 :  deductionComponent.getAmountAfterDeduction()"
        [submitted]="submitted"
        (buy)="buy()"></app-payment-details>
    </div>
  `,
  styles: [
    `
      .repurchase {
        width: 100%;
        max-width: 1300px;
        margin: 0 auto 250px auto;
      }
      .item-box {
        margin-top: 50px;
      }
      .deduction {
        background: #ffffff;
        border-radius: 2px;
        padding: 20px 10px 10px 10px;
      }
    `,
  ],
})
export class ServerUpgradeComponent {
  @ViewChild("upgradeProductsComponent") upgradeProductsComponent: UpgradeProductsComponent;

  @ViewChild("deductionComponent") deductionComponent: DeductionComponent;

  @ViewChild("paymentDetailsComponent") paymentDetailsComponent: PaymentDetailsComponent;

  protected readonly BuyType = BuyType;

  public loading = false;

  public submitted = false;

  public billingCycles: BillingCycles[] = defaultBillingCycles;

  public currentBillingCycle$ = new BehaviorSubject<BillingCycles>(this.billingCycles[0]);

  public selectedProduct$ = new BehaviorSubject<Product>(null);

  public origialAmount$ = new BehaviorSubject<number>(0);

  constructor(
    public router: Router,
    public serviceService: ServiceService,
    private paymentMethodService: PaymentMethodService,
    private apiManager: APIManager,
    private appService: AppService
  ) { }

  ngAfterViewInit() {
    this.serviceService.currentService$.subscribe({
      next: (service) => {
        if (service) {
          const billingCycle = this.billingCycles.find(cycle => cycle.id === service.billingcycle);
          if (billingCycle) {
            this.currentBillingCycle$.next(billingCycle);
          }
        }
      }
    });
    this.upgradeProductsComponent.selectedProduct$.subscribe({
      next: (product) => {
        if (product) {
          this.loading = true;
          if (product.id != this.serviceService.currentService$.value?.productId) {
            this.preOrder()
          }
        }
      }
    });
  }

  async preOrder() {
    const currentService = this.serviceService.currentService$.value;
    const currentProduct = this.upgradeProductsComponent.selectedProduct$.value;
    const currentBillingCycle = this.currentBillingCycle$.value;
    if (!currentService || !currentProduct) {
      this.loading = false;
      return;
    }
    const result = await this.apiManager
      .upgradeService(currentService?.id, {
        productId: currentProduct?.id,
        billingCycle: currentBillingCycle?.id,
      })
      .toPromise();
    this.origialAmount$.next(Number(result['amount']));

    this.loading = false;
  }

  async buy() {
    this.submitted = true;
    const currentMethod = this.paymentMethodService.currentMethod$.value;
    const currentService = this.serviceService.currentService$.value;
    const currentProduct = this.upgradeProductsComponent.selectedProduct$.value;
    const currentBillingCycle = this.currentBillingCycle$.value;
    const deductionAmount = this.deductionComponent?.deductionAmount$.value;
    const deductionType = this.deductionComponent?.currentDeduction$.value;
    const currentCoupon = this.deductionComponent?.selectCouponComponent?.currentCoupon?.id?.toString();
    const discountCode = this.deductionComponent?.selectCodeComponent?.discountCode;
    try {
      const result = await this.apiManager
        .upgradeService(currentService?.id, {
          productId: currentProduct?.id,
          billingCycle: currentBillingCycle?.id,
        })
        .toPromise();

      const invoice = result['invoice']
      if (deductionAmount > 0) {
        if (deductionType === DeductionType.voucher) {
          await this.apiManager.deductionInvoice(invoice, deductionAmount, currentCoupon, undefined, currentBillingCycle?.id).toPromise();
        } else if (deductionType === DeductionType.balance) {
          await this.apiManager.deductionInvoice(invoice, deductionAmount, undefined, undefined, currentBillingCycle?.id).toPromise();
        } else if (deductionType === DeductionType.discount_code) {
          await this.apiManager.deductionInvoice(invoice, deductionAmount, undefined, discountCode, currentBillingCycle?.id).toPromise();
        }
      }
      const data = await this.apiManager.payInvoice(invoice, currentMethod.payType, "").toPromise();
      if (data.redirect_url !== undefined) {
        if (this.appService.isElectron() || this.appService.isExtension()) {
          window.open(data.redirect_url);
        } else {
          window.location = data.redirect_url;
        }
      }
    } catch (e) {
      this.appService.snackUp(e?.error?.message);
    } finally {
      this.submitted = false;
    }
  }
}
