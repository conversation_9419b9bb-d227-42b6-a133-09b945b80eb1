import { AfterViewInit, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { Location } from '@angular/common';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { Router } from '@angular/router';
import { APIManager } from "@flashvpn-io/web-core";
import { AppService } from "src/app/app.service";
import { interval } from "rxjs";
import { filter, flatMap, take, tap } from "rxjs/operators";
import { Rule } from "@flashvpn-io/web-core";
import { WalletsService } from "@flashvpn-io/web-core";
import { PaymentMethodService } from "@flashvpn-io/web-core";
import { PayType } from "@flashvpn-io/web-core";
import { Utils } from '../utils/utils';
import { IN_PAYMENT } from '../services/payment.service';


@Component({
  selector: 'app-recharge-wallet',
  template: `<div class="recharge">
  <mat-progress-bar *ngIf="loading" mode="indeterminate"></mat-progress-bar>
  <div class="recharge-box">
    <div class="recharge-box-title" i18n>Balance Recharge</div>
    <div class="recharge-content" >
      <div class="recharge-content-item"  (click)="selectAmount(recharge)" [ngStyle]="{background:selectedRule.id===recharge.id?'#FF5E5E':'rgba(255, 94, 94, 0.1)'}" *ngFor="let recharge of walletService.rechargeList$ | async">
          <div class="recharge-content-item-1" [ngStyle]="{color:selectedRule.id===recharge.id?'#FFFFFF':'#000000'}" >
            <span>\${{recharge.amount}}</span>
          </div>
          <div *ngIf="recharge.gift > 0" class="recharge-content-item-2" >
            <span>送\${{recharge.gift}}</span>
          </div>
      </div>
    </div>
    <app-payment-methods [excludePayType]="[this.payType.Balance]"></app-payment-methods>
    <div class="balance-box-bottom">
      <button id="recharge-page-order-button" *ngIf="loading" mat-raised-button color="warn" class="recharge-btn" disabled i18n>Recharge Now</button>
      <button *ngIf="!loading" mat-raised-button color="warn" class="recharge-btn" (click)="recharge()" i18n>Recharge Now</button>
    </div>
  </div>
</div>
`,
  styles: [
    `
.recharge-box{
  width: 100%;
  position: relative;
}
.balance-box-bottom{
  margin: 20px 14px;
}
.recharge-box-title{
  font-family: 'Libre Franklin';
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 24px;
  margin-bottom: 16px;
}
.recharge-content{
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: left;
}
.recharge-content-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  width: 100px;
  height: 100px;
  /*padding-bottom: 30%;*/
  background: rgba(255, 94, 94, 0.1);
  border-radius: 2px;
  box-sizing: border-box;
  margin: 10px;
}
.recharge-content-item-1{
  font-family: 'Libre Franklin';
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 29px;
  color: #000000;
  /*margin-top: 26%;*/
}
.recharge-content-item-2{
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 10px;
  width: 70%;
  background: #FFC039;
  border-radius: 4px;
  font-family: 'Libre Franklin';
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  color: #FFFFFF;
  padding: 3px;
}
.select-pay-method{
  display: flex;
  align-items: center;
  padding: 8px 14px;
  gap: 8px;
  height: 67px;
  width: 30%;
  background: #F6F6F6;
  position: relative;
  margin-top: 20px;
}
.select-pay-method-box{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.select-pay-method-title{
  font-family: 'Libre Franklin';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #808080;
}
.select-pay-method-info{
  ont-family: 'Libre Franklin';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #000000;
  margin-top: 10px;
  display: flex;
  align-items: center;
}
.select-pay-method-arrow{
  position: absolute;
  right: 20px;
}
.recharge-btn{
  height: 52px;
  width: 30%;
}
.card-box{
  width: 30%;
  padding: 30px 14px;
}

@media screen and (max-width: 599px){
  .recharge-content-item {
    width: 30%;
    height: 30%;
    min-height: 105px;
    margin: 0 0 20px 0;
  }
  .recharge-content{
    justify-content: space-between;
  }
  .recharge-btn{
    width: 100%;
    height: 52px;
  }
  .select-pay-method{
    margin-top: 0;
    width: auto;
  }
  .card-box{
    width: auto;
  }
  .balance-box-bottom{
    /*position: absolute;*/
    /*bottom: 15px;*/
    margin: 20px 0px;
    width: 100%;
  }
}

    `
  ]
})
export class RechargeWalletComponent implements OnInit {
  public payType = PayType;

  public loading = false;

  public selectedRule: Rule;

  @ViewChild('cardInfo') cardInfo: ElementRef;
  card: any;
  public paymentToken: string;

  public elements: any;

  // will subscribe this if there is one
  @Input() preSubmit?: () => Promise<boolean>;

  constructor(
    private location: Location,
    private router: Router,
    private apiManager: APIManager,
    private appService: AppService,
    public walletService: WalletsService,
    public paymentMethodService: PaymentMethodService
  ) {

    this.walletService.rechargeList$.pipe(
      filter(value => value.length > 0),
      tap((value) => this.selectAmount(value[0]))
    )
      .subscribe();

  }

  /**
   * 回退
   */
  back = () => {
    this.location.back();
  }

  /**
   * 选择充值的金额
   * amount
   */
  selectAmount = (amount) => {
    this.selectedRule = { ...amount };
  }

  /**
   * 充值
   */
  recharge = async () => {
    this.createInvoices();
    // this.router.navigate(['pay-status']);
  }

  /**
   * 创建充值订单
   */
  createInvoices = () => {
    if (this.selectedRule) {
      const postRule = {
        type: 'rule',
        amount: this.selectedRule?.amount,
        ruleId: this.selectedRule?.id,
      };
      this.loading = true;
      Utils.openPayWindow();
      this.apiManager.createBalanceInvoices(postRule).subscribe(
        value => {
          if (value) {
            this.payBalanceInvoice(value['invoiceid']);
          }
        },
        res => {
          Utils.closePayWindow();
          this.appService.snackUp(res?.error?.message);
          this.loading = false;
        },
        () => this.loading = false
      );
    }
  }

  /**
   * 支付
   * invoice
   */
  payBalanceInvoice = async (invoiceid) => {
    this.loading = true;
    if (this.preSubmit !== undefined) {
      const checked = await this.preSubmit();
      if (!checked) {
        this.loading = false;
        return;
      }
    }
    this.apiManager.payBalanceInvoice(invoiceid, this.paymentMethodService.currentMethod$.value.payType, this.paymentToken)
      .subscribe(
        data => {
          if (data.redirect_url !== undefined) {
            if (this.appService.isElectron() || this.appService.isExtension()) {
              window.open(data.redirect_url);
              Utils.closePayWindow();
            } else {
              // window.location = data.redirect_url;
              Utils.sendPayUrl(data.redirect_url);
              this.router.navigate(["payment-status"], { queryParams: { status: IN_PAYMENT, id: invoiceid } });
            }
          } else {
            this.router.navigate(['wallet']);
            Utils.closePayWindow();
          }
        },
        res => {
          this.loading = false;
          // go to invoice page and continue payment
          this.router.navigate(['invoices'], { queryParams: { id: invoiceid } });
          this.appService.snackUp(res?.error?.message);
        }
      );
  }


  onChange({ error }) {
    if (error) {
      this.appService.snackUp(error.code);
    }
    this.card.detectChanges();
  }

  ngOnInit(): void {
  }

  /**
   *  Polling invoice detail
   */
  pullingInvoice(invoiceid) {
    this.loading = true;
    const dis = interval(3000)
      .pipe(take(30))
      .pipe(flatMap(() => this.apiManager.fetchInvoiceDetail(invoiceid)))
      .pipe(filter(i => i.status === 'Paid'))
      .subscribe(invoice => {
        this.loading = false;
        this.appService.snackUp('已支付');
        this.router.navigate(['pay-status']);
        // this.dialog.closeAll();
        dis.unsubscribe();
      }, res => {
        this.loading = false;
        // go to invoice page and continue payment
        this.router.navigate(['invoices'], { queryParams: { id: invoiceid } });
        this.appService.snackUp(res?.error?.message);
        // this.dialog.closeAll();
      }, () => {

        // go to invoice page and continue payment
        this.loading = false;
        this.router.navigate(['invoices'], { queryParams: { id: invoiceid } });
        this.appService.snackUp(this.appService.translate('FailedToPayInvoice'));
        // this.dialog.closeAll();
      });
  }
}
