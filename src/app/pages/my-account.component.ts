import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { WalletsService } from "@flashvpn-io/web-core";
import { AFTER_RELOAD_FUND, NotificationService } from "@flashvpn-io/web-core";
import { APIManager } from "@flashvpn-io/web-core";
import { AppService } from "src/app/app.service";
import * as moment from "moment";

@Component({
  selector: "app-my-account",
  template: `
    <div class="my-account" fxFill>
      <mat-progress-bar *ngIf="loading" mode="indeterminate"></mat-progress-bar>
      <div>
        <div class="balance-box">
          <div class="balance-box-left">
            <div class="balance-box-title" i18n>Account Balance</div>
            <div class="balance-box-amount">{{ (walletService.wallet$ | async)?.balance }}</div>
          </div>
          <div class="balance-box-right">
            <button id="wallet-page-recharge-button" mat-raised-button color="warn" (click)="linkToRecharge()" i18n>recharge</button>
          </div>
        </div>
      </div>
      <div class="details-title" i18n>Balance Details</div>
      <div class="details-box" *ngIf="(walletService.transactions$ | async).length > 0">
        <div class="details-box-item" *ngFor="let transaction of walletService.transactions$ | async">
          <div *ngIf="transaction.direction === '1'" class="details-box-item-left-top" i18n>充值</div>
          <div *ngIf="transaction.direction === '2' && !transaction.context.expired" class="details-box-item-left-top" i18n>消费</div>
          <div *ngIf="transaction.direction === '2' && transaction.context.expired" class="details-box-item-left-top">到期清零</div>
          <div *ngIf="transaction.direction === '3'" class="details-box-item-left-top">奖励</div>
          <div *ngIf="transaction.direction === '4'" class="details-box-item-left-top">到期</div>
          <div class="details-box-item-right-top">{{ formatDate(transaction.createdAt) }}</div>
          <div class="details-box-item-left-bottom">
            <div class="details-box-pay-method-1"></div>
            <div class="details-box-pay-method-2"></div>
          </div>
          <div
            class="details-box-item-right-bottom"
            [ngStyle]="{ color: transaction.direction === '1' || transaction.direction === '3' ? '#21D37E' : '#FF5D5D' }">
            {{ transaction.direction === "1" || transaction.direction === "3" ? "+$" + transaction.amount : "-$" + transaction.amount }}
          </div>
        </div>
      </div>
      <div class="details-box-null" *ngIf="(walletService.transactions$ | async).length === 0">
        <div class="details-box-null-item">
          <svg width="58" height="58" viewBox="0 0 58 58" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M28.9988 0C23.2631 0 17.6563 1.70082 12.8873 4.88738C8.11823 8.07394 4.40123 12.6031 2.20629 17.9022C0.0113444 23.2012 -0.562952 29.0322 0.556019 34.6576C1.67499 40.2831 4.43697 45.4504 8.4927 49.5061C12.5484 53.5618 17.7157 56.3238 23.3412 57.4428C28.9666 58.5617 34.7976 57.9874 40.0966 55.7925C45.3957 53.5976 49.9249 49.8806 53.1114 45.1115C56.298 40.3425 57.9988 34.7357 57.9988 29C57.9988 21.3087 54.9434 13.9325 49.5049 8.4939C44.0663 3.05535 36.6901 0 28.9988 0V0ZM28.9988 46.4C28.4252 46.4 27.8645 46.2299 27.3876 45.9113C26.9107 45.5926 26.539 45.1397 26.3195 44.6098C26.1 44.0799 26.0426 43.4968 26.1545 42.9342C26.2664 42.3717 26.5426 41.855 26.9482 41.4494C27.3538 41.0438 27.8705 40.7676 28.433 40.6557C28.9956 40.5438 29.5787 40.6013 30.1086 40.8208C30.6385 41.0402 31.0914 41.4119 31.4101 41.8888C31.7287 42.3657 31.8988 42.9264 31.8988 43.5C31.8988 44.2691 31.5933 45.0068 31.0494 45.5506C30.5055 46.0945 29.7679 46.4 28.9988 46.4V46.4ZM31.8988 37.7H26.0988V11.6H31.8988V37.7Z"
              fill="#C4C4C4" />
          </svg>
          <div style="margin-top: 10px" i18n>No records</div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .my-account {
        color: #000;
        position: relative;
        max-width: 570px !important;
        min-width: auto !important;
      }
      .balance-box {
        width: 100%;
        height: 160px;
        background-image: url("/assets/images/my-account.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        position: relative;
      }
      .balance-box-left {
        position: absolute;
        bottom: 50px;
        left: 50px;
      }
      .balance-box-right {
        position: absolute;
        bottom: 50px;
        right: 50px;
        z-index: 10;
      }
      .balance-box-title {
        font-family: "Libre Franklin";
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        line-height: 17px;
        color: rgba(255, 255, 255, 0.5);
        margin-bottom: 9px;
      }
      .balance-box-amount {
        font-family: "Libre Franklin";
        font-style: normal;
        font-weight: 500;
        font-size: 24px;
        line-height: 29px;
        color: #ffffff;
      }
      .details-title {
        font-family: "Libre Franklin";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 17px;
        color: #808080;
        margin: 24px 0;
      }
      .details-box-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        width: 100%;
        height: 108px;
        background: #f6f6f6;
        position: relative;
        margin-bottom: 20px;
      }
      .details-box-item-left-top {
        position: absolute;
        top: 16px;
        left: 14px;
        font-family: "Libre Franklin";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 17px;
        color: #000000;
      }
      .details-box-item-right-top {
        position: absolute;
        top: 16px;
        right: 14px;
        font-family: "Libre Franklin";
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 15px;
        color: #808080;
      }
      .details-box-item-left-bottom {
        position: absolute;
        bottom: 13px;
        left: 14px;
      }
      .details-box-pay-method-1 {
        font-family: "Libre Franklin";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 17px;
        color: #808080;
        margin-bottom: 5px;
      }
      .details-box-pay-method-2 {
        font-family: "Libre Franklin";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 17px;
        color: #000000;
        display: flex;
        align-items: center;
      }
      .details-box-item-right-bottom {
        position: absolute;
        bottom: 13px;
        right: 14px;
        font-family: "Libre Franklin";
        font-style: normal;
        font-weight: 500;
        font-size: 24px;
        line-height: 29px;
      }
      .details-box-null {
        position: relative;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
      .details-box-null-item {
        padding-top: 50%;
        font-family: "Libre Franklin";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 17px;
        color: #c4c4c4;
        text-align: center;
      }

      @media screen and (max-width: 599px) {
        .balance-box {
          height: 160px;
        }
        .balance-box-left {
          position: absolute;
          bottom: 16px;
          left: 14px;
        }
        .balance-box-right {
          position: absolute;
          bottom: 16px;
          right: 14px;
        }
      }
    `,
  ],
})
export class MyAccountComponent {
  public loading = false;

  constructor(
    private router: Router,
    public walletService: WalletsService,
    public notificationService: NotificationService,
    public apiManager: APIManager,
    public appService: AppService
  ) { }

  /**
   * 跳转充值页面
   */
  linkToRecharge = () => {
    this.router.navigate(["recharge"]);
  };

  formatDate = (s) => {
    if (s) {
      return moment(s).format("YYYY-MM-DD HH:mm:ss");
    }
    return "";
  };
}
