<style>
  .drawer-container {
    height: 100%;
  }

  #brand {
    font-family: Merriweather, sans-serif;
    font-style: italic;
    text-decoration-line: none;
    color: black;
  }

  /*layout side navigations*/

  #navi-container {
    height: 100%;
    display: grid;
    grid-template-columns: 0 258px 0;
    grid-template-rows: 140px auto 140px;
    grid-template-areas: ". header ." ". main . " ". footer .";
  }

  #navi-header {
    grid-area: header;
    align-self: center;
    padding-left: 16px;
  }

  #navi-body {
    grid-area: main;
    align-self: start;
    justify-self: start;
  }

  #navi-footer {
    grid-area: footer;
    padding-left: 16px;
  }

  .navi-link {
    font-family: 'Libre Franklin', sans-serif;
    font-style: normal;
    font-weight: normal;
    font-size: 24px !important;
    line-height: 29px !important;
    color: #000000;
    text-decoration-line: none;
  }

  .navi-link-active {
    color: #FF5E5E;
  }

  /*override default to have larger social icons*/

  #navi-footer .mat-icon {
    width: 33px;
    height: 33px;
  }

  .mat-drawer-container {
    background-color: white;
    z-index: unset;
    -webkit-overflow-scrolling: unset;
  }

  .override-drawer-content {
    z-index: 1;
  }

  #content-header .mat-icon {
    width: 20px;
    height: 20px;
  }

  .module-title {
    text-transform: capitalize;
    margin: 0;
  }

  .module-title-back {
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;
    color: #000000;
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  /*style drawer's content*/

  .drawer-content {
    height: 100%;
    display: grid;
    grid-template-columns: 20px auto 20px;
    grid-template-rows: 100px auto 60px;
    grid-template-areas: ". header ." ". main . " ". footer .";
  }

  #content-header {
    grid-area: header;
    align-self: center;
  }

  #content-body {
    grid-area: main;
  }

  #content-footer {
    grid-area: footer;
  }

  #content-header-home {
    display: none !important;
  }

  #content-body-home {
    grid-area: 1 /2 /4 /3;
  }

  #content-footer-home {
    display: none !important;
  }

  .user-link {
    color: #000000;
    text-decoration-line: underline;
  }

  @media screen and (max-width: 599px) {
    #content-body {
      margin-bottom: 80px;
    }

    .drawer-content {
      height: 100%;
      display: grid;
      grid-template-columns: 20px auto 20px;
      grid-template-rows: 80px auto 40px;
      grid-template-areas: ". header ." ". main . " ". footer .";
    }
  }

  #navi-body {
    width: 100%;
  }

  :host ::ng-deep #navi-body .mat-list-item {
    height: 62px;
    width: 100%;
  }

  :host ::ng-deep #navi-body .mat-list-item-content {
    padding: 0;
  }

  .navi-link {
    padding: 0 20px;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
  }

  .navi-link svg {
    fill: #000;
  }

  .navi-link-active {
    border-left: 2px solid #FF5E5E;
    background: linear-gradient(90deg, rgba(255, 94, 94, 0.2) -19.77%, rgba(255, 94, 94, 0) 34.11%);
  }

  .navi-link-active svg {
    fill: #FF5E5E;
  }

  .infiniteScroll {
    width: 100%;
    height: 100%;
  }

  .disabledScroll {
    width: 100%;
    height: 100%;
  }

  .told-events {
    position: relative;
    cursor: pointer;
    width: 25px;
  }

  .told-events-num {
    position: absolute;
    top: 0;
    left: 2px;
    height: 20px;
    width: 20px;
    border-radius: 50% 50%;
    background: #ff5e5e;
    line-height: 20px;
    text-align: center;
    font-size: 12px;
    color: #fff;
  }

  .alert-box {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 25px;
    width: 100%;
    background: red;
    color: #fff;
  }

  /* .trial-entry {
    position: fixed;
    width: fit-content;
    height: fit-content;
    z-index: 1000;
    right: 0;
    top: 360px;
  } */

  .trial-icon {
    width: 96px;
    height: 96px;
  }

  @media screen and (max-width: 599px) {
    .trial-entry {
      top: 300px;
    }
  }
</style>

<div [class]="scrollDisabled? 'disabledScroll' : 'infiniteScroll'" infiniteScroll [infiniteScrollDistance]="1"
  [infiniteScrollThrottle]="1000" [infiniteScrollDisabled]="scrollDisabled" (scrolled)="onScroll()">
  <!-- <div *ngIf="(serviceService.isTried$ | async) !== true" class="trial-entry" (click)="goTrial()">
    <img class="trial-icon" src="assets/images/trial-entry.svg" alt="" />
  </div> -->
  <div *ngIf="!isSupportBrowser" class="alert-box">请切换至系统浏览器(Safari), 以体验完整服务!</div>
  <mat-drawer-container class="drawer-container" autosize>
    <mat-drawer #drawer [opened]="appService.isLarge&&activeModule !=='' " style="z-index: 10;"
      [mode]="appService.isLarge ? 'side' : 'over'">
      <div id="navi-container">
        <div id="navi-header">
          <a id="brand" [href]="env.web">
            <h1 i18n>FlashVPN</h1>
          </a>
          <div *ngIf="usersService.user$ | async as user">
            <div class="h3Size">{{usersService.user?.email}}</div>
          </div>
          <div *ngIf="usersService.user?.emailVerified == '0'">
            <h3 class="action-button" (click)="verify()" i18n>Verify Email</h3>
            <mat-progress-bar *ngIf="submitted" mode="indeterminate"></mat-progress-bar>
          </div>
        </div>
        <mat-list id="navi-body">
          <mat-list-item *ngIf="(serviceService.isTried$ | async) !== true">
            <a id="navi-link-trial" class="navi-link" routerLink="trial" routerLinkActive="navi-link-active">
              <svg style="margin-right: 10px;" width="20" height="20" viewBox="0 0 20 20" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M2 0C0.895431 0 0 0.895431 0 2V18C0 19.1046 0.895431 20 2 20H18C19.1046 20 20 19.1046 20 18V2C20 0.895431 19.1046 0 18 0H2ZM7 6C7 6.74028 6.5978 7.38663 6 7.73244V8C6 10.2091 7.79086 12 10 12C12.2091 12 14 10.2091 14 8V7.73244C13.4022 7.38663 13 6.74028 13 6C13 4.89543 13.8954 4 15 4C16.1046 4 17 4.89543 17 6C17 6.74028 16.5978 7.38663 16 7.73244V8C16 11.3137 13.3137 14 10 14C6.68629 14 4 11.3137 4 8V7.73244C3.4022 7.38663 3 6.74028 3 6C3 4.89543 3.89543 4 5 4C6.10457 4 7 4.89543 7 6Z" />
              </svg>
              <span>试用</span>
              <svg style="margin-left: 10px;" width="24" height="24" viewBox="0 0 24 24" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M12.1669 23.9988C6.49875 23.9988 1.9976 20.1044 2.00927 14.556C1.97791 14.0043 2.02622 13.4509 2.15264 12.9139C2.3727 11.9026 2.90283 9.92453 3.33128 8.38187C3.57634 7.49741 5.17508 7.10489 6.07865 7.35343C7.31396 7.69624 8.06749 6.61467 8.32255 5.38911C8.60167 3.97996 9.08555 2.62205 9.75792 1.36104C11.3417 -1.54088 14.134 0.701126 16.0595 3.39564C16.849 4.44171 17.7548 5.38918 18.7586 6.21872C22.7596 10.3462 23.298 17.6001 19.012 21.4534C17.1091 23.1328 14.6743 24.0382 12.1669 23.9988Z"
                  fill="#FF5E5E" />
                <path d="M5 12.098H5.763V14.205H8.381V12.098H9.144V17.096H8.381V14.856H5.763V17.096H5V12.098Z"
                  fill="white" />
                <path
                  d="M12.339 12C13.095 12 13.697 12.245 14.138 12.742C14.558 13.211 14.768 13.834 14.768 14.604C14.768 15.367 14.558 15.983 14.138 16.459C13.697 16.949 13.095 17.194 12.339 17.194C11.576 17.194 10.974 16.942 10.54 16.452C10.12 15.976 9.91695 15.36 9.91695 14.604C9.91695 13.841 10.12 13.225 10.54 12.749C10.974 12.245 11.576 12 12.339 12ZM12.339 12.672C11.807 12.672 11.394 12.847 11.1 13.211C10.82 13.554 10.68 14.016 10.68 14.604C10.68 15.185 10.82 15.647 11.1 15.99C11.394 16.34 11.807 16.522 12.339 16.522C12.871 16.522 13.284 16.347 13.571 16.004C13.851 15.668 13.998 15.199 13.998 14.604C13.998 14.002 13.851 13.533 13.571 13.19C13.284 12.84 12.871 12.672 12.339 12.672Z"
                  fill="white" />
                <path d="M15.156 12.098H19.251V12.749H17.585V17.096H16.829V12.749H15.156V12.098Z" fill="white" />
              </svg>
            </a>
          </mat-list-item>
          <mat-list-item *ngIf="usersService.user$ | async as user">
            <a id="navi-link-service" class="navi-link" routerLink="center" routerLinkActive="navi-link-active">
              <svg style="margin-right: 10px;" width="20" height="20" viewBox="0 0 20 20" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <rect y="6" width="6" height="11" rx="2" fill="black" />
                <rect x="7" width="6" height="17" rx="2" fill="black" />
                <rect x="14" y="3" width="6" height="14" rx="2" fill="black" />
                <rect y="18" width="20" height="2" rx="1" fill="black" />
              </svg>

              <span i18n>个人中心</span>
            </a>
          </mat-list-item>
          <mat-list-item *ngIf="usersService.user$ | async as user">
            <a id="navi-link-myservices" class="navi-link" routerLink="myservices" routerLinkActive="navi-link-active">
              <svg style="margin-right: 10px;" width="20" height="20" viewBox="0 0 20 20" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M2 0C0.895431 0 0 0.895431 0 2V18C0 19.1046 0.895431 20 2 20H18C19.1046 20 20 19.1046 20 18V2C20 0.895431 19.1046 0 18 0H2ZM7 6C7 6.74028 6.5978 7.38663 6 7.73244V8C6 10.2091 7.79086 12 10 12C12.2091 12 14 10.2091 14 8V7.73244C13.4022 7.38663 13 6.74028 13 6C13 4.89543 13.8954 4 15 4C16.1046 4 17 4.89543 17 6C17 6.74028 16.5978 7.38663 16 7.73244V8C16 11.3137 13.3137 14 10 14C6.68629 14 4 11.3137 4 8V7.73244C3.4022 7.38663 3 6.74028 3 6C3 4.89543 3.89543 4 5 4C6.10457 4 7 4.89543 7 6Z" />
              </svg>
              <span i18n>我的服务</span>
            </a>
          </mat-list-item>
          <mat-list-item *ngIf="(usersService.user$ | async) === null">
            <a id="navi-link-service" class="navi-link" routerLink="services" routerLinkActive="navi-link-active">
              <svg style="margin-right: 10px;" width="20" height="20" viewBox="0 0 20 20" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M2 0C0.895431 0 0 0.895431 0 2V18C0 19.1046 0.895431 20 2 20H18C19.1046 20 20 19.1046 20 18V2C20 0.895431 19.1046 0 18 0H2ZM7 6C7 6.74028 6.5978 7.38663 6 7.73244V8C6 10.2091 7.79086 12 10 12C12.2091 12 14 10.2091 14 8V7.73244C13.4022 7.38663 13 6.74028 13 6C13 4.89543 13.8954 4 15 4C16.1046 4 17 4.89543 17 6C17 6.74028 16.5978 7.38663 16 7.73244V8C16 11.3137 13.3137 14 10 14C6.68629 14 4 11.3137 4 8V7.73244C3.4022 7.38663 3 6.74028 3 6C3 4.89543 3.89543 4 5 4C6.10457 4 7 4.89543 7 6Z" />
              </svg>
              <span i18n>Services</span>
            </a>
          </mat-list-item>
          <mat-list-item *ngIf="usersService.user$ | async as user">
            <a id="navi-link-devices" class="navi-link" routerLink="devices" routerLinkActive="navi-link-active">
              <svg style="margin-right: 10px;" width="20" height="20" viewBox="0 0 20 20" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M4 2C2.89543 2 2 2.89543 2 4V16C2 17.1046 2.89543 18 4 18H16C17.1046 18 18 17.1046 18 16V4C18 2.89543 17.1046 2 16 2H4ZM0 4C0 1.79086 1.79086 0 4 0H16C18.2091 0 20 1.79086 20 4V16C20 18.2091 18.2091 20 16 20H4C1.79086 20 0 18.2091 0 16V4ZM5 9C5 6.79086 6.79086 5 9 5H11C13.2091 5 15 6.79086 15 9V11C15 13.2091 13.2091 15 11 15H9C6.79086 15 5 13.2091 5 11V9Z" />
              </svg>
              <span i18n>设备管理</span>
            </a>
          </mat-list-item>
          <mat-list-item *ngIf="usersService.user$ | async as user">
            <a id="navi-link-wallet" class="navi-link" routerLink="wallet" routerLinkActive="navi-link-active">
              <svg style="margin-right: 10px;" width="20" height="20" viewBox="0 0 20 20" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M14 2C13.6028 2 13.2327 2.11577 12.9215 2.3154C12.6118 0.988424 11.4213 0 10 0C9.00066 0 8.11544 0.488635 7.57024 1.24002C7.2458 1.08611 6.88296 1 6.5 1C5.11929 1 4 2.11929 4 3.5C4 4.84873 5.06804 5.94801 6.40449 5.99821C4.2167 7.88383 2 10.8145 2 13.5C2 17.9183 5.58172 20 10 20C14.4183 20 18 17.9183 18 13.5C18 10.7893 15.7415 7.8288 13.5339 5.94541C13.6835 5.9811 13.8395 6 14 6C15.1046 6 16 5.10457 16 4C16 2.89543 15.1046 2 14 2ZM13.4141 5.71988C13.5689 5.9486 13.5089 6.25945 13.2802 6.41418C12.2441 7.11508 11.145 7.50005 9.99999 7.50005C8.85501 7.50005 7.75587 7.11508 6.71982 6.41418C6.4911 6.25945 6.43112 5.94861 6.58585 5.71988C6.74059 5.49116 7.05143 5.43118 7.28015 5.58592C8.18277 6.19654 9.09018 6.50005 9.99999 6.50005C10.9098 6.50005 11.8172 6.19654 12.7198 5.58592C12.9485 5.43118 13.2594 5.49116 13.4141 5.71988ZM10.5003 9H9.50035V17.6349H10.5003V9ZM8.03553 10.0419C8.31617 9.84318 8.63714 9.70098 9.00017 9.61354V10.6746C8.94937 10.6942 8.90196 10.716 8.85787 10.7401C8.5533 10.8941 8.41117 11.1508 8.41117 11.5C8.41117 11.808 8.5736 12.0647 8.91878 12.2495C8.94054 12.2613 8.96768 12.2745 9.00017 12.2891V13.4275C8.52025 13.262 8.20432 13.1363 8.05584 13.0505C7.49746 12.7116 7.22335 12.2187 7.22335 11.5718C7.22335 10.9249 7.48731 10.4115 8.03553 10.0419ZM11.0002 14.0921V12.9797C11.5125 13.1599 11.8633 13.3089 12.0558 13.4201C12.6853 13.8001 13 14.3237 13 15.0014C13 15.6586 12.7462 16.1823 12.2386 16.5622C11.9127 16.7995 11.499 16.9649 11.0002 17.0529V15.9493C11.1282 15.9098 11.2435 15.8606 11.3452 15.8024C11.6497 15.6175 11.8122 15.3814 11.8122 15.0733C11.8122 14.6831 11.5787 14.3751 11.132 14.1492C11.1023 14.1341 11.0584 14.1151 11.0002 14.0921ZM9.00017 15.8838V17.0213C8.60461 16.935 8.26896 16.7956 7.99492 16.6033C7.38579 16.172 7.05076 15.4943 7 14.5599H8.17766C8.25888 15.1144 8.45178 15.5149 8.7665 15.751C8.8354 15.8008 8.91324 15.8451 9.00017 15.8838ZM11.0002 9.61406V10.7736C11.036 10.795 11.0698 10.8181 11.1015 10.8428C11.3655 11.0379 11.5584 11.3665 11.6599 11.8183H12.8376C12.7665 11.0174 12.4721 10.4218 11.9645 10.0419C11.7012 9.84357 11.3792 9.70155 11.0002 9.61406Z" />
              </svg>
              <div class="wallet-container">
                <span i18n>Wallet</span>
                <!--            <span class="recharge-tag" i18n>SAVE 20%</span>-->
              </div>
            </a>
          </mat-list-item>
          <mat-list-item *ngIf="(usersService.user$ | async) === null">
            <a id="navi-link-sign-in" class="navi-link" routerLink="users/signin" routerLinkActive="navi-link-active">
              <svg style="margin-right: 10px;" width="20" height="16" viewBox="0 0 20 16" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M20 0H0V16H5.70175L4.73684 13.1556H2.80702V2.84444H17.193V13.1556H15.2632L14.2982 16H20V0ZM9.99995 5.09091L6.07137 9.09091H8.46268V12.7273H11.3664V9.09091H13.9285L9.99995 5.09091ZM12.4999 14.5455V16H7.49995V14.5455H12.4999Z" />
              </svg>
              <span i18n>Sign In</span>
            </a>
          </mat-list-item>
          <mat-list-item *ngIf="(usersService.user$ | async) === null">
            <a id="navi-link-sign-up" class="navi-link" routerLink="users/signup" routerLinkActive="navi-link-active">
              <svg width="20" style="margin-right: 10px;" height="20" viewBox="0 0 20 20" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M9.31622 15.4002C9.31622 13.2855 10.3836 11.4324 11.9949 10.3248C12.1761 10.0428 12.3374 9.74057 12.4988 9.41807C12.4988 9.3978 12.5393 9.37778 12.5594 9.37778C13.1233 9.29722 13.667 8.67272 13.8484 7.84704C14.0498 6.92054 13.8081 6.0746 13.2845 5.73232C13.2644 5.7123 13.2442 5.67178 13.2442 5.65176C13.2644 3.89958 12.8413 2.52997 11.9954 1.54316C10.8877 0.27438 9.37701 0.0124207 8.30963 0.0124207C7.22199 0.0124207 5.73153 0.27438 4.66415 1.50288C3.79819 2.48993 3.39536 3.87956 3.41538 5.61172C3.41538 5.65152 3.3956 5.67154 3.37581 5.69156L3.3751 5.69228C2.83116 6.03456 2.60972 6.90052 2.81114 7.807C2.99229 8.63268 3.51597 9.25718 4.10019 9.33775C4.12021 9.33775 4.14047 9.35801 4.16049 9.37803C4.56332 10.3248 5.12704 11.1102 5.79183 11.6741C5.81185 11.6741 5.81185 11.6942 5.81185 11.6942C6.03328 11.9361 6.03328 12.3187 6.03328 12.3187C6.02322 12.3288 6.02832 12.5982 6.03335 12.8638C6.03833 13.1269 6.04324 13.3863 6.03328 13.3863C5.63045 13.4266 4.42197 13.5877 3.27402 13.9905C2.52867 14.2522 1.90441 14.5745 1.40099 14.937C0.756221 15.4204 0.333372 15.9644 0.131958 16.5886C0.111694 16.6692 0.0916748 16.7498 0.0916748 16.8101V17.7366C0.0916748 18.1594 0.393675 18.4817 0.776484 18.4817H10.1419C9.61846 17.5754 9.31622 16.5281 9.31622 15.4002ZM10.767 15.4002C10.767 17.938 12.8214 20.0124 15.3793 20.0124C17.9371 20.0124 20.0116 17.958 19.9915 15.4002C19.9915 12.8624 17.9171 10.7879 15.3793 10.7879C12.8414 10.7879 10.767 12.8423 10.767 15.4002ZM16.0441 14.7556H17.3128C17.6754 14.7556 17.9574 15.0376 17.9574 15.4002C17.9574 15.7627 17.6754 16.0447 17.3128 16.0447H16.0441V17.3337C16.0441 17.6963 15.7621 17.9783 15.3995 17.9783C15.037 17.9783 14.755 17.6963 14.755 17.3337V16.0447H13.4659C13.1034 16.0447 12.8214 15.7627 12.8214 15.4002C12.8214 15.0376 13.1034 14.7556 13.4659 14.7556H14.755V13.4866C14.755 13.1241 15.037 12.8421 15.3995 12.8421C15.7621 12.8421 16.0441 13.1241 16.0441 13.4866V14.7556Z" />
              </svg>
              <span i18n>Sign Up</span>
            </a>
          </mat-list-item>
          <mat-list-item *ngIf="(usersService.user$ | async) !== null"> <a id="navi-link-welfare" class="navi-link"
              routerLink="welfare" routerLinkActive="navi-link-active">
              <svg style="margin-right: 10px;" width="20" height="20" viewBox="0 0 20 20" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M14.9434 0.784787C15.7619 1.60325 15.9313 2.85424 15.4931 4H18C19.1046 4 20 4.89543 20 6V7C20 8.10457 19.1046 9 18 9H11V7C11 6.44772 10.5523 6 10 6C9.44771 6 9 6.44772 9 7V9H2C0.895431 9 0 8.10457 0 7V6C0 4.89543 0.895431 4 2 4H5.16144C4.84349 3.06711 4.99117 2.08027 5.64009 1.43134C6.6164 0.455033 8.3576 0.613324 9.52918 1.7849C9.59043 1.84615 9.64892 1.90897 9.70462 1.97315C9.87545 1.67741 10.0899 1.39563 10.3472 1.13834C11.7141 -0.228495 13.7718 -0.386786 14.9434 0.784787ZM2 10H9V18C9 18.5523 9.44771 19 10 19C10.5523 19 11 18.5523 11 18V10H18V18C18 19.1046 17.1046 20 16 20H4C2.89543 20 2 19.1046 2 18V10Z" />
              </svg>
              <span i18n>Welfare Center</span>
            </a> </mat-list-item>
          <mat-list-item *ngIf="!appService.isElectron() && !appService.isExtension()">
            <a id="navi-link-apps" class="navi-link" target="_blank" href="http://{{deliveryIPs[0]}}:9992">
              <svg style="margin-right: 10px" width="20" height="20" viewBox="0 0 20 20" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20ZM7.04103 5.99C6.35371 6.60371 6 7.33956 6 7.95263C6 8.50492 5.55228 8.95263 5 8.95263C4.44772 8.95263 4 8.50492 4 7.95263C4 6.61307 4.72963 5.3726 5.70897 4.49816C6.69811 3.61496 8.05318 3 9.5 3C10.9404 3 12.2965 3.59831 13.291 4.48631C14.2798 5.36918 15 6.62301 15 8C15 9.58547 13.7658 10.6206 12.8249 11.2137C12.318 11.5333 11.7956 11.7879 11.3667 11.9827C11.1467 12.0827 10.9733 12.158 10.8335 12.2186L10.8334 12.2186L10.8333 12.2187C10.6934 12.2794 10.5873 12.3255 10.502 12.367C10.5007 12.4053 10.5 12.4493 10.5 12.5C10.5 13.0523 10.0523 13.5 9.5 13.5C8.94772 13.5 8.5 13.0523 8.5 12.5C8.5 12.1054 8.52248 11.627 8.77679 11.2288C9.01957 10.8487 9.42616 10.6639 9.52396 10.6194L9.52397 10.6194L9.52398 10.6194C9.53289 10.6154 9.53923 10.6125 9.54265 10.6107C9.68039 10.5399 9.90496 10.4416 10.1379 10.3397L10.138 10.3397L10.138 10.3396L10.138 10.3396C10.2742 10.28 10.4133 10.2192 10.5395 10.1618C10.9336 9.9828 11.3626 9.77132 11.7584 9.52181C12.6231 8.97673 13 8.46189 13 8C13 7.32962 12.6369 6.58345 11.959 5.97816C11.2868 5.37801 10.3929 5 9.5 5C8.61349 5 7.71856 5.38504 7.04103 5.99ZM11 15.5C11 16.3284 10.3284 17 9.5 17C8.67157 17 8 16.3284 8 15.5C8 14.6716 8.67157 14 9.5 14C10.3284 14 11 14.6716 11 15.5Z" />
              </svg>
              <span i18n>Help Center</span>
            </a>
          </mat-list-item>
          <mat-list-item *ngIf="(usersService.user$ | async) !== null">
            <a id="navi-link-settings" class="navi-link" routerLink="settings" routerLinkActive="navi-link-active">
              <svg style="margin-right: 10px;" width="20" height="20" viewBox="0 0 20 20" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_8352_10437)">
                  <path
                    d="M9.90553 14.3286C10.7613 14.3286 11.5979 14.0739 12.3095 13.5968C13.021 13.1196 13.5756 12.4415 13.9031 11.6481C14.2306 10.8546 14.3163 9.98157 14.1494 9.13926C13.9824 8.29696 13.5703 7.52326 12.9652 6.916C12.36 6.30873 11.589 5.89518 10.7497 5.72763C9.91033 5.56009 9.04032 5.64608 8.24967 5.97473C7.45901 6.30338 6.78323 6.85993 6.30778 7.57399C5.83232 8.28806 5.57855 9.12758 5.57855 9.98638C5.58212 11.1369 6.03915 12.2393 6.84984 13.0528C7.66054 13.8663 8.75904 14.325 9.90553 14.3286ZM-0.199811 9.98638C-0.20091 9.26911 -0.132771 8.55341 0.00365266 7.84932C0.607678 7.78258 1.1868 7.57085 1.69206 7.23204C2.19731 6.89323 2.61383 6.43731 2.9064 5.90282C3.21821 5.34292 3.3942 4.71718 3.42012 4.0763C3.44605 3.43543 3.32117 2.79743 3.0556 2.21401C4.12867 1.16826 5.42192 0.377498 6.84002 -0.100006C7.1755 0.422583 7.63626 0.852365 8.18012 1.14999C8.72397 1.44761 9.33354 1.60356 9.95301 1.60356C10.5725 1.60356 11.182 1.44761 11.7259 1.14999C12.2698 0.852365 12.7305 0.422583 13.066 -0.100006C14.4815 0.387712 15.7701 1.18775 16.8368 2.24123C16.5798 2.82313 16.462 3.45726 16.4926 4.093C16.5232 4.72875 16.7015 5.34851 17.0132 5.90282C17.2987 6.41429 17.6983 6.85249 18.1807 7.1831C18.6631 7.51371 19.2152 7.72776 19.7938 7.80848C20.0939 9.34865 20.0662 10.9351 19.7125 12.4637C19.1425 12.5597 18.6019 12.7849 18.1318 13.1222C17.6616 13.4595 17.2744 13.9 16.9996 14.4102C16.7181 14.9162 16.5471 15.4764 16.498 16.0537C16.4489 16.6311 16.5228 17.2123 16.7148 17.7588C15.6168 18.8262 14.2897 19.6271 12.8354 20.1C12.4904 19.656 12.049 19.2968 11.5448 19.0497C11.0407 18.8026 10.487 18.6742 9.92588 18.6742C9.3648 18.6742 8.81111 18.8026 8.30693 19.0497C7.80275 19.2968 7.36136 19.656 7.01635 20.1C5.56668 19.6439 4.23987 18.8618 3.13699 17.8132C3.34528 17.2571 3.43013 16.662 3.38561 16.0695C3.3411 15.4771 3.1683 14.9015 2.87927 14.383C2.59614 13.8563 2.193 13.4042 1.7027 13.0636C1.2124 12.723 0.648818 12.5034 0.0579096 12.4229C-0.117495 11.6228 -0.20393 10.8056 -0.199811 9.98638Z" />
                </g>
                <defs>
                  <clipPath id="clip0_8352_10437">
                    <rect width="20" height="20" fill="white" />
                  </clipPath>
                </defs>
              </svg>
              <span i18n>Settings</span>
            </a>
          </mat-list-item>
          <mat-list-item *ngIf="(usersService.user$ | async) !== null">
            <a id="navi-link-sign-out" class="navi-link" style="cursor: pointer" (click)="logout()"
              routerLinkActive="navi-link-active">
              <svg style="margin-right: 10px" width="20" height="20" viewBox="0 0 20 20" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M10 10C12.7614 10 15 7.76142 15 5C15 2.23858 12.7614 0 10 0C7.23858 0 5 2.23858 5 5C5 7.76142 7.23858 10 10 10ZM4.50468 10.2499C5.17863 9.59403 6.22344 9.75648 7.04115 10.2209C7.91431 10.7168 8.92408 11 10 11C11.0759 11 12.0857 10.7168 12.9588 10.2209C13.7766 9.75648 14.8214 9.59403 15.4953 10.2499C17.0377 11.7507 18 13.8764 18 16.2334C18 20.1416 15.3543 20.0687 11.8057 19.9711L11.8056 19.9711C11.2253 19.9551 10.6209 19.9385 10 19.9385C9.37908 19.9385 8.77469 19.9551 8.19444 19.9711L8.19432 19.9711C4.6457 20.0687 2 20.1416 2 16.2334C2 13.8764 2.96233 11.7507 4.50468 10.2499Z"
                  fill="black" />
              </svg>
              <span i18n>Logout</span>
            </a>
          </mat-list-item>
        </mat-list>
      </div>
      <div id="mat-drawer-mask"
        style="display:none; overflow: hidden; z-index: 1; width: 259px; height: 100%; position: fixed; top: 0px; left: 0px; background: rgb(0, 0, 0); opacity: 0.6;">
      </div>
    </mat-drawer>
    <mat-drawer-content class="override-drawer-content">
      <div class="drawer-content">
        <div [id]="activeModule !== ''? 'content-header': 'content-header-home'">
          <div class="mobile-header">
            <div *ngIf="!showBack" (click)="drawer.toggle()" class="mobile-header-left">
              <mat-icon svgIcon="bars">
                <svg width="100%" height="100%" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg"
                  fit="" preserveAspectRatio="xMidYMid meet" focusable="false">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M0 2V0H20V2H0ZM0 7V9H20V7H0ZM0 14V16H20V14H0Z"
                    fill="black"></path>
                </svg>
              </mat-icon>
            </div>
            <h2 class="module-title" *ngIf="!showBack">
              {{activeModule=='helps'?'帮助中心':activeModule=='trial'?'试用':activeModule}}
            </h2>
            <div *ngIf="showBack" (click)="goBack()" class="mobile-header-left">
              <svg width="8" height="15" viewBox="0 0 8 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M8 2.01325L6.8 0.833252L1.19333 6.35992L0 7.53325L1.2 8.71992L6.73333 14.1666L7.92 12.9933L2.39333 7.54659L8 2.01325Z"
                  fill="black" />
              </svg>
              <h2 class="module-title">{{activeModule}}</h2>
            </div>
            <div class="told-events" routerLink="/inbox/message">
              <div *ngIf="currentPath.indexOf('/article')<0 && (usersService.user$ | async) !== null">
                <img style="width: 25px;" src="assets/images/told.png" />
                <ng-container *ngIf="fcmService.unreadNum$ | async as unreadNum">
                  <div class="told-events-num" *ngIf="unreadNum > 0">{{unreadNum}}</div>
                </ng-container>
              </div>
            </div>
          </div>
          <div class="desktop-header">
            <h2 class="module-title">{{activeModule=='helps'?'帮助中心':activeModule=='trial'?'试用':activeModule}}</h2>
            <div style="display: flex">
              <div class="module-title-back" *ngIf="showBack" (click)="goBack()">
                <svg width="8" height="15" viewBox="0 0 8 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M8 2.01325L6.8 0.833252L1.19333 6.35992L0 7.53325L1.2 8.71992L6.73333 14.1666L7.92 12.9933L2.39333 7.54659L8 2.01325Z"
                    fill="black" />
                </svg>
                <span style="margin-left: 10px">{{'返回前页'}}</span>
              </div>
              <div class="told-events" style="padding-right:20px;" (click)="handleShowToldDialog()"
                *ngIf="currentPath.indexOf('/article')<0 && (usersService.user$ | async) !== null">
                <img style="width: 25px;" src="assets/images/told.png" />
                <ng-container *ngIf="fcmService.unreadNum$ | async as unreadNum">
                  <div class="told-events-num" *ngIf="unreadNum > 0">{{unreadNum}}</div>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
        <div [id]="activeModule !== ''? 'content-body': 'content-body-home'">
          <router-outlet></router-outlet>
        </div>
        <div [id]="activeModule !== ''? 'content-footer': 'content-footer-home'" fxLayout="row"
          fxLayoutAlign="start center" fxLayoutAlign.gt-md="center center">
          <h4>© FlashVPN Network 2020</h4>
        </div>
      </div>
    </mat-drawer-content>
  </mat-drawer-container>
</div>