import { Inject, Injectable, LOCALE_ID, OnInit } from "@angular/core";
import { MatSnackBar } from "@angular/material/snack-bar";
import * as _ from "lodash";
import { BreakpointObserver, Breakpoints } from "@angular/cdk/layout";
import { initializeApp } from "firebase/app";
import { getMessaging, getToken, onMessage, isSupported } from "firebase/messaging";
import { UsersService } from "@flashvpn-io/web-core";

declare var chrome;
/**
 * Global utilises go here
 */

@Injectable({
  providedIn: "root",
})
export class AppService implements OnInit {
  isLarge = true;
  private messaging;

  constructor(
    @Inject(LOCALE_ID) public localeId: string,
    private snackBar: MatSnackBar,
    public userService: UsersService,
    private breakpointObserver: BreakpointObserver
  ) {
    // Responsive watcher
    this.breakpointObserver.observe([Breakpoints.Medium, Breakpoints.Large, Breakpoints.XLarge]).subscribe((x) => (this.isLarge = x.matches));
    // todo fix firebase
    // try {
    //   const firebaseApp = initializeApp(this.config.firebase);
    //   isSupported().then((result) => {
    //     if (result) {
    //       this.messaging = getMessaging(firebaseApp);
    //       onMessage(this.messaging, (payload) => {
    //         console.log("Message received. ", payload);
    //         this.snackUp(payload.notification.title);
    //       });
    //     } else {
    //       console.log(`This browser doesn't support the firebase API`);
    //     }
    //   });
    // } catch (e) {
    //   console.log(`Got error when init the firebase`);
    // }
  }
  ngOnInit(): void { }

  getToken(): Promise<string | void> {
    // return getToken(this.messaging, { vapidKey: this.config.vapidKey })
    //   .then((currentToken) => {
    //     if (currentToken) {
    //       console.log(currentToken);
    //       return currentToken;
    //     } else {
    //       console.log("No registration token available. Request permission to generate one.");
    //       return "no permission";
    //     }
    //   })
    //   .catch((err) => {
    //     console.log("An error occurred while retrieving token. ", err);
    //     return "err";
    //   });
    return Promise.resolve(undefined);
  }

  /**
   * Display a snack bar at the top and stay 2000ms, no further interaction
   * @param message the simple message to show
   */
  snackUp(message: string) {
    this.snackBar.open(message, "Okay", {
      duration: 4000,
      verticalPosition: "top",
    });
  }

  /**
   *  If run inside chrome extension
   */
  isExtension(): boolean {
    if (typeof chrome === "undefined") {
      return false;
    }

    if (typeof chrome.proxy === "undefined") {
      return false;
    }
    return true;
  }

  /**
   * If run inside chrome
   */
  isChrome(): boolean {
    return typeof chrome !== "undefined";
  }

  /**
   * If run inside iOS
   */
  isIOS(): boolean {
    const userAgent = navigator.userAgent || navigator.vendor;
    // iOS detection from: http://stackoverflow.com/a/9039885/177710
    return /iPad|iPhone|iPod/.test(userAgent) && !(window as any).MSStream;
  }

  /**
   * If run inside android
   */
  isAndroid(): boolean {
    const userAgent = navigator.userAgent || navigator.vendor;
    return /android/i.test(userAgent);
  }

  /**
   * If run inside electron
   */
  isElectron(): boolean {
    return window.process !== undefined;
  }

  /**
   * if we are redirected from an app, i.e ios,android,macos,win and chrome
   * @param app the name of our app
   */
  async fromApp(app: string) {
    if (!_.includes(["ios", "android", "macos", "windows", "chrome"], app)) {
      return;
    }
    let appSetting = await localStorage.getItem("app");
    if (appSetting === null) {
      appSetting = JSON.stringify([app]);
    } else {
      const old = JSON.parse(appSetting);
      old.push(app);
      appSetting = JSON.stringify(_.uniq(old));
    }
    await localStorage.setItem("app", appSetting);
  }

  /**
   * Check if we are redirect from app
   * @return An array of app name
   */
  async isFromApp(): Promise<string[]> {
    const appSetting = await localStorage.getItem("app");
    return appSetting !== null ? JSON.parse(appSetting) : [];
  }

  copyMessage(val: string) {
    const selBox = document.createElement("textarea");
    selBox.style.position = "fixed";
    selBox.style.left = "0";
    selBox.style.top = "0";
    selBox.style.opacity = "0";
    selBox.value = val;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand("copy");
    document.body.removeChild(selBox);
    this.snackUp($localize`:@@copied:Copied`);
  }
  /**
   * log in chrome extension
   */
  loginToChrome() {
    window.postMessage({ type: "LOGIN", email: this.userService.user.email, token: this.userService.user.token }, "*");
    this.snackUp($localize`:@@chrome-login-hint:Login successfully, please reopen our chrome extension again`);
  }

  async setCurrentService(id) {
    localStorage.setItem("currentService", `${id}`);
  }

  guidesURL() {
    const hostname = window.location.hostname;
    const parts = hostname.split(".");

    if (parts.length > 2) {
      // Handle cases like 'subdomain.example.co.uk'
      return "https://guides." + parts.slice(-2).join(".");
    } else {
      // Handle cases like 'example.com'
      return "https://guides." + hostname;
    }
  }

  /**
   * translate
   */
  translate(term: string): string {
    switch (term) {
      case "users":
        return $localize`:@@module-users:users`;
      case "services":
        return '我的服务';
      case "myservices":
        return '我的服务';
      case "repurchase":
        return '购买服务';
      case "billings":
        return $localize`:@@module-billings:billings`;
      case "products":
        return $localize`:@@module-products:products`;
      case "invoices":
        return $localize`:@@module-invoices:invoices`;
      case "plans":
        return $localize`:@@module-plans:plans`;
      case "settings":
        return $localize`:@@module-settings:settings`;
      case "rewards":
        return $localize`:@@module-rewards:rewards`;
      case "transactions":
        return $localize`:@@module-transactions:transactions`;
      case "payment-status":
        return $localize`:@@module-payment-status:Payment Status`;
      case "register-buy":
        return $localize`:@@module-register-buy:Confirm Purchase Bill`;
      case "payment-methods":
        return $localize`:@@module-payment-methods:Buy Service`;
      case "purchase-data":
        return $localize`:@@module-purchase-data:购买流量包`;
      case 'wallet':
        return $localize`:@@module-wallet:wallet`;
      case "recharge":
        return $localize`:@@module-recharge:recharge`;
      case "wake":
        return $localize`:@@module-wake:wake`;
      case "welfare":
        return $localize`:@@module-welfare:Welfare`;
      case "invite-record":
        return $localize`:@@module-invite-record:Invitation reward record`;
      case "my-voucher":
        return $localize`:@@module-my-voucher:My Voucher`;
      case "select-voucher":
        return $localize`:@@module-select-voucher:Select Voucher`;
      case "tg":
        return $localize`:@@module-telegram:telegram`;
      case "apps":
        return "APPs";
      case "monthly":
        return $localize`:@@billing-monthly:Monthly`;
      case "quarterly":
        return $localize`:@@billing-quarterly:Quarterly`;
      case "semiannually":
        return $localize`:@@billing-semiannually:Semi-Annually`;
      case "annually":
        return $localize`:@@billing-annually:Annually`;
      case "biennially":
        return $localize`:@@billing-biennially:Biennially`;
      case "or":
        return $localize`:@@misc-or:or`;
      case "Unpaid":
        return $localize`:@@invoices-properties-status-unpaid:Unpaid`;
      case "Paid":
        return $localize`:@@invoices-properties-status-paid:Paid`;
      case "on":
        return $localize`:@@misc-on:on`;
      case "off":
        return $localize`:@@misc-off:off`;

      // here start the errors, either local or remote ones
      case "EmailDomainNotAllowed":
        return $localize`:@@EmailDomainNotAllowed:This domain is not allowed, please contact our CS`;
      case "EmailNotVerified":
        return $localize`:@@EmailNotVerified:or`;
      case "CredentialIsWrong":
        return $localize`:@@CredentialIsWrong:Your email or password is wrong`;
      case "EmailExisted":
        return $localize`:@@EmailExisted:You have signed up before, please sign in`;
      case "WrongVerificationCode":
        return $localize`:@@WrongVerificationCode:The verification code is wrong`;
      case "UserNotSignedUpYet":
        return $localize`:@@UserNotSignedUpYet:You have not signed up yet`;
      case "ConfirmedPasswordNotMatch":
        return $localize`:@@ConfirmedPasswordNotMatch:The second inputted password does not match the first`;
      case "ServiceExpired":
        return $localize`:@@ServiceExpired:Your service is expired, please reactivate it`;
      case "ReactivatingService":
        return $localize`:@@ReactivatingService:We are reactivating your service`;
      case "ReloadingService":
        return $localize`:@@ReloadingService:We are reloading your services`;
      case "FailedToLoadInvoice":
        return $localize`:@@FailedToLoadInvoice:Failed to load the invoice, make sure you have signed in.`;
      case "FailedToPayInvoice":
        return $localize`:@@FailedToPayInvoice:Payment failed, please try again`;
      case "InsufficientFunds":
        return $localize`:@@InsufficientFunds:You do not have enough funds`;
      case "WalletNotFound":
        return $localize`:@@WalletNotFound:The wallet address cannot be found`;
      case "invite":
        return $localize`:@@invite:Invite`;
      case "DowngradeInfo":
        // tslint:disable-next-line:max-line-length
        return $localize`:@@DowngradeInfo:We do not support downgrade at the moment, if you really need it, please click on the icon in the lower right corner to contact customer service`;
      case "NoNeedToUpgrade":
        return $localize`:@@NoNeedToUpgrade:No need to upgrade`;
      case "SuccessfulDowngrade":
        return $localize`:@@SuccessfulDowngrade:Successful downgrade!`;
      case "SuccessfulUpgrade":
        return $localize`:@@SuccessfulUpgrade:Successful upgrade!`;
      case "InvoiceCreated":
        // tslint:disable-next-line:max-line-length
        return $localize`:@@InvoiceCreated:The invoice has been created successfully, and it is being redirected to the invoice details page...`;
      case "ExistUnpaidInvoices":
        return $localize`:@@ExistUnpaidInvoices:You have unpaid bills, please update after payment!`;
      case "UpdateCycleError":
        return $localize`:@@UpdateCycleError:Billing cycle update failed`;
      case "UpdateCycleSuccess":
        return $localize`:@@UpdateCycleSuccess:Billing cycle update successfully`;
      case "BilledMonthly":
        return $localize`:@@BilledMonthly:Billed Monthly`;
      case "BilledQuarterly":
        return $localize`:@@BilledQuarterly:Billed Quarterly`;
      case "BilledSemiAnnually":
        return $localize`:@@BilledSemiAnnually:Billed Semi-Annually`;
      case "BilledAnnually":
        return $localize`:@@BilledAnnually:Billed Annually`;
      case "BilledBiennially":
        return $localize`:@@BilledBiennially:Billed Biennially`;
      case "Processing":
        return $localize`:@@Processing:Processing, please wait...`;
      case "NoServiceTip":
        return $localize`:@@NoServiceTip:You don't have the service yet, you can change the plan after purchasing`;
      case "TurnOnVpn":
        return $localize`:@@TurnOnVpn:VPN is enabled`;
      case "TurnOffVpn":
        return $localize`:@@TurnOffVpn:VPN is off`;
      case "SwitchTo":
        return $localize`:@@SwitchTo:Switch to`;
      case "TokenExpired":
        return $localize`:@@TokenExpired:Session expired, please login again`;
      case "InvoiceExpired":
        return $localize`:@@InvoiceExpired:Your invoice has expired and has been regenerated for you, please pay as soon as possible`;
      case "EmailIllegalCharacters":
        return $localize`:@@EmailIllegalCharacters:Email name contains illegal characters`;
      case "PasswordIllegalCharacters":
        return $localize`:@@PasswordIllegalCharacters:Password contains illegal characters`;
      case "EmailFormatError":
        return $localize`:@@EmailFormatError:Email format error`;
      case "EmailLongError":
        return $localize`:@@EmailLongError:Email address exceeds 40 characters`;
      case "PasswordLongError":
        return $localize`:@@PasswordLongError:Password length exceeds 20 characters`;
      case "PasswordShortError":
        return $localize`:@@PasswordShortError:Password length is less than 8 characters`;
      case "EmailcheckError":
        return $localize`:@@EmailcheckError:Email verification failed, please try again`;
      case "upgrade":
        return $localize`:@@upgrade:升级套餐`;
      case "center":
        return $localize`:@@Center:个人中心`;
      default:
        return term;
    }
  }
}
