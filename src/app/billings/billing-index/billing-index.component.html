<style>
  .billing {
    color: #000;
    font-size: 12px;
    line-height: 15px;
  }

  p {
    margin: 0;
  }

  li {
    list-style: none;
  }

  ul {
    margin: 0;
    padding: 0;
  }

  .btn {
    border-radius: 0;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    padding: 8px 20px;
    color: #fff;

  }

  .gray {
    color: #808080;
  }

  .red {
    color: #FF5E5E;
  }

  .red-bg {
    background: #FF5E5E;
  }

  .subscription {
    width: 320px;
  }

  .subscription li {
    margin-bottom: 18px;
  }

  .label {
    width: 120px;
    display: inline-block;
  }

  .deadline {
    width: 102px;
    display: inline-block;
  }

  .operator {
    width: 56px;
    display: inline-block;
    font-weight: bold;
    text-align: right;
    cursor: pointer;
    color: #FF5E5E;
  }

  .open {
    width: 320px;
  }

  p {
    margin-bottom: 17px;
  }

  h6 {
    font-size: 18px;
    line-height: 22px;
    margin: 20px 0;
  }

  .invoice-list {
    width: 100%;
    flex-wrap: wrap;
  }

  .invoice-list a {
    width: 320px;
    cursor: pointer;
    /* margin-right: 47px; */
    margin-bottom: 20px;
    color: #000;
  }

  @media screen and (max-width: 599px) {
    .invoice-list li {
      margin-right: 0;
    }
  }

  .invoice-list li span {
    font-size: 14px;
  }

  .description {
    width: 100%;
    margin: 0 10px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .view-btn {
    min-width: 30px;
  }

  .margin-top-fix {
    margin-top: 10px;
  }

  /* .invoice-box{
    width: 320px;
    margin-bottom: 20px;
  }
  @media screen and(max-width: 599px) {
    .invoice-box{
      width: 100%;
    }
  } */

</style>
<div class="billing" fxFill>
  <!-- <div>
    <p style="width: 330px;word-wrap:break-word;margin-bottom: 30px;">
      Your current service is set to  <span class="red">50 HKD</span> per month, and it will be renewed on <span class="red">May 23rd, 2020.</span>
    </p>
  </div> -->
  <mat-progress-bar *ngIf="loading" mode="indeterminate"></mat-progress-bar>
  <div *ngIf="!loading">
    <div *ngIf="service.usage">Out of <span class="highlighted">{{service?.usageCapInGB()}}GB
    </span>monthly data, you have used <span class="highlighted">{{service?.used()}}%</span></div>
    <div style="margin-right: 10px;margin-bottom: 20px;" fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="start center">
      <div *ngIf="service">It expires in <span
        class="highlighted">{{service?.daysTillDue()?.toFixed(0)}} </span> days</div>
      <a class="action-button margin-top-fix" (click)="fetchLatestInvoice()" i18n>Renew Here</a>
    </div>
    <div *ngIf="service">
      <ul class="subscription">
        <li>
          <span class="label" i18n>Subscription</span>
          <span class="deadline gray">{{service.name}}</span>
          <a class="operator" routerLink="/plans" i18n>Upgrade</a>
        </li>
        <li>
          <span class="label" i18n>Billing Interval</span>
          <span class="deadline gray">{{appService.translate(service.billingcycle)}}</span>
          <span class="operator" [matMenuTriggerFor]="menu" i18n>Update</span>
          <mat-menu #menu="matMenu">
            <button id="update-billing-cycle" mat-menu-item *ngFor="let billingCycle of billingCycles" (click)="updateBillingCycle(billingCycle.key)">
              <span>{{appService.translate(billingCycle.value)}}</span>
            </button>
          </mat-menu>
        </li>
        <mat-divider style="margin-top: 30px"></mat-divider>
      </ul>
    </div>
    <!-- <div class="open">
      <p>Your billing is managed by Apple’s in app purchase, please to to iOS to mange your billing settings</p>
      <button class="btn red-bg" mat-raised-button>Open in iOS </button>
      <mat-divider style="margin-top: 30px"></mat-divider>
    </div> -->
    <div class="invoice-box">
      <h6 i18n>
        Invoices
      </h6>
      <ul class="invoice-list" fxLayout="row" fxLayoutAlign="start center">
        <a *ngFor="let invoice of invoices" routerLink="/invoices/{{invoice.id}}" fxLayout="row"
            fxLayoutAlign="space-between center">
          <span style="width: 28px;font-size: 12px;" class="gray">#{{invoice.id}}</span>
          <span class="description">{{invoice.description}}</span>
          <span class="view-btn" i18n>View</span>
        </a>
      </ul>
    </div>
    <!-- <div *ngIf="invoices" >
      <div fxFill fxLayout="row wrap" fxLayoutGap="10px" >
        <div class="invoice-box" *ngFor="let invoice of invoices" routerLink="/invoices" [queryParams]="{id: invoice.id}">
          <span>#{{invoice.id}}</span>
          <h4 style="font-weight: bold;">{{invoice?.serviceId}} - {{invoice.description}}</h4>
          <div i18n>You are invoiced for above service in total {{invoice.total}} HKD</div>
          <h4 i18n>It dues in {{invoice.duedate | date:'dd/MM/yyyy'}}</h4>
          <span class="action-button" *ngIf="invoice.status ==='Unpaid'" routerLink="/invoices" [queryParams]="{id: invoice.id}" i18n>Pay Now</span>
          <h4 *ngIf="invoice.datepaid">You paid the in {{invoice.datepaid}}</h4>
          <h4></h4>
          <mat-divider></mat-divider>
        </div>
      </div>
    </div> -->
  </div>
</div>
