import { Component, Input, OnInit } from "@angular/core";
import { APIManager } from "@flashvpn-io/web-core";
import * as _ from "lodash";
import { AppService } from "../../app.service";
import { DialogsService } from "../../utils/dialogs/dialogs.service";
import { Router } from "@angular/router";
import { Service } from "@flashvpn-io/web-core";
import { Invoice } from "@flashvpn-io/web-core";
import { ServiceService } from "@flashvpn-io/web-core";

@Component({
  selector: "app-billing-index",
  templateUrl: "./billing-index.component.html",
  styleUrls: ["./billing-index.component.css"],
})
export class BillingIndexComponent implements OnInit {
  public billingCycles = [
    { key: "monthly", value: "BilledMonthly" },
    { key: "quarterly", value: "BilledQuarterly" },
    { key: "semiannually", value: "BilledSemiAnnually" },
    { key: "annually", value: "BilledAnnually" },
    { key: "biennially", value: "BilledBiennially" },
  ];

  @Input() service: Service;
  public invoice: Invoice;
  public invoices: Invoice[];
  public loading = false;
  constructor(
    private apiManager: APIManager,
    public appService: AppService,
    private serviceService: ServiceService,
    public dialogService: DialogsService,
    private router: Router
  ) { }

  async ngOnInit() {
    this.loading = true;
    const services = await this.apiManager.fetchServices().toPromise();
    if (!(services && services.length > 0) || services[0]?.status === "Pending") {
      // await this.router.navigate(["services", "nan"]);
      await this.router.navigate(["center"]);
    }
    // reload service
    // if ((await Service.validServices()).length === 0 ) {
    //   await this.apiManager.fetchServices().toPromise();
    //   if ((await Service.validServices()).length === 0 ) {
    //     await this.router.navigate(['services', 'nan']);
    //   }
    // }
    this.service = await this.serviceService.currentService$.getValue();
    await this.fetchData();
  }
  async fetchData() {
    this.apiManager.fetchInvoices().subscribe((invoices) => {
      this.invoices = _.orderBy(invoices, ["id"], ["desc"]).filter(
        (i) => (i.serviceId !== "0" && !Invoice.fromData(i).isDue()) || i.serviceId === "0"
      );
      this.loading = false;
    });
  }

  async fetchLatestInvoice() {
    this.apiManager.fetchLatestInvoice(this.service).subscribe((invoice: Invoice) => {
      if (this.service.status === "Suspended") {
        this.apiManager.regenerateInvoice(invoice).subscribe((newInvoice: Invoice) => {
          this.router.navigateByUrl(`invoices?id=${newInvoice.id}`);
        });
      } else {
        this.router.navigateByUrl(`invoices?id=${invoice.id}`);
      }
    });
  }

  updateBillingCycle(billingCycle: string) {
    this.dialogService.openDialog("update-confirm-dialog", {
      billingCycle,
      service: this.service,
    });

    this.dialogService.dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (this.service.billingcycle === result) {
          this.appService.snackUp(this.appService.translate("NoNeedToUpgrade"));
        } else {
          this.apiManager.updateBillingCycle(this.service.id, { billingCycle: result }).subscribe(async (res: any) => {
            if (res.hasOwnProperty("error")) {
              if (res.message.match("upgrade")) {
                this.appService.snackUp(this.appService.translate("ExistUnpaidInvoices"));
              } else {
                this.appService.snackUp(this.appService.translate("UpdateCycleError"));
              }
            } else {
              this.appService.snackUp(this.appService.translate("UpdateCycleSuccess"));
            }
            this.service = await this.serviceService.currentService$.getValue();
            this.fetchData();
          });
        }
      }
    });
  }
}
