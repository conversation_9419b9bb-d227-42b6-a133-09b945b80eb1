import { AfterViewInit, Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Payment } from './payments';
import { Invoice } from '@flashvpn-io/web-core';
import { interval, Subject } from 'rxjs';
import { APIManager } from '@flashvpn-io/web-core';
import { ActivatedRoute, ParamMap, Router } from "@angular/router";
import { filter, flatMap, map, take } from "rxjs/operators";
import { MatDialog } from "@angular/material/dialog";
import { DialogsService } from '../../utils/dialogs/dialogs.service';
import { IN_PAYMENT, PAYMENT_FAILURE } from '../../services/payment.service';
import { Service } from '@flashvpn-io/web-core';
import { Product } from '@flashvpn-io/web-core';
import { AppService } from 'src/app/app.service';


@Component({
  selector: 'app-payment',
  templateUrl: './payment.component.html',
  styleUrls: ['./payment.component.css']
})
export class PaymentComponent implements OnInit, OnDestroy, AfterViewInit {
  payments: Payment[] = [
    { name: 'Alipay', method: 'alipay' },
    { name: 'Alipay', method: 'alipay' },
    // {name: 'Wechat', method: 'wechat'},
    { name: 'Credit Card', method: 'creditcard' }]
    ;
  paymentSelected: Payment = this.payments[0];

  // will subscribe this if there is one
  @Input() preSubmit?: () => Promise<boolean>;
  @Input() service?: Service;
  @Input() product?: Product;
  @Input() invoice?: Invoice;
  @Input() promoCode?: string;
  @Input() pulling: boolean;
  public submitted: boolean;

  public elements: any;
  public useWechat = false;

  @ViewChild('cardInfo') cardInfo: ElementRef;
  card: any;
  public paymentToken: string;

  constructor(
    public apiManager: APIManager,
    public router: Router,
    public dialog: MatDialog,
    public route: ActivatedRoute,
    public appService: AppService,
    public dialogsService: DialogsService,
  ) {
  }

  ngOnInit() {
    this.route.queryParamMap.subscribe(
      async (params: ParamMap) => {
        const id = params.get('id');
        if (id !== null) {
          this.invoice = await this.apiManager.fetchInvoiceDetail(Number(id)).toPromise();
        }
        const useWechat = params.get('wechat');
        if (useWechat === 'yes') {
          this.useWechat = true;
        }
      });
  }

  async ngAfterViewInit() {
    if (this.pulling) {
      this.pullingInvoice();
    }
  }

  ngOnDestroy() {
    this.card?.removeEventListener('change', this.onChange.bind(this));
    this.card?.destroy();
  }

  onChange({ error }) {
    if (error) {
      this.appService.snackUp(error.code);
    }
    this.card.detectChanges();
  }

  async submit(paymentMethod: string) {
    this.submitted = true;
    if (this.preSubmit !== undefined) {
      const checked = await this.preSubmit();
      if (!checked) {
        this.submitted = false;
        return;
      }
    }

    this.submitted = true;
    this.apiManager.payInvoice(this.invoice, paymentMethod, this.paymentToken)
      .subscribe(
        data => {
          if (data.redirect_url !== undefined) {
            if (this.appService.isElectron() || this.appService.isExtension()) {
              window.open(data.redirect_url);
            } else {
              window.location = data.redirect_url;
            }
          }
          this.router.navigate(['payment-status'], { queryParams: { status: IN_PAYMENT, id: this.invoice.id } });
        },
        res => {
          this.submitted = false;
          // go to invoice page and continue payment
          // this.router.navigate(['invoices'], {queryParams: {id: this.invoice.id}});
          this.router.navigate(['payment-status'], { queryParams: { status: PAYMENT_FAILURE } });
          this.appService.snackUp(res?.error?.message);
        }
      );
  }

  /**
   *  Polling invoice detail
   */
  pullingInvoice() {
    this.submitted = true;
    const dis = interval(3000)
      .pipe(take(30))
      .pipe(map(i => this.submitted = true))
      .pipe(flatMap(() => this.apiManager.fetchInvoiceDetail(this.invoice.id)))
      .pipe(filter(i => i.status === 'Paid'))
      .subscribe(invoice => {
        this.submitted = false;
        this.appService.snackUp('已支付');
        this.router.navigate(['billings', 'payment-success'], { queryParams: { serviceId: invoice.serviceId } });
        this.dialog.closeAll();
        dis.unsubscribe();
      }, res => {
        this.submitted = false;
        // go to invoice page and continue payment
        this.router.navigate(['invoices'], { queryParams: { id: this.invoice.id } });
        this.appService.snackUp(res?.error?.message);
        this.dialog.closeAll();
      }, () => {

        // go to invoice page and continue payment
        this.submitted = false;
        this.router.navigate(['invoices'], { queryParams: { id: this.invoice.id } });
        this.appService.snackUp(this.appService.translate('FailedToPayInvoice'));
        this.dialog.closeAll();
      });
  }

  async deduction() {
    this.dialogsService.openDialog('deduction-dialog', { invoice: this.invoice });
  }
}

