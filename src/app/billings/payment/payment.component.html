<style>
  .paymentBox{
    padding: 5px;
    min-width: 320px;
  }


  .paymentSelectedBox{
    border: 1px solid #FF5E5E!important;
  }

  .StripeElement {
    box-sizing: border-box;

    height: 40px;

    padding: 10px 12px;

    border: 1px solid transparent;
    border-radius: 4px;
    background-color: white;

    box-shadow: rgba(84, 70, 35, 0.15) 0 2px 8px, rgba(84, 70, 35, 0.15) 0 1px 3px;
    -webkit-transition: box-shadow 150ms ease;
    transition: box-shadow 150ms ease;
  }

  .StripeElement--focus {
    box-shadow: 0 1px 3px 0 #cfd7df;
  }

  .StripeElement--invalid {
    border-color: #fa755a;
  }

  .StripeElement--webkit-autofill {
    background-color: #fefde5 !important;
  }
  #cardInfo {
    width: 100%;
  }
</style>
<div fxFlex.gt-sm="50" fxFlex="100">
  <div fxLayout.gt-sm="row" fxLayout="column" fxLayoutGap="10px">
    <div fxFlex="40" class="paymentBox" *ngIf="service" fxLayout="column" fxLayoutAlign="space-between start">
      <div>
        <div>{{service?.name}}</div>
        <div *ngIf="promoCode | validatePromoCode" fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5px">
          <h2>{{product?.calculateSaved(service?.billingcycle).price | discount:promoCode}} <span i18n>HKD</span></h2>
          <div>/</div>
          <h3 style="text-decoration: line-through" > {{product?.calculateSaved(service?.billingcycle).price}} <span i18n>HKD</span></h3>
        </div>
        <div *ngIf="!promoCode || !(promoCode | validatePromoCode)">
          <h2>{{product?.calculateSaved(service?.billingcycle).price}} <span i18n>HKD</span></h2>
        </div>
        <div i18n>Billed {{appService.translate(service?.billingcycle)}}</div>
        <div><span i18n>Saved</span><span class="highlighted" style="margin: 0 2px;">{{product?.calculateSaved(service?.billingcycle).pricePaidByMonth.minus(product.calculateSaved(service?.billingcycle).price)}}</span><span i18n>HKD</span></div>
      </div>
      <div>
        <mat-form-field class="form-input">
          <input id="promoCodeInput" matInput i18n-placeholder placeholder="Have a Promo Code?"  required  [(ngModel)]="promoCode" name="email">
        </mat-form-field>
        <div *ngIf="promoCode && !(promoCode | validatePromoCode)" i18n>This promotion code is wrong, please contact CS.</div>
      </div>
      <div style="margin-top: 10px"><span i18n>By continuing the payment , you agree to our</span><a href="static/tos.html" target="_blank" i18n>Terms of Service</a> <span i18n> and </span> <a href="static/privacy.html" target="_blank" i18n>Privacy Policy</a>.</div>
    </div>
    <div *ngIf="!submitted" fxFlex="40" class="paymentBox">
      <h4 i18n>Deduction By Balance</h4>
      <div fxLayoutGap="10px">
        <a id="payment-deduction" *ngIf="invoice?.serviceId>0" class="callout-button" (click)="deduction()" i18n>Deduction</a>
      </div>
      <h4 i18n>Pay quickly by</h4>
      <div fxLayoutGap="10px">
        <a id="payment-alipay" (click)="submit('alipay')"  class="callout-button" i18n>AliPay</a>
        <a id="payment-alipay-or-wechat" *ngIf="useWechat" (click)="submit('wellpay')"  class="callout-button" i18n>AliPay/Wechat</a>
        <a id="payment-balance" *ngIf="invoice?.serviceId>0" (click)="submit('balance')"  class="callout-button" i18n>Balance</a>
      </div>
      <div *ngIf="!appService.isExtension()">
        <app-divider></app-divider>
        <h4 i18n>Pay by card</h4>
        <div  style="margin-bottom: 10px" id="cardInfo" #cardInfo></div>
        <h4></h4>
        <a id="payBtn" (click)="submit('creditcard')"><span class="callout-button" i18n>Pay Now</span></a>
      </div>
    </div>
  </div>
  <mat-progress-bar *ngIf="submitted" mode="indeterminate"></mat-progress-bar>
</div>
