.invoice-content {
  margin: 0 0px 250px 30px;
}
tr td:first-child {
	min-width: 50px;
}

.methods {
  padding: 30px 0px 0px;
}

.methods-title {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #000000;
}

.methods-radio-group {
  display: flex;
  flex-direction: column;
  margin: 20px 0;
}

.methods-radio-button {
  margin: 10px;
}

.methods-radio-option {
  display: flex;
  align-items: center;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 16px;
  color: #000000;
}

.mobile-methods {
  display: none;
}

.select-pay-method {
  display: flex;
  align-items: center;
  padding: 8px 14px;
  gap: 8px;
  height: 67px;
  background: #FFFFFF;
  position: relative;
  margin-top: 20px;
  width: auto;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
}

.select-pay-method-box {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.select-pay-method-title {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #808080;
}

.select-pay-method-info {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #000000;
  margin-top: 10px;
  display: flex;
  align-items: center;
}

.select-pay-method-arrow {
  position: absolute;
  right: 20px;
}
.footer {
  position: absolute;
  bottom: 0;
  height: 260px;
  left: 0;
  right: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
  border-radius: 20px;
}


.deduction {
  background: #FFFFFF;
  border-radius: 2px;
  padding: 10px;
}

.deduction-title {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 16px;
  color: #000000;
}

.deduction-box {
  display: inline;
  width: 100%;
  margin-bottom: 30px;
  margin-top: 15px;
}

.deduction-top-box{
  color: #808080;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
}

.deduction-left-box {
  width: 100%;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  color: #808080;
}

.deduction-right-box {
  width: 100%;
  text-align: center;
}

.example-full-width {
  width: 384px;
}


.voucher-box,.voucher-box-disabled{
  width: 344px;
  height: 55px;
  border-radius: 4px;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.12);
  margin: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.voucher-box{
  background: #FFF;
  cursor: pointer;
}

.voucher-box-disabled{
  background: #F6F6F6;
  cursor: not-allowed;
}

.voucher-right,.voucher-left{
  display: flex;
  align-items: center;
}

.voucher-title{
  color: #808080;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  margin-left: 20px;
}

.voucher-amount{
  color: #FF5E5E;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 12px;
  margin-right: 20px;
}

.deduction-bottom-box{
  display: flex;
  align-items: center;
}

.deduction-code-btn{
  margin-left: 30px;
  margin-bottom: 1.34375em;
}

.deduction-code-msg{
  color: #3FE07F;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.34375em;
  margin-left: 30px;
}
.operation-btn {
  width: 120px;
  height: 50px;
  font-size: 16px;
  margin-right: 8%;
  margin-top: 20px;
}

@media screen and (max-width: 599px) {

  .footer {
    position: fixed;
  }
  .invoice-content {
    margin: 0 0 250px 0;
  }
  .voucher-box,.voucher-box-disabled{
    width: auto;
  }
  .deduction {
    padding: 0;
    margin-top: 0;
  }
  .footer {
    height: auto;
    border-radius: 0;
  }
  .example-full-width{
    width: 100%;
  }
  .deduction-bottom-box{
    display: block;
  }
  .deduction-code-msg{
    margin-bottom: 0;
    margin-left: 0;
    margin-top: 15px;
    justify-content:flex-start;
  }
  .deduction-code-btn{
    margin-left: 0;
    margin-bottom: 0;
    width: 50%;
  }
}
