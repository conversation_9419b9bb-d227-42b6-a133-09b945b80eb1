import { Component, ElementRef, Input, OnInit, ViewChild, AfterViewInit } from "@angular/core";
import { BillingCycles, BuyType, DeductionType, Invoice, PaymentMethodService, PayType, ServiceService } from "@flashvpn-io/web-core";
import { APIManager } from "@flashvpn-io/web-core";
import { ActivatedRoute, Router } from "@angular/router";
import { MatSnackBar } from "@angular/material/snack-bar";
import { AppService } from "../../app.service";
import { UsersService } from "@flashvpn-io/web-core";
import * as moment from "moment";
import { AFTER_INVOICE_DEDUCT, AFTER_USER_PAID, NotificationService } from "@flashvpn-io/web-core";
import { ToPayComponent } from "../../services/to-pay/to-pay.component";
import { MobilePaymethodComponent } from "../../component/mobile-paymethod.component";
import { MatBottomSheet } from "@angular/material/bottom-sheet";
import { IN_PAYMENT, PaymentService } from "../../services/payment.service";
import { Service } from "@flashvpn-io/web-core";
import { Product } from "@flashvpn-io/web-core";
import { Reward, RewardStatus } from "@flashvpn-io/web-core";
import { SelectCodeComponent } from "src/app/component/select-code.component";
import { SelectCouponComponent } from "src/app/component/select-coupon.component";
import { SelectBalanceComponent } from "src/app/component/select-balance.component";

@Component({
  selector: "app-invoices",
  templateUrl: "./invoices.component.html",
  styleUrls: ["./invoices.component.css"],
})
export class InvoicesComponent implements OnInit {
  private _pendingDiscountCode: string;

  protected readonly BuyType = BuyType;

  protected readonly payType = PayType;

  public readonly deductionType = DeductionType;

  @ViewChild("selectCouponComponent") selectCouponComponent: SelectCouponComponent;
  @ViewChild("selectBalanceComponent") selectBalanceComponent: SelectBalanceComponent;
  @ViewChild("selectCodeComponent") selectCodeComponent: SelectCodeComponent;

  @Input() service?: Service;
  @Input() product?: Product;
  @Input() invoice?: Invoice;
  submitted = false;
  pulling = false;
  invoiceId;
  deductionAmount = 0;
  currentDeduction: string;
  buyType: number;
  currentMethod: string;
  loading = false;
  payInfo = {};
  timer = undefined;
  time = 5;
  public currentBillingCycle: BillingCycles;
  public payMethodList = [
    {
      payType: "wechat",
      imgUrl: "assets/images/products/wechat.svg",
      payName: "微信支付",
    },
    {
      payType: "alipay",
      imgUrl: "assets/images/products/alipay.svg",
      payName: "支付宝",
    },
    {
      payType: "balance",
      imgUrl: "assets/images/products/balance.svg",
      payName: "余额支付",
    },
    // {
    //   payType: 'deduction',
    //   imgUrl: 'assets/images/products/balance.svg',
    //   payName: 'Deduction',
    // },
  ];
  public card: any;
  public payMethod;
  public currentVoucher: Reward;
  public discountCode;
  productList = []
  public disableVerifyBtn = false;

  constructor(
    public apiManager: APIManager,
    public router: Router,
    public route: ActivatedRoute,
    public appService: AppService,
    public usersService: UsersService,
    public snackBar: MatSnackBar,
    private paymentService: PaymentService,
    public notificationService: NotificationService,
    private bottomSheet: MatBottomSheet,
    private paymentMethodService: PaymentMethodService,
    private serviceService: ServiceService
  ) {
    this.notificationService.register(AFTER_INVOICE_DEDUCT).subscribe((_) => {
      this.load(this.invoiceId, null);
    });
  }

  ngOnInit() {
    this.loading = true;
    this.serviceService.products$.subscribe((products) => {
      if (products && products.length > 0) {
        this.productList = products
      }
    });
    this.route.params.subscribe(async (params) => {
      const { id, pulling, payStatus } = params;

      if (id !== undefined) {
        this.invoiceId = id;
        if (pulling !== undefined) {
          this.pulling = true;
        }
        if (payStatus === "paid") {
          await this.loadingInvoiceStatus(Number(id), payStatus);
        } else {
          await this.load(Number(id), payStatus);
        }
      }
    });
    this.route.queryParamMap.subscribe(async (params) => {
      const buyType = Number(params.get("buyType"));
      const currentDeduction = params.get("currentDeduction");
      const discountCode = params.get("discountCode");

      if (buyType) {
        this.buyType = buyType;
      }

      if (currentDeduction) {
        this.currentDeduction = currentDeduction;
      }

      if (discountCode) {
        this.discountCode = discountCode;
      }

      const id = Number(params.get("id"));
      const payStatus = params.get("payStatus");
      if (id !== undefined && id > 0) {
        this.invoiceId = id;
        const pulling = params.get("pulling");
        if (pulling !== null) {
          this.pulling = true;
        }
        await this.load(id, payStatus);
      }
    });
  }

  async loadingInvoiceStatus(invoiceId, status) {
    this.timer = setInterval(() => {
      if (this.time > 0) {
        this.time--;
        this.load(invoiceId, status);
      } else {
        clearInterval(this.timer);
      }
    }, 2000);
  }

  async load(id: number, payStatus: string) {
    this.submitted = true;
    this.apiManager.fetchInvoiceDetail(id).subscribe(
      (value) => {
        this.invoice = value;
        this.currentBillingCycle = {
          id: "",
          title: "",
          unit: "",
          description: this.invoice.description,
        };
        this.updateInvoiceDescription()
        this.payInfo["description"] = this.invoice.description;
        this.payInfo["amount"] = this.invoice.total;
        if (this.invoice.status === "Unpaid" && !value.description.match("Upgrade")) {
          if (moment(value.duedate).format("YYYY-MM-DD") < moment().utc().format("YYYY-MM-DD")) {
            this.apiManager.regenerateInvoice(value).subscribe((invoice) => {
              this.appService.snackUp(this.appService.translate("InvoiceExpired"));
              this.invoice = invoice;
              this.router.navigate(["invoices"], { queryParams: { id: invoice.id } });
            });
          }
          const invoiceStartDate = value?.description
            ?.match(/\d{1,2}\/\d{1,2}\/\d{4}/g)[0]
            ?.split("/")
            .reverse()
            .join("/");
          if (value.serviceStatus === "Suspended" && moment(invoiceStartDate).format("YYYY-MM-DD") < moment().utc().format("YYYY-MM-DD")) {
            this.apiManager.regenerateInvoice(value, true).subscribe((invoice) => {
              this.appService.snackUp(this.appService.translate("InvoiceExpired"));
              this.invoice = invoice;
              this.router.navigate(["invoices"], { queryParams: { id: invoice.id } });
            });
          }
        }

        if (payStatus && payStatus === "paid" && !this.appService.isExtension()) {
          clearInterval(this.timer);
          this.notificationService.post(AFTER_USER_PAID);
          this.postGtm(value);
        }
        if (this.invoice && this.invoice.items && this.invoice.items.length > 1) {
          const deductionItems = this.invoice.items.filter((i) => i.description.includes("Deduction"));
          if (deductionItems.length > 0) {
            switch (deductionItems[0].description.split(" ")[0]) {
              case "Coupon":
                this.currentDeduction = DeductionType.voucher;
                break;
              case "Code":
                this.currentDeduction = DeductionType.discount_code;
                const notes = JSON.parse(deductionItems[0]['notes']);
                this._pendingDiscountCode = notes.code;
                setTimeout(() => {
                  if (this.selectCodeComponent) {
                    this.selectCodeComponent.discountCode = this._pendingDiscountCode;
                    this.disableVerifyBtn = true;
                  }
                });
                break;
              default:
                this.currentDeduction = DeductionType.balance;
            }
          }
        }
        this.getDeductionInfo();
        this.calculateDeductionAmount();
        this.getDeductionInfo();
        this.submitted = false;
        this.loading = false;
      },
      (error) => {
        this.snackBar.open(this.appService.translate("FailedToPayInvoice"), "Okay", {
          duration: 4000,
          verticalPosition: "top",
        });
      },
      () => (this.submitted = false)
    );
  }

  getDeductionInfo() {
    switch (this.currentDeduction) {
      case this.deductionType.voucher:
        this.getVoucher();
        break;
      case this.deductionType.discount_code:

      case this.deductionType.balance:
        let notes = JSON.parse(this.invoice["notes"].replace(/&quot;/g, '"'));
        this.deductionAmount = notes["points"];
        break;
    }
  }

  methodOnChange(event) {
    this.currentMethod = event?.value;
    if ("balance" === event?.value && this.currentDeduction === this.deductionType.balance) {
      // this.currentDeduction = undefined;
      // this.deductionAmount = undefined;
    }
  }

  onChange({ error }) {
    if (error) {
      this.appService.snackUp(error.message);
    }
    if (this.card?.detectChanges()) {
      this.card?.detectChanges();
    }
    this.submitted = false;
  }

  openSelectPayMethod = () => {
    this.bottomSheet.open(MobilePaymethodComponent, { data: this.payMethodList });
  };

  // Send the data GTM
  postGtm = (invoice: Invoice) => {
    const dataLayer = (window as any).dataLayer;

    function gtag(...arg) {
      dataLayer.push(arguments);
    }

    gtag("event", "purchase", {
      transaction_id: invoice?.id,
      value: invoice?.total,
      currency: "HKD",
      items: [
        {
          item_id: invoice?.id,
          item_name: invoice?.description,
          currency: "HKD",
          price: invoice?.total,
        },
      ],
    });
  };

  getVoucher() {
    this.apiManager.getRewardsByUser().subscribe(
      (rewards) => {
        if (rewards) {
          let usedVoucher = rewards.filter((r) => r.status === RewardStatus.USED);
          this.selectCouponComponent.currentCoupon = usedVoucher.filter((r) => r.invoiceId === this.invoiceId).pop();
          if (this.selectCouponComponent.currentCoupon) {
            this.deductionAmount = this.selectCouponComponent.currentCoupon?.award;
          }
        }
      },
      (res) => {
        this.appService.snackUp(res?.error?.message);
      }
    );
  }

  async goToMyService() {
    // await this.router.navigate(["services"]);
    await this.router.navigate(["center"]);
  }

  getOrigialAmount(): number {
    try {
      if (this.invoice && this.invoice.total) {
        return Number(this.invoice.total) + Number(this.deductionAmount ?? 0);
      }
    } catch (e) {
      console.error(e);
    }
    return 0;
  }

  getAmountAfterDeduction(): number {
    return this.getOrigialAmount() - this.deductionAmount;
  }

  calculateDeductionAmount() {
    switch (this.currentDeduction) {
      case DeductionType.voucher:
        this.deductionAmount = this.selectCouponComponent?.currentCoupon?.award ?? 0;
        break;
      case DeductionType.balance:
        this.deductionAmount = this.selectBalanceComponent?.deductionAmount ?? 0;
        break;
      case DeductionType.discount_code:
        this.deductionAmount = this.selectCodeComponent?.deductionAmount ?? 0;
        break;
      default:
        this.deductionAmount = 0;
        break;
    }
  }

  deDutiontoString(): string {
    switch (this.currentDeduction) {
      case DeductionType.balance:
        return "余额抵扣";
      case DeductionType.voucher:
        return "优惠券";
      case DeductionType.discount_code:
        return "折扣码抵扣";
      case DeductionType.none:
        return "无抵扣";
      default:
        return "未知抵扣类型";
    }
  }

  async buy() {
    const currentMethod = this.paymentMethodService.currentMethod$.value;
    try {
      const data = await this.apiManager.payInvoice(this.invoice, currentMethod.payType, "").toPromise();
      if (data.redirect_url !== undefined) {
        if (this.appService.isElectron() || this.appService.isExtension()) {
          window.open(data.redirect_url);
        } else {
          window.location = data.redirect_url;
        }
      }
      this.router.navigate(["payment-status"], { queryParams: { status: IN_PAYMENT, id: this.invoice.id } });
    } catch (e) {
      this.appService.snackUp(e?.error?.message);
      this.submitted = false;
    }
  }

  updateInvoiceDescription() {
    if (this.productList.length > 0 && this.invoice) {
      if (this.invoice.description.includes('Upgrade')) {
        const match = this.invoice.description.match(/Upgrade from (\d+) to (\d+)/);
        if (match) {
          const fromProductId = parseInt(match[1]);
          const toProductId = parseInt(match[2]);
          const fromProduct = this.productList.find(p => p.id === fromProductId);
          const toProduct = this.productList.find(p => p.id === toProductId);
          if (fromProduct && toProduct) {
            this.invoice.description = `套餐升级：${fromProduct.name} > ${toProduct.name}`;
          }
        }
      }

    }
  }

  formatDate(date: string): string {
    return moment(new Date(date)).format("YYYY-MM-DD HH:mm:ss");
  }
}
