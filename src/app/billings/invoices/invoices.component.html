<div class="invoice-content">
  <div>
    <h3>#{{ invoice?.id }}</h3>
    <table>
      <tr>
        <td i18n="@@invoices-properties-status">Status</td>
        <td>{{ appService.translate(invoice?.status) }}</td>
      </tr>
      <tr>
        <td i18n="@@invoices-properties-user">Invoiced to</td>
        <td>{{ usersService.user?.email }}</td>
      </tr>
      <tr>
        <td i18n="@@invoices-properties-description">Description</td>
        <td>{{ invoice?.description }} - {{ invoice?.serviceId }}</td>
      </tr>
      <tr>
        <td i18n="@@invoices-properties-total">Total</td>
        <td>
          {{ invoice?.total }}
          <span i18n>HKD</span>
        </td>
      </tr>
      <tr *ngIf="invoice?.description !== '充值'">
        <td i18n="@@invoices-properties-due">Due</td>
        <td>{{ invoice?.duedate }}</td>
      </tr>
      <tr *ngIf="invoice?.datepaid">
        <td i18n="@@invoices-properties-payed-date">Paid in</td>
        <td>{{ formatDate(invoice?.datepaid) }}</td>
      </tr>
    </table>
  </div>
  <mat-progress-bar *ngIf="submitted" mode="indeterminate"></mat-progress-bar>

  <div *ngIf="invoice?.status === 'Paid'">
    <button mat-raised-button color="warn" class="operation-btn" (click)="goToMyService()">我的服务</button>
  </div>

  <div *ngIf="invoice?.status === 'Unpaid'">
    <!--    <h3 i18n>Payment</h3>-->
    <app-payment-methods [excludePayType]="[this.payType.CreditCard]"></app-payment-methods>
    <div class="methods-title" *ngIf="currentDeduction" style="margin-top: 50px">优惠折扣</div>
    <div class="deduction" *ngIf="currentDeduction">
      <mat-radio-group aria-labelledby="methods-radio-group-label" class="methods-radio-group"
        [(ngModel)]="currentDeduction">
        <div *ngIf="currentDeduction === deductionType.balance" class="deduction-title">
          <mat-radio-button disabled="true" class="example-margin"
            [value]="deductionType.balance">使用余额抵扣</mat-radio-button>
        </div>
        <div *ngIf="currentDeduction === deductionType.balance" class="deduction-box" style="margin-bottom: 15px">
          <app-select-balance #selectBalanceComponent [enabled]="currentDeduction === deductionType.balance"
            [price]="getOrigialAmount()" (deductionAmountChange)="calculateDeductionAmount()"></app-select-balance>
        </div>

        <div class="deduction-title" *ngIf="deductionType.discount_code === currentDeduction">
          <mat-radio-button disabled="true" class="example-margin"
            [value]="deductionType.discount_code">使用折扣码</mat-radio-button>
        </div>
        <div class="deduction-box" *ngIf="deductionType.discount_code === currentDeduction">
          <app-select-code #selectCodeComponent [enabled]="false" [disableBtn]="disableVerifyBtn"
            [billingCycle]="currentBillingCycle?.id" [serviceId]="service?.id.toString()"
            (discountAmountChange)="calculateDeductionAmount()"></app-select-code>
        </div>

        <div *ngIf="deductionType.voucher === currentDeduction" class="deduction-title">
          <mat-radio-button disabled="true" class="example-margin"
            [value]="deductionType.voucher">使用优惠券折扣</mat-radio-button>
        </div>
        <div *ngIf="deductionType.voucher === currentDeduction" class="deduction-box">
          <app-select-coupon #selectCouponComponent [enabled]="true" [isInvoices]="true"
            (couponSelected)="calculateDeductionAmount()"></app-select-coupon>
        </div>
      </mat-radio-group>
    </div>
  </div>
  <app-payment-details #paymentDetailsComponent *ngIf="invoice?.status === 'Unpaid'" [buyType]="BuyType.SERVICE"
    [loading]="loading" [currentBillingCycle]="currentBillingCycle" [deductionAmount]="deductionAmount"
    [origialAmount]="getOrigialAmount()" [deDutiontoString]="deDutiontoString()"
    [amountAfterDeduction]="getAmountAfterDeduction()" [submitted]="submitted" (buy)="buy()"></app-payment-details>
</div>