import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-payment-success',
  templateUrl: './payment-success.component.html',
  styleUrls: ['./payment-success.component.css']
})
export class PaymentSuccessComponent implements OnInit {
  private serviceId: number | string;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
  ) { }

  ngOnInit(): void {
    this.route.queryParams.subscribe(queryParams => {
      this.serviceId = queryParams?.serviceId;
    });
  }

  navigateToService() {
    // this.router.navigate(['services', 'dashboard'], {queryParams: {id: this.serviceId}});
    this.router.navigate(['center']);
  }

}
