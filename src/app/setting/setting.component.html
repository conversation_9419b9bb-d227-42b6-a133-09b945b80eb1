<style>
  .setting {
    min-height: 100%;
    display: grid;
    grid-template-rows: 100px auto 60px;
    grid-template-columns: 100%;
    grid-template-areas:
      "header"
      "main"
      "footer";
  }
  .header {
    grid-area: header;
  }
  .main {
    grid-area: main;
  }
  .footer {
    grid-area: footer;
  }

  .back {
    border: 2px dashed #E5E5E5;
    width: 30px;
    height: 30px;
    margin-right: 17px;
  }

  h2 {
    font-family: Roboto;
    font-style: normal;
    font-weight: 700;
    font-size: 36px;
    line-height: 42px;

    color: #000000;
  }

  h4 {
    font-family: Roboto;
    font-style: normal;
    font-weight: 700;
    font-size: 24px;
    line-height: 28px;
    margin: 0;

    color: #000000;
  }

  .account-item span {
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: 500;
    font-size: 10px;
    line-height: 12px;

    color: #333333;
  }

  .text {
    font-family: Roboto;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 16px;
    /* identical to box height */


    color: #333333;
  }

</style>
<div fixFill class="setting">
  <section fxLayoutAlign="space-between center" class="header">
    <div fxLayoutAlign="space-between center">
      <div class="back" fxLayoutAlign="center center">
        <svg width="30" height="29" viewBox="0 0 30 29" fill="none" xmlns="http://www.w3.org/2000/svg" (click)="back()">
          <path d="M20.67 6.72531L18.89 4.95746L9 14.8454L18.9 24.7334L20.67 22.9656L12.54 14.8454L20.67 6.72531Z"
            fill="#333333" />
        </svg>
      </div>
      <h2>Setting</h2>
    </div>
    <div>
      <svg width="23" height="24" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M11.5 0C17.8513 0 23 5.14873 23 11.5C23 17.8513 17.8513 23 11.5 23C9.63896 23 7.8405 22.5568 6.22479 21.7212L1.8251 22.9486C1.06044 23.1622 0.267468 22.7154 0.0539416 21.9507C-0.0167161 21.6977 -0.0166819 21.4301 0.0539901 21.1773L1.2818 16.7811C0.444321 15.1639 0 13.3633 0 11.5C0 5.14873 5.14873 0 11.5 0ZM11.5 1.725C6.10142 1.725 1.725 6.10142 1.725 11.5C1.725 13.1901 2.15369 14.8159 2.95899 16.2581L3.13223 16.5683L1.8526 21.1501L6.43718 19.8711L6.74715 20.0439C8.18807 20.8474 9.81201 21.275 11.5 21.275C16.8986 21.275 21.275 16.8986 21.275 11.5C21.275 6.10142 16.8986 1.725 11.5 1.725ZM11.5 15.525C12.1351 15.525 12.65 16.0399 12.65 16.675C12.65 17.3101 12.1351 17.825 11.5 17.825C10.8649 17.825 10.35 17.3101 10.35 16.675C10.35 16.0399 10.8649 15.525 11.5 15.525ZM11.5 5.4625C13.2466 5.4625 14.6625 6.8784 14.6625 8.625C14.6625 9.78736 14.3203 10.4351 13.4537 11.3374L13.2599 11.5349C12.5447 12.2501 12.3625 12.5537 12.3625 13.225C12.3625 13.7013 11.9763 14.0875 11.5 14.0875C11.0237 14.0875 10.6375 13.7013 10.6375 13.225C10.6375 12.0626 10.9797 11.4149 11.8463 10.5126L12.0401 10.3151C12.7553 9.59994 12.9375 9.29628 12.9375 8.625C12.9375 7.83109 12.2939 7.1875 11.5 7.1875C10.7557 7.1875 10.1435 7.75316 10.0699 8.47802L10.0625 8.625C10.0625 9.10135 9.67635 9.4875 9.2 9.4875C8.72365 9.4875 8.3375 9.10135 8.3375 8.625C8.3375 6.8784 9.7534 5.4625 11.5 5.4625Z"
          fill="#212121" />
      </svg>
    </div>
  </section>
  <div class="main">
    <section>
      <h4>Account</h4>
      <mat-divider style="margin-top: 18px;"></mat-divider>
      <div class="account-item" style="margin-top: 14px;" fxLayoutAlign="space-between center">
        <span>AccountID</span>
        <span class="text"><EMAIL></span>
      </div>
      <mat-divider style="margin-top: 24px;"></mat-divider>
      <div class="account-item" style="margin-top: 14px;" fxLayoutAlign="space-between center">
        <span>Flash Point</span>
        <span class="text">150 pts</span>
      </div>
    </section>
    <section style="margin-top: 168px;">
      <h4 style="margin-bottom: 0;">
        Follow US
      </h4>
      <p class="text">Join our social network to get the latest updates</p>
      <div fxLayoutGap="14px" style="margin-top: 27px;">
        <svg width="34" height="33" aria-hidden="true" focusable="false" data-prefix="fab" data-icon="telegram"
          class="svg-inline--fa fa-telegram fa-w-16" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"
          fit="" preserveAspectRatio="xMidYMid meet">
          <path fill="currentColor"
            d="M248 8C111 8 0 119 0 256s111 248 248 248 248-111 248-248S385 8 248 8zm121.8 169.9l-40.7 191.8c-3 13.6-11.1 16.9-22.4 10.5l-62-45.7-29.9 28.8c-3.3 3.3-6.1 6.1-12.5 6.1l4.4-63.1 114.9-103.8c5-4.4-1.1-6.9-7.7-2.5l-142 89.4-61.2-19.1c-13.3-4.2-13.6-13.3 2.8-19.7l239.1-92.2c11.1-4 20.8 2.7 17.2 19.5z">
          </path>
        </svg>
        <svg width="34" height="33" viewBox="0 0 34 33" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M33.1429 16.9038C33.1429 7.91945 25.7246 0.637909 16.5714 0.637909C7.4183 0.637909 0 7.91945 0 16.9038C0 25.0241 6.05893 31.7528 13.9821 32.9728V21.6057H9.77455V16.9038H13.9821V13.3202C13.9821 9.24423 16.4549 6.99178 20.2417 6.99178C22.0542 6.99178 23.9509 7.30948 23.9509 7.30948V11.3124H21.86C19.8016 11.3124 19.1607 12.5673 19.1607 13.854V16.9038H23.7567L23.022 21.6057H19.1607V32.9728C27.0839 31.7528 33.1429 25.0241 33.1429 16.9038Z"
            fill="black" />
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" width="34" height="33" viewBox="0 0 557 557" fit=""
          preserveAspectRatio="xMidYMid meet" focusable="false">
          <g id="Group_1978" data-name="Group 1978" transform="translate(-72 -404)">
            <circle id="Ellipse_1" data-name="Ellipse 1" cx="278.5" cy="278.5" r="278.5" transform="translate(72 404)">
            </circle>
            <path id="twitter-brands"
              d="M289.238,113.334c.2,2.864.2,5.728.2,8.591,0,87.344-66.479,187.984-187.984,187.984A186.71,186.71,0,0,1,0,280.25a136.68,136.68,0,0,0,15.955.818A132.32,132.32,0,0,0,97.981,252.84a66.187,66.187,0,0,1-61.775-45.82,83.322,83.322,0,0,0,12.478,1.023,69.879,69.879,0,0,0,17.387-2.25,66.08,66.08,0,0,1-52.98-64.844v-.818a66.54,66.54,0,0,0,29.865,8.386A66.169,66.169,0,0,1,22.5,60.15a187.8,187.8,0,0,0,136.232,69.139,74.587,74.587,0,0,1-1.636-15.137A66.134,66.134,0,0,1,271.441,68.946a130.08,130.08,0,0,0,41.933-15.955A65.892,65.892,0,0,1,284.328,89.4a132.453,132.453,0,0,0,38.047-10.227,142.026,142.026,0,0,1-33.137,34.16Z"
              transform="translate(189.784 503.605)" fill="#fff"></path>
          </g>
        </svg>
      </div>
    </section>
    <section style="margin-top: 32px;">
      <h4>
        About
      </h4>
      <p style="margin-top: 14px;" class="text">
        Kikuni aims to provide the best learning tools for Japanese lovers.
      </p>
    </section>
  </div>
  <section class="footer" fxLayoutAlign="space-between center">
    <span>© 2016 Kikuni™</span>
    <span>V2.1.2</span>
  </section>
</div>
