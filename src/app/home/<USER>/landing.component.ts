import { Component, OnInit } from "@angular/core";
import { NotificationService, OPEN_DRAWER } from "@flashvpn-io/web-core";
import { environment } from "../../../environments/environment";
import { UsersService } from "@flashvpn-io/web-core";
import { Router } from "@angular/router";
import { APIManager } from "@flashvpn-io/web-core";
import { MatDialog } from "@angular/material/dialog";
import { WinDialogComponent } from "../../inbox/win-dialog/winDialog.component";
import { FcmService } from "@flashvpn-io/web-core";
import { Service } from "@flashvpn-io/web-core";
import { AppService } from "../../app.service";

@Component({
  selector: "app-landing",
  templateUrl: "./landing.component.html",
  styleUrls: ["./landing.component.css"],
})
export class LandingComponent implements OnInit {
  env = environment;
  email: any;
  service: Service;
  focused = false;
  isLogged = false;
  constructor(
    private notificationService: NotificationService,
    public appService: AppService,
    private router: Router,
    public apiManager: APIManager,
    public usersService: UsersService,
    public dialog: MatDialog,
    public fcmService: FcmService
  ) {
    this.service = Service.fromData({ id: "sample" });
  }

  async ngOnInit() {
    // only in extension, redirect to service page
    this.isLogged = await this.usersService.isLoggedIn();
    if (this.appService.isExtension() || this.appService.isElectron()) {
      // await this.router.navigate(["services"]);
      await this.router.navigate(["center"]);
    }
  }
  // 查看公告
  handleShowToldDialog() {
    this.dialog.open(WinDialogComponent);
  }

  openDrawer() {
    this.notificationService.post(OPEN_DRAWER);
  }

  tryLink() {
    this.router.navigate([this.isLogged ? "/services/dashboard" : "/users/signup"]);
  }
}
