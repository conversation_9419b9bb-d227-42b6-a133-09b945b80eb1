<style>
    .told-events {
        position: relative;
        padding: 0 10px;
        cursor: pointer;
    }

    .told-events-mobile {
        display: none;
        position: relative;
        padding: 0 10px;
        cursor: pointer;
    }

    .told-events-num {
        position: absolute;
        top: 0;
        left: 2px;
        height: 20px;
        width: 20px;
        border-radius: 50% 50%;
        background: #ff5e5e;
        line-height: 20px;
        text-align: center;
        font-size: 12px;
        color: #fff;
    }

    @media screen and (max-width: 599px) {
        .told-events {
            display: none;
        }

        .told-events-mobile {
            display: block;
        }

        .h0 {
            font-weight: 500;
            font-size: 2em
        }

        #homeBody {
            text-align: center;
        }

        #sellingPoint {
            text-align: left;
            display: block !important;
        }
    }

    #home {
        max-width: 1200px;
        height: 100%;
        margin: 0 auto;
        display: grid;
        grid-template-rows: 100px auto;
        grid-template-columns: 0 auto 0;
        grid-template-areas: ". header ." ". main . "
    }

    #homeHeader {
        grid-area: header;
    }

    #homeBody {
        grid-area: main;
    }

    .navi-link {
        font-family: 'Li<PERSON> Franklin', sans-serif;
        font-style: normal;
        font-weight: normal;
        font-size: 24px;
        line-height: 29px !important;
        color: #000000;
        text-decoration-line: none;
    }

    .footer-link {
        font-family: 'Libre Franklin', sans-serif;
        font-style: normal;
        font-weight: normal;
        font-size: 14px;
        line-height: 14px !important;
        text-decoration-line: none;
        color: #505050;
        display: inline-block;
        margin: 5px 0;
    }

    #brand {
        font-size: 30px;
        font-family: Merriweather, sans-serif;
        font-style: italic;
        text-decoration-line: none;
        color: black;
        margin-right: 60px !important;
    }

    #brandSmall {
        font-size: 24px;
        font-family: Merriweather, sans-serif;
        font-style: italic;
        text-decoration-line: none;
        color: black;
    }

    .h0 {
        font-weight: 500;
        font-size: 3em;
    }

    #sellingPoint img {
        width: 52px;
        height: 54px;
    }

    #sellingPoint {
        justify-content: space-around;
        box-sizing: border-box;
        display: flex;
    }

    .h {
        font-weight: 500;
        font-size: 6em;
    }

    #serverHolder {
        height: 700px;
        margin: 1em 0;
        position: relative;
        background: white;
        box-shadow: rgba(84, 70, 35, 0.15) 0px 2px 8px, rgba(84, 70, 35, 0.15) 0px 1px 3px;
        border-radius: 8px;
        overflow: hidden;
        padding: 0 10px;
    }

    #serverCover {
        background: rgba(255, 94, 94, 0.05);
        border: 3px solid #FF5E5E;
        box-sizing: border-box;
        border-radius: 10px;
        z-index: 100;
        width: 100%;
        height: 100%;
        position: absolute;
    }

    #serverCover:hover {
        background: rgba(255, 94, 94, 0);
    }

    #servers {
        margin: 20px;
        width: 100%;
        height: 100%;
    }

    #playBtn {
        width: 380px;
        height: 58px;
        border-radius: 35px;
        margin: 10px;
        background: black;
        color: white;
        line-height: 1;
        text-align: center;
    }

    #homeFooter {
        text-align: left;
    }
</style>
<div id="home">
    <div id="homeHeader" fxLayout.gt-sm="row" fxLayoutAlign="space-between center">
        <div fxLayout="row" fxLayoutGap="20px" fxLayoutAlign.gt-md="space-between center">
            <div (click)="openDrawer()" fxHide.gt-sm fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="start center">
                <mat-icon svgIcon="bars"></mat-icon>
                <a class="navi-link" id="brand" i18n [href]="env.web">FlashVPN</a>
            </div>
            <div fxHide.sm fxHide.xs fxLayout.gt-sm="row" fxLayoutGap.gt-sm="20px" fxLayoutAlign.gt-md="start center">
                <a class="navi-link" id="brand" i18n [href]="env.web">FlashVPN</a>
                <a id="landing-trial" class="navi-link" *ngIf="!isLogged" routerLink="/trial"
                    routerLinkActive="navi-link-active">试用</a>
                <a id="landing-sign-up" class="navi-link" *ngIf="!isLogged" routerLink="/users/signup"
                    routerLinkActive="navi-link-active" i18n>Sign Up</a>
                <a id="landing-sign-in" class="navi-link" *ngIf="!isLogged" routerLink="/users/signin"
                    routerLinkActive="navi-link-active" i18n>Sign In</a>
                <!--        <a id="landing-products" class="navi-link" *ngIf="!isLogged" routerLink="/products" routerLinkActive="navi-link-active" i18n>Products</a>-->
                <a id="landing-services" class="navi-link" routerLink="/services/dashboard"
                    routerLinkActive="navi-link-active" i18n>Services</a>
                <!--        <a id="landing-billings" class="navi-link" *ngIf="isLogged"  routerLink="/billings/index" routerLinkActive="navi-link-active" i18n>Billings</a>-->
                <!--        <a id="landing-plans" class="navi-link" *ngIf="isLogged"  routerLink="/plans" routerLinkActive="navi-link-active" i18n>Plans</a>-->
                <a id="landing-wallets" class="navi-link" *ngIf="isLogged" routerLink="/wallet"
                    routerLinkActive="navi-link-active" i18n>Wallet</a>
                <a id="landing-welfare" class="navi-link" *ngIf="isLogged" routerLink="/welfare"
                    routerLinkActive="navi-link-active" i18n>Welfare Center</a>
                <a id="landing-apps" class="navi-link" routerLink="/apps" routerLinkActive="navi-link-active"
                    i18n>Apps</a>
                <!--                <a id="landing-blog" class="navi-link" href="/blog/" routerLinkActive="navi-link-active" i18n>Blog</a>-->
                <a id="landing-help" class="navi-link" target="_blank" href="{{appService.guidesURL()}}" i18n>Help
                    Center</a>
            </div>
        </div>
        <div fxLayout="row" fxLayoutAlign="start center">
            <!--      <div fxLayout="row" fxHide.sm fxHide.xs id="navi-footer">-->
            <!--        <a mat-icon-button target="_blank" (click)="apiManager.redirect('twitter')"><mat-icon svgIcon="twitter"></mat-icon></a>-->
            <!--        <a mat-icon-button target="_blank" (click)="apiManager.redirect('telegram')"><mat-icon svgIcon="telegram"></mat-icon></a>-->
            <!--        <a mat-icon-button target="_blank" (click)="apiManager.redirect('facebook')"><mat-icon svgIcon="facebook"></mat-icon></a>-->
            <!--        <a mat-icon-button target="_blank" (click)="apiManager.redirect('line')"><mat-icon svgIcon="line"></mat-icon></a>-->
            <!--      </div>-->
            <!--      <app-i18-picker></app-i18-picker>-->
        </div>
        <div *ngIf="isLogged" class="told-events" style="padding-right:20px;" (click)="handleShowToldDialog()">
            <img style="width: 25px;" class="lazyload" data-src="assets/images/told.png" />
            <ng-container *ngIf="fcmService.unreadNum$ | async as unreadNum">
                <div class="told-events-num" *ngIf="unreadNum > 0">{{unreadNum}}</div>
            </ng-container>
        </div>
        <div *ngIf="isLogged" class="told-events-mobile" routerLink="/inbox/message">
            <img style="width: 25px;" class="lazyload" data-src="assets/images/told.png" />
            <ng-container *ngIf="fcmService.unreadNum$ | async as unreadNum">
                <div class="told-events-num" *ngIf="unreadNum > 0">{{unreadNum}}</div>
            </ng-container>
        </div>
    </div>
    <div id="homeBody">
        <div style="margin: 10vw 0" fxLayout.gt-sm="row" fxLayoutAlign.gt-sm="space-between center">
            <div fxFlex.gt-sm="40">
                <div>
                    <h1 i18n>Only VPN You Ever Need</h1>
                </div>
                <h3 i18n class="bodyTextColor">FlashVPN is a simple to use, fast and stable VPN service that runs all of
                    your devices. Flash gas pedal guards your network security and privacy.</h3>
                <div fxHide.sm fxHide.xs id="space" style="height: 100px"></div>
            </div>
            <div fxFlex.gt-sm="50">
                <img class="lazyload" style="max-width: 100%; margin-top: 20px" data-src="assets/images/hero.svg"
                    alt="The Only VPN You Ever Need">
            </div>
        </div>
        <div id="sellingPoint" fxLayout.gt-sm="row" fxLayoutGap.gt-sm="10px" style="justify-content: space-around">
            <div fxFlex.gt-sm="25">
                <img class="lazyload" data-src="assets/images/cross-platform.svg" alt="Cross Platform">
                <h2 i18n>Cross Platform APPs</h2>
                <h3 class="bodyTextColor" i18n>We provide iOS, Android, Windows, macOS APPs, they all feel the same.
                </h3>
            </div>
            <div fxFlex.gt-sm="25">
                <img class="lazyload" data-src="assets/images/browser-extension.svg" alt="Works in Browser">
                <h2 i18n>Works in Browser</h2>
                <h3 class="bodyTextColor" i18n>Our Chrome/Edge extension connect directly to our servers, which is much
                    faster and stable.</h3>
            </div>
            <div fxFlex.gt-sm="25">
                <img class="lazyload" data-src="assets/images/privacy-protection.svg" alt="Absolutely No Logs">
                <h2 i18n>Absolutely No Logs</h2>
                <h3 class="bodyTextColor" i18n>Your browsing history is nothing but liability to us, so why bother to
                    save. You are safe with us.</h3>
            </div>
            <!-- <div fxFlex.gt-sm="25">
        <img src="assets/images/unlock-content.svg" alt="Unlock Contents">
        <h2 i18n>Unlock Contents</h2>
        <h3  class="bodyTextColor" i18n>We have servers all over the world, you can watch Netflix, HBO, AmebaTV anywhere you want.</h3>
      </div> -->
        </div>
        <div class="divider" style="margin: 6vw 0 2vw 0"></div>
        <div fxLayout="column" fxLayoutAlign="start center">
            <div i18n class="h0" style="margin-top: 8vw; margin-bottom: 10px">Too Many Failed VPN?</div>
            <div i18n style="font-size: 20px;" class="bodyTextColor">With FlashVPN, you won't look again.</div>
        </div>
        <div fxLayout.gt-sm="row" fxLayoutAlign.gt-sm="space-between end" fxLayout="column" fxLayoutAlign="start center"
            fxLayoutGap="10px" style="margin-top: 6vw">
            <div fxLayout="row" fxLayoutAlign="start end" fxLayoutGap="30px">
                <div class="h" fxHide.lt-md>1</div>
                <div fxLayout="column" fxLayoutGap="10px">
                    <div class="h2Size"><span fxHide.gt-sm>1. </span><span i18n>Holistic Experience</span></div>
                    <div class="h3Size bodyTextColor" i18n>All apps shares the same simple and intuitive interfaces.
                    </div>
                </div>
            </div>
            <div class="bodyTextColor h3Size" i18n>A single account can be used on all devices.</div>
        </div>
        <div fxLayout.gt-sm="row" fxLayoutAlign.gt-sm="space-between start" fxLayoutAlign="center start"
            style="margin-top: 2vw">
            <picture>
                <source type="image/webp" srcset="assets/images/shot-one.webp">
                <source type="image/jpeg" srcset="assets/images/shot-one.jpg">
                <img fxHide.lt-md data-src="assets/images/shot-one.jpg" class="lazyload" alt="Unlock Contents">
            </picture>
            <picture>
                <source type="image/webp" srcset="assets/images/shot-two.webp">
                <source type="image/jpeg" srcset="assets/images/shot-two.jpg">
                <img data-src="assets/images/shot-two.jpg" class="lazyload" alt="Unlock Contents">
            </picture>
        </div>
        <div fxLayout.gt-sm="row" fxLayoutAlign.gt-sm="space-between end" fxLayout="column" fxLayoutAlign="start center"
            fxLayoutGap="10px" style="margin-top: 6vw">
            <div fxFlex="30" fxLayout="row" fxLayoutAlign="start end" fxLayoutGap="20px">
                <div class="h" fxHide.lt-md>2</div>
                <div fxLayout="column" fxLayoutGap="10px">
                    <div class="h2Size"><span fxHide.gt-sm>2. </span><span i18n>Fast and Stable</span></div>
                    <div class="h3Size bodyTextColor" i18n>All servers are monitored, idle servers will never appear.
                    </div>
                </div>
            </div>
            <div fxFlex="30" class="bodyTextColor h3Size" i18n>Works very well within China thanks to our advanced
                technology.</div>
            <form fxLayout="row" fxLayoutAlign="start end" fxLayoutGap="10px">
                <mat-form-field class="form-input">
                    <input #email id="emailInput" matInput i18n-placeholder placeholder="Input your email" required
                        name="email">
                </mat-form-field>
                <a id="signUpBtn" class="callout-button" style="color: #FFFFFF" routerLink="/users/signup"
                    [queryParams]="{email: email.value}" i18n>Get Started</a>
            </form>
        </div>
        <!--    <div id="serverHolder" fxLayout="column" fxLayoutAlign="center center" style="margin-top: 2vw">-->
        <!--      <div id="serverCover" *ngIf="!focused"  fxLayout="column" fxLayoutAlign="center center">-->
        <!--        <div id="playBtn" fxLayout="column" fxLayoutAlign="center center" (click)="focused=true">-->
        <!--          <div i18n class="h2Size">Live servers - click to play</div>-->
        <!--        </div>-->
        <!--      </div>-->
        <!--       <app-servers id="servers" [service]="service"></app-servers> -->
        <!--    </div>-->
        <!--    <div fxLayout="column" fxLayoutAlign="start center">-->
        <!--      <div i18n class="h0" style="margin-top: 8vw; margin-bottom: 10px">Try it now</div>-->
        <!--      <div i18n style="font-size: 20px;" class="bodyTextColor">{{appService.brand}} works on all your devices.</div>-->
        <!--    </div>-->
        <!--    <div fxLayout="row" fxLayoutAlign="space-between start" fxLayoutAlign.lt-md="center center"  style="margin-top: 4vw">-->
        <!--      <div fxHide.lt-md fxLayout="column" fxLayoutAlign="start center" fxLayoutGap="10px">-->
        <!--        <div class="h1Size" i18n>Web apps</div>-->
        <!--        <img src="assets/images/shot-three.jpg" alt="Unlock Contents">-->
        <!--        <app-app-version  [simpleMode]="true" [platform]="'chrome'" [version] = "'latest'" ></app-app-version>-->
        <!--      </div>-->
        <!--      <div fxHide.lt-md fxLayout="column" fxLayoutAlign="start center" fxLayoutGap="10px">-->
        <!--        <div class="h1Size" i18n>Desktop apps</div>-->
        <!--        <img src="assets/images/shot-four.jpg" alt="Unlock Contents">-->
        <!--        <div fxLayout="row" fxLayoutGap="20px">-->
        <!--          <app-app-version  [simpleMode]="true" [platform]="'macos'" [version] = "'latest'" ></app-app-version>-->
        <!--          <app-app-version  [simpleMode]="true" [platform]="'windows'" [version] = "'latest'" ></app-app-version>-->
        <!--        </div>-->
        <!--      </div>-->
        <!--      <div fxLayout="column" fxLayoutAlign="start center" fxLayoutGap="10px">-->
        <!--        <div class="h1Size" i18n>Mobile apps</div>-->
        <!--        <img src="assets/images/shot-six.jpg" alt="Unlock Contents">-->
        <!--        <app-app-version  [simpleMode]="true" [platform]="'android'" [version] = "'latest'" ></app-app-version>-->
        <!--      </div>-->
        <!--    </div>-->
        <div class="divider" style="margin: 3vw 0 3vw 0"></div>
        <div id="homeFooter" fxLayout="column" fxLayoutGap="20px" fxLayout.gt-sm="row" fxLayoutGap.gt-sm="50px"
            fxLayoutAlign="start start" style="margin: 0 0 2vw 0">
            <div fxFlex.gt-sm="40" fxLayoutGap="10px" fxLayout="column" style="margin-right: 50px">
                <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="10px">
                    <img class="lazyload" data-src="assets/images/logo.svg" alt="Unlock Contents">
                    <div id="brandSmall"><span i18n>FlashVPN</span></div>
                </div>
                <div i18n>We aim to provide the best networking facility tools to help customers to reach their desired
                    content.</div>
                <div class="h5Size">© FlashVPN Network 2020</div>
                <div style="margin-bottom: 10px" class="h5Size"><a href="static/tos.html" target="_blank" i18n>Terms of
                        Service</a> <span i18n> and </span> <a href="https://help.flashvpn.io/flashvpn-privacy-policy"
                        target="_blank" i18n>Privacy Policy</a></div>
            </div>
            <div fxFlex.gt-sm="10">
                <h4 i18n style="font-weight: bold; margin: 0 0 10px 0">Links</h4>
                <div><a id="footer-apps" class="footer-link" routerLink="/apps" routerLinkActive="navi-link-active"
                        i18n>Apps</a></div>
                <div><a id="footer-services" class="footer-link" routerLink="/services"
                        routerLinkActive="navi-link-active" i18n>Services</a></div>
                <div><a id="footer-sign-up" class="footer-link" routerLink="/users/signup"
                        routerLinkActive="navi-link-active" i18n>Sign Up</a></div>
                <div><a id="footer-sign-in" class="footer-link" routerLink="/users/signin"
                        routerLinkActive="navi-link-active" i18n>Sign In</a></div>
            </div>
            <div fxFlex.gt-sm="10">
                <h4 i18n style="font-weight: bold; margin: 0 0 10px 0">Help & Contact</h4>
                <div><a id="footer-twitter" class="footer-link" target="_blank"
                        href="https://twitter.com/vpn_flash">Twitter</a></div>
                <div><a id="footer-telegram" class="footer-link" target="_blank"
                        href="https://t.me/+FvmAI2yHUAWSW0Rb">Telegram</a></div>
                <div><a id="footer-facebook" class="footer-link" target="_blank"
                        href="https://www.facebook.com/profile.php?id=100063705956009">Facebook</a></div>
            </div>
            <div fxFlex.gt-sm="30"></div>
        </div>
    </div>
</div>