.wallet-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.recharge-tag {
  font-size: 12px;
  color: #ffffff;
  background-color: #ff5e5e;
  max-height: 18px;
  line-height: 18px;
  padding-left: 5px;
  padding-right: 5px;
  border-radius: 2.5px;
}

.mat-btn {
  color: white;
  box-shadow: 0px 3px 1px -2px rgb(0 0 0 / 20%), 0px 2px 2px 0px rgb(0 0 0 / 14%), 0px 1px 5px 0px rgb(0 0 0 / 12%);
  background-color: #ff5e5e;

  box-sizing: border-box;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: pointer;
  outline: none;
  border: none;
  -webkit-tap-highlight-color: transparent;
  display: inline-block;
  white-space: nowrap;
  text-decoration: none;
  vertical-align: baseline;
  text-align: center;
  margin: 0;
  min-width: 64px;
  line-height: 36px;
  padding: 0 16px;
  border-radius: 4px;
  overflow: visible;
  transform: translate3d(0, 0, 0);
  transition: background 400ms cubic-bezier(0.25, 0.8, 0.25, 1), box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  font-weight: 500;
}

.mat-btn-disable {
  background-color: rgba(0, 0, 0, 0.12);
  color: rgba(0, 0, 0, 0.26);
}

.desktop-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.mobile-header {
  display: none;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.mobile-header-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}

@media screen and (max-width: 960px) {
  .desktop-header {
    display: none;
  }

  .mobile-header {
    display: flex;
  }
}