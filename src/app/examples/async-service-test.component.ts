import { Component, OnInit } from '@angular/core';
import { ServiceService } from '@flashvpn-io/web-core';
import * as moment from 'moment';

@Component({
  selector: 'app-async-service-test',
  template: `
    <div class="test-container">
      <h2>异步服务加载测试</h2>
      
      <!-- 显示当前服务状态 -->
      <div class="service-status">
        <h3>服务状态</h3>
        <div *ngIf="serviceService.currentService$ | async as service; else noService">
          <p>✅ 服务已加载: {{ service.name }} (ID: {{ service.id }})</p>
        </div>
        <ng-template #noService>
          <p>⏳ 等待服务加载...</p>
        </ng-template>
      </div>

      <!-- 测试按钮 -->
      <div class="test-buttons">
        <button (click)="refreshServices()">刷新服务信息</button>
        <button (click)="clearCurrentService()">清空当前服务</button>
      </div>

      <!-- Traffic Log 组件 - 不传 serviceId，测试异步加载 -->
      <div class="traffic-log-section">
        <h3>流量日志 (无 serviceId)</h3>
        <app-traffic-log
          [startDate]="startDate"
          [endDate]="endDate">
        </app-traffic-log>
      </div>

      <!-- Traffic Log 组件 - 传入固定 serviceId -->
      <div class="traffic-log-section" *ngIf="fixedServiceId">
        <h3>流量日志 (固定 serviceId: {{ fixedServiceId }})</h3>
        <app-traffic-log
          [serviceId]="fixedServiceId"
          [startDate]="startDate"
          [endDate]="endDate">
        </app-traffic-log>
      </div>
    </div>
  `,
  styles: [`
    .test-container {
      padding: 20px;
      max-width: 1000px;
      margin: 0 auto;
    }

    .service-status {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 8px;
    }

    .test-buttons {
      margin-bottom: 30px;
    }

    .test-buttons button {
      margin-right: 10px;
      padding: 8px 16px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .test-buttons button:hover {
      background-color: #0056b3;
    }

    .traffic-log-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
    }

    .traffic-log-section h3 {
      margin-top: 0;
      color: #333;
    }
  `]
})
export class AsyncServiceTestComponent implements OnInit {
  startDate: string = moment().subtract(7, 'days').format('YYYY.M.D');
  endDate: string = moment().format('YYYY.M.D');
  fixedServiceId?: number;

  constructor(public serviceService: ServiceService) {}

  ngOnInit() {
    // 监听服务变化，获取第一个服务的ID作为固定测试ID
    this.serviceService.serviceInfo$.subscribe(services => {
      if (services && services.length > 0) {
        this.fixedServiceId = services[0].id;
      }
    });

    // 确保服务信息已加载
    this.serviceService.refreshServiceInfo();
  }

  refreshServices() {
    console.log('手动刷新服务信息...');
    this.serviceService.refreshServiceInfo();
  }

  clearCurrentService() {
    console.log('清空当前服务...');
    // 这只是为了测试，实际应用中不应该这样做
    this.serviceService.currentService$.next(null);
  }
}
