<div class="wake" fxFill>
  <div class="title" i18n>FlashVPN set off again!</div>
  <div *ngIf="!success">
    <div class="content" *ngIf="!isLogged">
      <div class="content-text" i18n>To get your gift information, please log in first!</div>
      <a class="action-button" id="wake" (click)="goToSigin()" i18n>Sign in to get the return package</a>
    </div>
    <div class="content" *ngIf="isLogged">
      <div class="content-text focus-in-expand" [innerHTML]="activity.description"></div>
      <button id="getReturnPackage" *ngIf="!submitted" mat-raised-button color="warn" (click)="submit()" i18n>Get the Return Package</button>
      <div class="botton_box" *ngIf="submitted">
        <button mat-fab color="warn" class="bounce-in-fwd">
          <mat-spinner diameter="30"></mat-spinner>
        </button>
      </div>
    </div>
  </div>
  <div *ngIf="success">
    <div class="content-success" *ngIf="isLogged">
      <div class="success-logo">
        <svg width="104" height="104" viewBox="0 0 104 104" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M52 91C73.5391 91 91 73.5391 91 52C91 30.4609 73.5391 13 52 13C30.4609 13 13 30.4609 13 52C13 73.5391 30.4609 91 52 91ZM71.5004 41.3246L66.1734 36.3999L43.0783 57.7505L33.9275 49.2909L28.6004 54.2156L43.0783 67.5999L71.5004 41.3246Z" fill="#FF5E5E"/>
        </svg>
      </div>
      <div class="content-success-text">
        <div style="margin-bottom: 10px">
          <span i18n>We have successfully updated the service time for you, please go to the </span>
          <a class="content-text-a" routerLink="/services/dashboard" routerLinkActive="navi-link-active" i18n>service page</a>
          <span i18n> to check.</span>
        </div>
        <div style="margin-bottom: 10px">
          <span i18n>We have successfully recharged the amount for you, you can deduct it when paying, go to the </span>
          <a class="content-text-a" routerLink="/wallet" routerLinkActive="navi-link-active" i18n>wallet page</a>
          <span i18n> to check your balance.</span>
        </div>
        <div style="margin-bottom: 10px">
          <span i18n>We have released a new version of APPS, you are welcome to go to the </span>
          <a class="content-text-a" routerLink="/apps" routerLinkActive="navi-link-active" i18n>APPS page</a>
          <span i18n> to download and experience.</span>
        </div>
      </div>
    </div>
  </div>
</div>
