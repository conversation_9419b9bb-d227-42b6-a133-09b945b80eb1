import { Component, OnInit, ViewEncapsulation } from "@angular/core";
import { UsersService } from "@flashvpn-io/web-core";
import { ActivatedRoute, Router } from "@angular/router";
import { APIManager } from "@flashvpn-io/web-core";
import { MatSnackBar } from "@angular/material/snack-bar";

@Component({
  selector: 'app-wake',
  templateUrl: './wake.component.html',
  styleUrls: ['./wake.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class WakeComponent implements OnInit {

  isLogged = false;

  giftInfo: any;

  submitted = false;

  success = false;
  activity = {
    id: 0,
    name: 'Initial Activities',
    description: 'Loading',
    type: 'BothAll',
    status: 'online',
    gap: 15,
    balance: 15,
    day: 14,
    traffic: 0,
    expiredAt: '2022-10-01 00:00:00',
    createdAt: '2022-10-01 00:00:00',
    updatedAt: '2022-10-01 00:00:00',
  };
  activityId = 1;

  constructor(
    private router: Router,
    private usersService: UsersService,
    private apiManager: APIManager,
    private snackBar: MatSnackBar,
    public route: ActivatedRoute,
  ) {}

  async ngOnInit(): Promise<void> {
    this.route.params.subscribe(
      async params => {
        this.isLogged = await this.usersService.isLoggedIn();
        if (this.isLogged) {
          const { id } = params;
          this.apiManager.getActivityInfo(id || this.activityId).subscribe(res => {
            this.activity = res as any;
            if (id > 0) {
              this.activityId = id;
            }
          }, err => {
            this.snackBar.open(err.error.message, 'Okay', {
              duration: 2000,
              verticalPosition: 'top'
            });
          });
        }
      }
    );
  }

  goToSigin = () => {
      this.router.navigate(['users', 'signin'], {queryParams: {url: this.router.url}});
  }

  /**
   * awake
   */
  awake = ( serviceId ) => {
    this.apiManager.wakeUp(serviceId, +this.activityId || 1)
      .subscribe(
        async res => {
          await this.apiManager.fetchServices().toPromise();
          this.submitted = false;
          this.success = true;
          // if(serviceId){
          //   await this.router.navigate(['services', 'dashboard'], {queryParams: {id: serviceId}});
          // }else{
          //   await this.router.navigate(['services', 'dashboard']);
          // }
        },
        err => {
          this.submitted = false;
          this.snackBar.open(err?.error?.message, 'Okay', {
            duration: 2000,
            verticalPosition: 'top'
          });
        }
      );
  }


  submit = async () => {
    this.submitted = true;
    let services = await this.apiManager.fetchServices().toPromise();
    let serviceId = null;
    if (services && services.length > 0) {
      serviceId = services.sort(( a, b ) => a.daysTillDue() - b.daysTillDue() )[0]?.id;
    }
    await this.awake(serviceId);
  }

}
