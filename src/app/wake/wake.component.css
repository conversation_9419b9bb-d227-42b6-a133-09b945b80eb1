.wake{

}
.title{
  font-family: 'Libre Franklin';
  font-style: normal;
  font-weight: 550;
  font-size: 50px;
  color: #000000;
  margin: 50px;
}
.content{
  text-align: center;
  min-height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin: 0 100px;
}
.content-text{
  font-family: 'Libre Franklin';
  font-style: normal;
  font-weight: 500;
  font-size: 30px;
  color: #000000;
  line-height: 70px;
  margin-bottom: 30px;
}
.content-text > span{
  font-weight: 600;
  color: #FF5E5E;
}
/deep/.mat-spinner circle {
  stroke: #FFFFFF;
}
.botton_box{
  display: flex;
  align-items: center;
  justify-content: center;
}
.bounce-in-fwd {
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-animation: bounce-in-fwd 0.8s both;
  animation: bounce-in-fwd 0.8s both;
}

@-webkit-keyframes bounce-in-fwd {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  38% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
    opacity: 1;
  }
  55% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  72% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  81% {
    -webkit-transform: scale(0.84);
    transform: scale(0.84);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  89% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  95% {
    -webkit-transform: scale(0.95);
    transform: scale(0.95);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}
@keyframes bounce-in-fwd {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  38% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
    opacity: 1;
  }
  55% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  72% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  81% {
    -webkit-transform: scale(0.84);
    transform: scale(0.84);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  89% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  95% {
    -webkit-transform: scale(0.95);
    transform: scale(0.95);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}

.content-success{
  margin: 100px;
}

.content-success-text{
  font-family: 'Libre Franklin';
  font-style: normal;
  font-weight: 500;
  font-size: 25px;
  color: #000000;
  line-height: 50px;
  margin-bottom: 30px;
  -webkit-animation: text-focus-in 0.6s cubic-bezier(0.550, 0.085, 0.680, 0.530) both;
  animation: text-focus-in 0.6s cubic-bezier(0.550, 0.085, 0.680, 0.530) both;
}

.content-text-a{
  font-weight: 600;
  color: #FF5E5E;
  font-size: 30px;
  text-decoration: underline;
}

.success-logo{
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  -webkit-animation: bounce-in-fwd 0.8s both;
  animation: bounce-in-fwd 0.8s both;
}

@media screen and (max-width: 599px){
  .title{
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: 550;
    font-size: 40px;
    color: #000000;
    text-align: center;
  }
  .content{
    margin: 20px;
    position: relative;
    left: 0;
    top: 0;
    transform: none;
    text-align: center;
    width: auto;
  }
  .content-text{
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: 500;
    font-size: 30px;
    color: #000000;
    line-height: 50px;
    margin-bottom: 30px;
  }
  .content-success{
    margin: 20px;
    text-align: center;
  }
}

@-webkit-keyframes text-focus-in {
  0% {
    -webkit-filter: blur(12px);
    filter: blur(12px);
    opacity: 0;
  }
  100% {
    -webkit-filter: blur(0px);
    filter: blur(0px);
    opacity: 1;
  }
}
@keyframes text-focus-in {
  0% {
    -webkit-filter: blur(12px);
    filter: blur(12px);
    opacity: 0;
  }
  100% {
    -webkit-filter: blur(0px);
    filter: blur(0px);
    opacity: 1;
  }
}




