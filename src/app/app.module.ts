import { LOCALE_ID, NgModule } from "@angular/core";
import { BrowserModule } from "@angular/platform-browser";
import { environment } from "../environments/environment";

import { HttpClientModule } from "@angular/common/http";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { MatBottomSheetModule } from "@angular/material/bottom-sheet";
import { MatButtonModule } from "@angular/material/button";
import { MatButtonToggleModule } from "@angular/material/button-toggle";
import { MatCardModule } from "@angular/material/card";
import { MatCheckboxModule } from "@angular/material/checkbox";
import { MatRippleModule } from "@angular/material/core";
import { MatDialogModule } from "@angular/material/dialog";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatIconModule } from "@angular/material/icon";
import { MatInputModule } from "@angular/material/input";
import { MatListModule } from "@angular/material/list";
import { MatMenuModule } from "@angular/material/menu";
import { MatPaginatorIntl, MatPaginatorModule } from "@angular/material/paginator";
import { MatProgressBarModule } from "@angular/material/progress-bar";
import { MatProgressSpinnerModule } from "@angular/material/progress-spinner";
import { MatRadioModule } from "@angular/material/radio";
import { MatSelectModule } from "@angular/material/select";
import { MatSidenavModule } from "@angular/material/sidenav";
import { MatSlideToggleModule } from "@angular/material/slide-toggle";
import { MatSnackBarModule } from "@angular/material/snack-bar";
import { MatStepperModule } from "@angular/material/stepper";
import { MatTableModule } from "@angular/material/table";
import { MatTabsModule } from "@angular/material/tabs";
import { MatTooltipModule } from "@angular/material/tooltip";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { WebCoreModule } from "@flashvpn-io/web-core";
import { QRCodeModule } from "angularx-qrcode";
import { InfiniteScrollModule } from "ngx-infinite-scroll";
import { AppRoutingModule } from "./app-routing.module";
import { AppComponent } from "./app.component";
import { BillingIndexComponent } from "./billings/billing-index/billing-index.component";
import { BillingsComponent } from "./billings/billings.component";
import { InvoicesComponent } from "./billings/invoices/invoices.component";
import { PaymentSuccessComponent } from "./billings/payment-success/payment-success.component";
import { PaymentComponent } from "./billings/payment/payment.component";
import { CommunityComponent } from "./component/community.component";
import { CouponListComponent } from "./component/coupon-list.component";
import { MobilePaymethodComponent } from "./component/mobile-paymethod.component";
import { PopularityComponent } from "./component/popularity.component";
import { VoucherComponent } from "./component/voucher.component";
import { ArticleComponent } from "./helps/article/article.component";
import { HelpsComponent } from "./helps/helpIndex/helps.component";
import { HelpShowComponent } from "./helps/helpShow/helpShow.component";
import { LandingComponent } from "./home/<USER>/landing.component";
import { DetailsComponent } from "./inbox/details/details.component";
import { MessageComponent } from "./inbox/message/message.component";
import { DetailDialogComponent } from "./inbox/win-dialog-detail/detailDialog.component";
import { WinDialogComponent } from "./inbox/win-dialog/winDialog.component";
import { CsComponent } from "./pages/cs.component";
import { MyAccountComponent } from "./pages/my-account.component";
import { MyVoucherComponent } from "./pages/my-voucher.component";
import { PayStatusComponent } from "./pages/pay-status.component";
import { RechargeWalletComponent } from "./pages/recharge-wallet.component";
import { WelfareComponent } from "./pages/welfare.component";
import { PlansComponent } from "./plans/plans.component";
import { ProductsComponent } from "./products/products/products.component";
import { InfoComponent } from "./rewards/info/info.component";
import { InviteComponent } from "./rewards/invite/invite.component";
import { RewardsComponent } from "./rewards/rewards/rewards.component";
import { TransactionsComponent } from "./rewards/transactions/transactions.component";
import { DashboardComponent } from "./services/dashboard/dashboard.component";
import { MatPaginatorIntlCro } from "./services/dashboard/MatPaginatorIntlCro";
import { ExampleComponent } from "./services/example/example.component";
import { PaymentStatusComponent } from "./services/payment-status/payment-status.component";
import { RegisterBuyComponent } from "./services/register-buy/register-buy.component";
import { SelectVoucherComponent } from "./services/select-voucher/select-voucher.component";
import { ServiceRenewComponent } from "./services/service-renew/service-renew.component";
import { ServicesComponent } from "./services/services/services.component";
import { ToPayComponent } from "./services/to-pay/to-pay.component";
import { WelcomeComponent } from "./services/welcome/welcome.component";
import { SettingComponent } from "./setting/setting.component";
import { SettingBaseComponent } from "./settings/setting-base/setting-base.component";
import { TelegramComponent } from "./telegram/telegram.component";
import { ForgetPasswordVerifyComponent } from "./users/forget-password-verify/forget-password-verify.component";
import { ForgetPasswordComponent } from "./users/forget-password/forget-password.component";
import { SigninComponent } from "./users/signin/signin.component";
import { SignupVerifyComponent } from "./users/signup-verify/signup-verify.component";
import { SignupComponent } from "./users/signup/signup.component";
import { UsersComponent } from "./users/users/users.component";
import { DialogsComponent } from "./utils/dialogs/dialogs.component";
import {
  DeductionDialog, EmailVerificationDialog, InvoiceCheckDialog,
  PaymentMenthodsDialog,
  PaymentsDialog,
  ReceiveDialog,
  RedeemDialog,
  SelectVoucherDialog,
  SendDialog,
  SignoutCheckDialog, TrialFailureDialog, TrialInvoiceCheckDialog,
  UpdateConfirmDialog,
  UpdateDialog,
  UpgradeConfirmDialog,
  WindowsExportDialog
} from "./utils/dialogs/dialogs.service";
import { DividerComponent } from "./utils/divider/divider.component";
import { LoadBottonComponent } from "./utils/load-botton/load-botton.component";
import { MiscI18nComponent } from "./utils/misc-i18n/misc-i18n.component";
import { DataPipe } from "./utils/pipes/data.pipe";
import { DiscountPipe, ValidatePromoCodePipe } from "./utils/pipes/discount.pipe";
import { AllVersionComponent } from "./versions/all-version/all-version.component";
import { AndroidGuideComponent } from "./versions/android-guide/android-guide.component";
import { AppVersionComponent } from "./versions/app-version/app-version.component";
import { AppsComponent } from "./versions/apps/apps.component";
import { ClashGuideComponent } from "./versions/clash-guide/clash-guide.component";
import { GuideComponent } from "./versions/guide/guide.component";
import { IosGuideComponent } from "./versions/ios-guide/ios-guide.component";
import { LatestVersionComponent } from "./versions/latest-version/latest-version.component";
import { MacGuideComponent } from "./versions/mac-guide/mac-guide.component";
import { RedirectComponent } from "./versions/redirect/redirect.component";
import { WinGuideComponent } from "./versions/win-guide/win-guide.component";
import { WakeComponent } from "./wake/wake.component";
import { WebsocketService } from "./websocket.service";

import { MatBadgeModule } from "@angular/material/badge";
import { RouteReuseStrategy } from "@angular/router";
import { NgxSkeletonLoaderModule } from "ngx-skeleton-loader";
import { SwiperConfigInterface, SwiperModule, SWIPER_CONFIG } from "ngx-swiper-wrapper";
import { ClaimComponent } from "./claim/claim.component";
import { AlreadyRegisteredComponent } from "./component/already-registered.component";
import { AppVersionsInfoComponent } from "./component/app-versions-info.component";
import { BillingCyclesComponent } from "./component/billing-cycles.component";
import { BuyProductsComponent } from "./component/buy-products.component";
import { CouponsCountdownComponent } from "./component/coupons-countdown.component";
import { DeductionComponent } from "./component/deduction.component";
import { DigitalTitleComponent } from "./component/digital-title.component";
import { InviteRecordComponent } from "./component/invite-record.component";
import { InvoiceHistoryScrollComponent } from "./component/invoice-history-scroll.component";
import { InvoiceHistoryComponent } from "./component/invoice-history.component";
import { OauthFormComponent } from "./component/oauth-form.component";
import { OauthComponent } from "./component/oauth.component";
import { PaymentDetailsComponent } from "./component/payment-details.component";
import { PaymentMethodsComponent } from "./component/payment-methods.component";
import { SelectBalanceComponent } from "./component/select-balance.component";
import { SelectCodeComponent } from "./component/select-code.component";
import { SelectCouponComponent } from "./component/select-coupon.component";
import { ServiceTileComponent } from "./component/service-tile.component";
import { ServiceUpgradeComponent } from "./component/service-upgrade.component";
import { TipsComponent } from "./component/tips.component";
import { UpgradeProductsComponent } from "./component/upgrade-products.component";
import { UserTabsComponent } from "./component/user-tabs.component";
import { WaitCollectionComponent } from "./component/wait-collection.component";
import { ConfettiService } from "./confetti.service";
import { FcmComponent } from "./pages/fcm.component";
import { PurchaseDataComponent } from "./pages/purchase-data.component";
import { RepurchaseComponent } from "./pages/repurchase.component";
import { ServerUpgradeComponent } from "./pages/server-upgrade.component";
import { PaymentSuccessPageComponent } from "./payment-success-page/payment-success-page.component";
import { WaitPaymentComponent } from "./payment/wait-payment/wait-payment.component";
import { SimpleReuseStrategy } from "./simple-reuse-strategy";
import { TrialComponent } from "./trial/trial.component";
import { PersonalCenterComponent } from './users/personal-center/personal-center.component';
import { QrDialogComponent } from "./utils/dialogs/qr-dialog.component";
import { IconService } from "./utils/icon.service";
import { UbuntuGuideComponent } from "./versions/ubuntu-guide/ubuntu-guide.component";
const DEFAULT_SWIPER_CONFIG: SwiperConfigInterface = {
  direction: "horizontal",
  slidesPerView: "auto",
};

@NgModule({
  declarations: [
    AppComponent,
    SignupComponent,
    SigninComponent,
    UsersComponent,
    SignupVerifyComponent,
    ServicesComponent,
    DashboardComponent,
    DividerComponent,
    WelcomeComponent,
    BillingsComponent,
    InvoicesComponent,
    PaymentComponent,
    ProductsComponent,
    AppsComponent,
    ExampleComponent,
    LandingComponent,
    ForgetPasswordComponent,
    ForgetPasswordVerifyComponent,
    RedirectComponent,
    AllVersionComponent,
    LatestVersionComponent,
    AppVersionComponent,
    GuideComponent,
    MiscI18nComponent,
    DataPipe,
    DiscountPipe,
    ValidatePromoCodePipe,
    MacGuideComponent,
    WinGuideComponent,
    OauthComponent,
    InviteComponent,
    InfoComponent,
    RewardsComponent,
    TransactionsComponent,
    SettingComponent,
    SendDialog,
    RedeemDialog,
    ReceiveDialog,
    UpdateDialog,
    PaymentsDialog,
    WindowsExportDialog,
    InvoiceCheckDialog,
    TrialInvoiceCheckDialog,
    DialogsComponent,
    PaymentSuccessComponent,
    BillingIndexComponent,
    SettingBaseComponent,
    IosGuideComponent,
    AndroidGuideComponent,
    ClashGuideComponent,
    IosGuideComponent,
    PlansComponent,
    UpdateConfirmDialog,
    UpgradeConfirmDialog,
    DeductionDialog,
    ServiceRenewComponent,
    CsComponent,
    MyAccountComponent,
    RechargeWalletComponent,
    PayStatusComponent,
    WakeComponent,
    TelegramComponent,
    PaymentStatusComponent,
    PaymentMenthodsDialog,
    MobilePaymethodComponent,
    LoadBottonComponent,
    RegisterBuyComponent,
    ToPayComponent,
    SignoutCheckDialog,
    HelpsComponent,
    HelpShowComponent,
    ArticleComponent,
    FcmComponent,
    ArticleComponent,
    MessageComponent,
    DetailsComponent,
    WinDialogComponent,
    DetailDialogComponent,
    WelfareComponent,
    PopularityComponent,
    CommunityComponent,
    VoucherComponent,
    MyVoucherComponent,
    CouponListComponent,
    SelectVoucherDialog,
    SelectVoucherComponent,
    ClaimComponent,
    TrialComponent,
    UbuntuGuideComponent,
    InvoiceHistoryComponent,
    InvoiceHistoryScrollComponent,
    PaymentMethodsComponent,
    ServiceTileComponent,
    ServiceUpgradeComponent,
    PurchaseDataComponent,
    SelectCouponComponent,
    SelectBalanceComponent,
    SelectCodeComponent,
    PaymentDetailsComponent,
    InviteRecordComponent,
    UserTabsComponent,
    OauthFormComponent,
    EmailVerificationDialog,
    TrialFailureDialog,
    PaymentSuccessPageComponent,
    TipsComponent,
    CouponsCountdownComponent,
    WaitCollectionComponent,
    AppVersionsInfoComponent,
    BillingCyclesComponent,
    DigitalTitleComponent,
    BuyProductsComponent,
    RepurchaseComponent,
    DeductionComponent,
    AlreadyRegisteredComponent,
    QrDialogComponent,
    ServerUpgradeComponent,
    UpgradeProductsComponent,
    PersonalCenterComponent,
    WaitPaymentComponent,
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    MatSidenavModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    HttpClientModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    MatProgressBarModule,
    MatSnackBarModule,
    MatTabsModule,
    MatStepperModule,
    MatSelectModule,
    MatButtonToggleModule,
    MatSlideToggleModule,
    MatMenuModule,
    MatTooltipModule,
    MatDialogModule,
    MatCardModule,
    MatTableModule,
    QRCodeModule,
    MatBottomSheetModule,
    MatProgressSpinnerModule,
    MatPaginatorModule,
    MatRadioModule,
    MatRippleModule,
    InfiniteScrollModule,
    MatCheckboxModule,
    ReactiveFormsModule,
    SwiperModule,
    MatBadgeModule,
    NgxSkeletonLoaderModule,
    WebCoreModule.forRoot({
      vapidKey: environment.vapidKey,
      production: environment.production,
      apiUrl: environment.apiUrl,
      frontDomain: environment.frontDomain,
      web: environment.web,
      stripeKey: environment.stripeKey,
      tgBot: environment.tgBot,
      firebase: environment.firebase,
    }),
  ],
  providers: [
    WebsocketService,
    ConfettiService,
    IconService,
    { provide: LOCALE_ID, useValue: "zh-Hans" },
    { provide: MatPaginatorIntl, useClass: MatPaginatorIntlCro },
    {
      provide: SWIPER_CONFIG,
      useValue: DEFAULT_SWIPER_CONFIG,
    },
    { provide: RouteReuseStrategy, useClass: SimpleReuseStrategy },
  ],
  bootstrap: [AppComponent],
})
export class AppModule { }
