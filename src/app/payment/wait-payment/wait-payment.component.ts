import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { interval, Subscription } from 'rxjs';
import { take } from 'rxjs/operators';

@Component({
    selector: 'app-wait-payment',
    templateUrl: './wait-payment.component.html',
    styleUrls: ['./wait-payment.component.less']
})
export class WaitPaymentComponent implements OnInit, OnDestroy {

    public remainingTime = 30; // 30秒倒计时
    private pollSubscription: Subscription;

    constructor(
        private router: Router,
        private route: ActivatedRoute
    ) { }

    ngOnInit() {
        this.startPolling();
    }

    ngOnDestroy() {
        if (this.pollSubscription) {
            this.pollSubscription.unsubscribe();
        }
        // 清理localStorage
        localStorage.removeItem(`invoice-paying`);
        localStorage.removeItem(`close-window`);
    }

    private startPolling() {
        // 每秒轮询一次
        this.pollSubscription = interval(1000)
            .pipe(take(30)) // 最多轮询30次
            .subscribe(() => {
                this.remainingTime--;
                // 检查localStorage
                const payUrl = localStorage.getItem(`invoice-paying`);
                if (payUrl) {
                    // 找到支付URL，清理并跳转
                    localStorage.removeItem(`invoice-paying`);
                    window.location.href = payUrl;
                    return;
                }

                // 检查是否存在email-verification-failed
                if (localStorage.getItem("close-window") && localStorage.getItem("close-window") == "true") {
                    window.close();
                    return;
                }

                // 超时处理
                if (this.remainingTime <= 0) {
                    this.pollSubscription.unsubscribe();
                    window.close();
                }
            });
    }
} 