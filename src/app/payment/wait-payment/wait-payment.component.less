.wait-payment-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    padding: 2rem;
    text-align: center;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    margin: 2rem auto;
}

.content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.countdown {
    font-size: 3rem;
    font-weight: bold;
    margin: 1.5rem 0;
    color: #FF5E5E;
    font-family: 'Libre Franklin', sans-serif;
}

.message {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.warning {
    color: #FF5E5E;
    font-size: 1.1rem;
    margin-top: 1.5rem;
    font-weight: 500;
    animation: blink 1s infinite;
}

@keyframes blink {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}