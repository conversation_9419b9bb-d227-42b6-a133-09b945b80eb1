import { Component, OnInit } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { UsersService, APIManager, ServiceService } from "@flashvpn-io/web-core";
import { MatSnackBar } from "@angular/material/snack-bar";

@Component({
  selector: "app-claim",
  templateUrl: "./claim.component.html",
  styleUrls: ["./claim.component.less"],
})
export class ClaimComponent implements OnInit {
  success = false;
  isLogged = false;
  submitted = false;
  id = 0;
  compensation: "extend" | "reset" = "extend";

  compensationList = [
    {
      id: 1,
      value: "extend",
      label: "延期当前服务7天",
    },
    {
      id: 2,
      value: "reset",
      label: "重置本月已使用流量",
    },
  ];

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private usersService: UsersService,
    private apiManager: APIManager,
    private snackBar: MatSnackBar,
    private serviceService: ServiceService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(async (params) => {
      this.isLogged = await this.usersService.isLoggedIn();
      if (this.isLogged) {
        const { id } = params;
        this.id = id;
        await this.apiManager.fetchServices().toPromise();
      }
    });
  }

  goToSignIn = () => {
    this.router.navigate(["users", "signin"], { queryParams: { url: this.router.url } });
  };
  submit = async () => {
    if (this.id.toString().length !== 8 || isNaN(Number(this.id))) {
      this.snackBar.open("请使用客服提供给您的地址", "Okay", {
        duration: 2000,
        verticalPosition: "top",
      });
      return;
    }
    const service = await this.serviceService.currentService$.getValue();
    if (!service) {
      this.snackBar.open("您没有可以供索赔的服务，如果您确实正在使用我们的产品，请联系客服手动处理", "Okay", {
        duration: 3000,
        verticalPosition: "top",
      });
      return;
    }
    this.submitted = true;
    console.log(`claim service ${service.id} for ${this.compensation}`);
    this.apiManager.claim(service.id, this.compensation, this.id.toString()).subscribe(
      (res) => {
        this.snackBar.open("索赔成功", "Okay", {
          duration: 5000,
          verticalPosition: "top",
        });
        this.submitted = false;
      },
      (err) => {
        this.snackBar.open(err.error.message, "Okay", {
          duration: 2000,
          verticalPosition: "top",
        });
        this.submitted = false;
      }
    );
  };
}
