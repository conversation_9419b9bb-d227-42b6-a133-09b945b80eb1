<div class="claim" fxFill>
  <div class="content" *ngIf="!isLogged">
    <div class="content-text">请登录后再查看索赔信息</div>
    <a class="action-button" id="wake" (click)="goToSignIn()">登录</a>
  </div>
  <div class="content" *ngIf="isLogged">
    <div class="content-text focus-in-expand">
      <div>服务中断补偿</div>
      <div>很抱歉由于突发事件导致您的服务受到影响，请选择您的赔偿：</div>
      <div fxFlex>
        <mat-radio-group [(ngModel)]="compensation">
          <mat-radio-button class="radio-button" *ngFor="let item of compensationList" [value]="item.value" i18n>{{item.label}}</mat-radio-button>
        </mat-radio-group>
      </div>
    </div>
    <button id="getReturnPackage" *ngIf="!submitted" mat-raised-button color="warn" (click)="submit()">获取补偿</button>
    <div class="botton_box" *ngIf="submitted">
      <button mat-fab color="warn" class="bounce-in-fwd">
        <mat-spinner diameter="30"></mat-spinner>
      </button>
    </div>
  </div>
</div>
