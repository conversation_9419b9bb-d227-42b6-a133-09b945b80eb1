import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { BillingIndexComponent } from "./billings/billing-index/billing-index.component";
import { BillingsComponent } from "./billings/billings.component";
import { InvoicesComponent } from "./billings/invoices/invoices.component";
import { PaymentSuccessComponent } from "./billings/payment-success/payment-success.component";
import { ClaimComponent } from "./claim/claim.component";
import { DeviceManagementComponent } from './component/device-management.component';
import { InviteRecordComponent } from "./component/invite-record.component";
import { DeviceBanComponent } from './components/device-ban/device-ban.component';
import { DeviceUnbanComponent } from './components/device-unban/device-unban.component';
import { ArticleComponent } from "./helps/article/article.component";
import { HelpsComponent } from "./helps/helpIndex/helps.component";
import { HelpShowComponent } from "./helps/helpShow/helpShow.component";
import { DetailsComponent } from "./inbox/details/details.component";
import { MessageComponent } from "./inbox/message/message.component";
import { CsComponent } from "./pages/cs.component";
import { FcmComponent } from "./pages/fcm.component";
import { MyAccountComponent } from "./pages/my-account.component";
import { MyVoucherComponent } from "./pages/my-voucher.component";
import { MyservicesComponent } from "./pages/myservices/myservices.component";
import { PayStatusComponent } from "./pages/pay-status.component";
import { PurchaseDataComponent } from "./pages/purchase-data.component";
import { RechargeWalletComponent } from "./pages/recharge-wallet.component";
import { RepurchaseComponent } from "./pages/repurchase.component";
import { ServerUpgradeComponent } from "./pages/server-upgrade.component";
import { WelfareComponent } from "./pages/welfare.component";
import { PaymentSuccessPageComponent } from "./payment-success-page/payment-success-page.component";
import { WaitPaymentComponent } from "./payment/wait-payment/wait-payment.component";
import { PlansComponent } from "./plans/plans.component";
import { ProductsComponent } from "./products/products/products.component";
import { InfoComponent } from "./rewards/info/info.component";
import { InviteComponent } from "./rewards/invite/invite.component";
import { RewardsComponent } from "./rewards/rewards/rewards.component";
import { TransactionsComponent } from "./rewards/transactions/transactions.component";
import { DashboardComponent } from "./services/dashboard/dashboard.component";
import { PaymentStatusComponent } from "./services/payment-status/payment-status.component";
import { RegisterBuyComponent } from "./services/register-buy/register-buy.component";
import { SelectVoucherComponent } from "./services/select-voucher/select-voucher.component";
import { ServiceRenewComponent } from "./services/service-renew/service-renew.component";
import { WelcomeComponent } from "./services/welcome/welcome.component";
import { SettingBaseComponent } from "./settings/setting-base/setting-base.component";
import { TelegramComponent } from "./telegram/telegram.component";
import { TrialComponent } from "./trial/trial.component";
import { ForgetPasswordVerifyComponent } from "./users/forget-password-verify/forget-password-verify.component";
import { ForgetPasswordComponent } from "./users/forget-password/forget-password.component";
import { PersonalCenterComponent } from "./users/personal-center/personal-center.component";
import { SigninComponent } from "./users/signin/signin.component";
import { SignupVerifyComponent } from "./users/signup-verify/signup-verify.component";
import { SignupComponent } from "./users/signup/signup.component";
import { UsersComponent } from "./users/users/users.component";
import { AllVersionComponent } from "./versions/all-version/all-version.component";
import { AndroidGuideComponent } from "./versions/android-guide/android-guide.component";
import { AppsComponent } from "./versions/apps/apps.component";
import { ClashGuideComponent } from "./versions/clash-guide/clash-guide.component";
import { GuideComponent } from "./versions/guide/guide.component";
import { IosGuideComponent } from "./versions/ios-guide/ios-guide.component";
import { LatestVersionComponent } from "./versions/latest-version/latest-version.component";
import { MacGuideComponent } from "./versions/mac-guide/mac-guide.component";
import { RedirectComponent } from "./versions/redirect/redirect.component";
import { UbuntuGuideComponent } from "./versions/ubuntu-guide/ubuntu-guide.component";
import { WinGuideComponent } from "./versions/win-guide/win-guide.component";
import { WakeComponent } from "./wake/wake.component";

const routes: Routes = [
  {
    path: "",
    redirectTo: "users/signin",
    component: UsersComponent,
  },
  {
    path: "users",
    component: UsersComponent,
    children: [
      {
        path: "signin",
        component: SigninComponent,
      },
      {
        path: "signup",
        component: SignupComponent,
      },
      {
        path: "signup-verify",
        component: SignupVerifyComponent,
      },
      {
        path: "forget-password",
        component: ForgetPasswordComponent,
      },
      {
        path: "forget-password-verify",
        component: ForgetPasswordVerifyComponent,
      },
      {
        path: "redirect",
        component: RedirectComponent,
      },
    ],
  },
  {
    path: "trial",
    component: TrialComponent,
  },
  { path: "service", redirectTo: "services" },
  {
    path: "services",
    children: [
      {
        path: "",
        component: DashboardComponent,
      },
      {
        path: "dashboard",
        component: DashboardComponent,
      },
      {
        path: "welcome",
        component: WelcomeComponent,
      },

    ],
  },
  {
    path: "center",
    component: PersonalCenterComponent,
  },
  {
    path: "myservices",
    component: MyservicesComponent,
  },
  {
    path: "billings",
    component: BillingsComponent,
    children: [
      {
        path: "",
        redirectTo: "index",
        pathMatch: "full",
      },
      {
        path: "index",
        component: BillingIndexComponent,
      },
      {
        path: "payment-success",
        component: PaymentSuccessComponent,
      },
    ],
  },
  {
    path: "wallet",
    component: MyAccountComponent,
    // children: [
    //   {
    //     path: 'recharge',
    //     component: RechargeComponent
    //   },
    // ]
  },
  {
    path: "recharge",
    component: RechargeWalletComponent,
  },
  {
    path: "pay-status",
    component: PayStatusComponent,
  },
  {
    path: "invoices",
    component: InvoicesComponent,
  },
  {
    path: "invoices/:id",
    component: InvoicesComponent,
  },
  {
    path: "invoices/:id/:payStatus",
    component: InvoicesComponent,
  },
  {
    path: "products",
    component: ProductsComponent,
  },
  {
    path: "helps",
    component: HelpsComponent,
    children: [
      {
        path: "",
        component: HelpShowComponent,
      },
      {
        path: "article/:id",
        component: ArticleComponent,
      },
    ],
  },
  {
    path: "apps",
    component: AppsComponent,
    children: [
      {
        path: "",
        component: LatestVersionComponent,
      },
      {
        path: "platform/:platform/all",
        component: AllVersionComponent,
      },
      {
        path: "guide",
        component: GuideComponent,
      },
      {
        path: "win-guide",
        component: WinGuideComponent,
      },
      {
        path: "mac-guide",
        component: MacGuideComponent,
      },
      {
        path: "ios-guide",
        component: IosGuideComponent,
      },
      {
        path: "android-guide",
        component: AndroidGuideComponent,
      },
      {
        path: "clash-guide",
        component: ClashGuideComponent,
      },
      {
        path: "ubuntu-guide",
        component: UbuntuGuideComponent,
      },
    ],
  },
  {
    path: "rewards",
    component: RewardsComponent,
    children: [
      {
        path: "",
        redirectTo: "info",
        pathMatch: "full",
      },
      {
        path: "info",
        component: InfoComponent,
      },
    ],
  },
  {
    path: "transactions",
    component: TransactionsComponent,
  },
  {
    path: "invite",
    component: InviteComponent,
  },
  {
    path: "settings",
    children: [
      {
        path: "",
        redirectTo: "base",
        pathMatch: "full",
      },
      {
        path: "base",
        component: SettingBaseComponent,
      },
      {
        path: "reset-password",
        component: ForgetPasswordComponent,
      },
    ],
  },
  {
    path: "plans",
    component: PlansComponent,
  },
  {
    path: "wake/:id",
    component: WakeComponent,
  },
  {
    path: "wake",
    component: WakeComponent,
  },
  {
    path: "tg",
    component: TelegramComponent,
  },
  {
    path: "fcm",
    component: FcmComponent,
  },
  {
    path: "cs",
    component: CsComponent,
  },
  {
    path: "payment-status",
    component: PaymentStatusComponent,
  },
  {
    path: "payment-success-page",
    component: PaymentSuccessPageComponent,
  },
  {
    path: "payment-methods/:buyType",
    component: ServiceRenewComponent,
    data: { keepalive: true },
  },
  {
    path: "wait-payment",
    component: WaitPaymentComponent,
  },
  {
    path: "purchase-data",
    component: PurchaseDataComponent,
  },
  {
    path: "register-buy",
    component: RegisterBuyComponent,
  },
  {
    path: "welfare",
    component: WelfareComponent,
  },
  {
    path: "invite-record",
    component: InviteRecordComponent,
  },
  {
    path: "my-voucher",
    component: MyVoucherComponent,
  },
  {
    path: "select-voucher",
    component: SelectVoucherComponent,
  },
  {
    path: "inbox/message",
    component: MessageComponent,
  },
  {
    path: "inbox/details",
    component: DetailsComponent,
  },
  {
    path: "claim/:id",
    component: ClaimComponent,
  },
  {
    path: "repurchase",
    component: RepurchaseComponent,
  },
  {
    path: "upgrade",
    component: ServerUpgradeComponent,
  },
  {
    path: "devices/ban/:id",
    component: DeviceBanComponent
  },
  {
    path: "devices/unban/:id",
    component: DeviceUnbanComponent
  },
  {
    path: "devices",
    component: DeviceManagementComponent
  },
  { path: "**", redirectTo: "" },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule { }
