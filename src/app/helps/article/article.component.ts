/*
 * @Author: ji<PERSON><PERSON>
 * @Date: 2022-12-03 10:30:26
 * @LastEditors: jiangnan
 * @LastEditTime: 2022-12-10 10:58:43
 * @Describle: 描述
 */
import { Component, OnInit, ViewEncapsulation } from "@angular/core";
import { APIManager } from "@flashvpn-io/web-core";
import { ActivatedRoute, Router } from "@angular/router";
import { Location } from "@angular/common";

@Component({
  selector: "app-article",
  templateUrl: "./article.component.html",
  encapsulation: ViewEncapsulation.None,
  styleUrls: ["./article.component.less"],
})
export class ArticleComponent implements OnInit {
  public contentHtml = "";
  public getClientSaveList = [];
  public getQuestionDataArr = [];
  public artcleId = 0;
  public sideBarShow = false;
  public timer = null;
  constructor(private apiManager: APIManager, private route: ActivatedRoute, private router: Router, private location: Location) {}

  handleSideBarShow() {
    this.sideBarShow = !this.sideBarShow;
    if (this.sideBarShow) {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.timer = setTimeout(() => {
        const top = document.getElementsByClassName("selectActive");
        const topLength = (top[0].parentNode as any).offsetTop;
        document.getElementsByClassName("sideBar")[0].scrollTop = topLength - 50;
      }, 200);
    }
  }
  helpsArticlesData(params) {
    this.apiManager.getHelpsArticles(params).subscribe(
      (value) => {
        const html = value.data.attributes.html;
        this.contentHtml = html.replace(/{{{subscription#.+}}}/g, "获取中...");
        this.apiManager.fetchServices("helpCenter").subscribe(
          (res) => {
            if (res.length) {
              this.apiManager.fetchServiceDetail(res[0], "helpCenter").subscribe(
                (service) => {
                  if (/{{{subscription#.+}}}/.test(html)) {
                    const agent = html
                      .match(/{{{subscription#.+}}}/)[0]
                      .replace("}}}", "")
                      .split("#")[1];
                    this.contentHtml = html.replace(/{{{subscription#.+}}}/g, `${service.subscriptionUrl}?agent=${agent}`);
                  }
                  // 控制滚动条
                  this.setSideBarScroll();
                },
                (error) => {
                  this.contentHtml = html.replace(/{{{subscription#.+}}}/g, "网络错误，刷新后可以查看订阅地址");
                  // 控制滚动条
                  this.setSideBarScroll();
                }
              );
            } else {
              this.contentHtml = html.replace(/{{{subscription#.+}}}/g, "购买服务后可以查看订阅地址");
              // 控制滚动条
              this.setSideBarScroll();
            }
          },
          (error) => {
            this.contentHtml = html.replace(/{{{subscription#.+}}}/g, '<a href="/users/signin">登录</a>后可以查看订阅地址');
            // 控制滚动条
            this.setSideBarScroll();
          }
        );
      },
      (error) => {
        console.log(`error->`, error);
      }
    );
  }

  setSideBarScroll() {
    const wid = window.innerWidth || document.documentElement.clientWidth;
    const top = document.getElementsByClassName("selectActive");
    if (wid > 650 && top) {
      const topLength = (top[0].parentNode as any).offsetTop;
      const sideBarDom = document.getElementsByClassName("sideBar")[0];
      sideBarDom.scrollTop = topLength - 40;
    }
  }

  switchDetail(item, type) {
    document.getElementsByClassName("drawer-content")[0].scrollTop = 0;
    document.getElementsByClassName("mat-drawer-content")[0].scrollTop = 0;
    const wid = window.innerWidth || document.documentElement.clientWidth;
    if (wid < 650) {
      this.sideBarShow = false;
    }
    if (type === "article") {
      // 取articleId
      this.router.navigate([`/helps/article/${item.articleId}`]);
    } else {
      // 取id
      this.router.navigate([`/helps/article/${item.id}`]);
    }
  }

  caculteWid() {
    const wid = window.innerWidth || document.documentElement.clientWidth;
    const hid = window.innerHeight || document.documentElement.clientHeight;
    if (wid > 650) {
      this.sideBarShow = true;
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.timer = setTimeout(() => {
        const sideBarDom = document.getElementsByClassName("sideBar")[0] as any;
        sideBarDom.style.height = hid - 80 + "px";
      });
    } else {
      this.sideBarShow = false;
    }
  }

  async ngOnInit() {
    this.caculteWid();
    window.onresize = () => {
      this.caculteWid();
    };
    this.getClientSaveList = JSON.parse(localStorage.getItem("flash_clientSaveList"));
    this.getQuestionDataArr = JSON.parse(localStorage.getItem("flash_questionDataArr"));
    this.route.params.subscribe((res) => {
      this.artcleId = res.id;
      this.helpsArticlesData(res);
    });
  }

  goBack() {
    this.location.back();
  }
}
