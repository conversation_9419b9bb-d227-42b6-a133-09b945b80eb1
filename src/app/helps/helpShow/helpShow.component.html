<!--
 * @Author: ji<PERSON><PERSON>
 * @Date: 2022-12-03 10:41:13
 * @LastEditors: jiangnan
 * @LastEditTime: 2022-12-04 09:40:39
 * @Describle: 描述
-->
<style type="text/css">
    .appVersion {
        min-height: 10px;
    }
    
    .appBox {
        margin: 10px 0 0 0;
        max-width: 414px;
        width: 100%;
        font-size: 16px;
    }
    
    .client-title {
        font-size: 18px;
        font-weight: 500;
        padding-bottom: 5px;
    }
    
    .helpShow-header {
        font-weight: 600;
        font-size: 18px;
    }
    
    .helps-question {
        padding-inline-start: 0;
        font-size: 16px;
        padding-bottom: 30px;
    }
    
    .helps-question li {
        list-style: none;
        line-height: 32px;
        cursor: pointer;
    }
    
    .client-subTitle {
        cursor: pointer;
    }
    
    @media screen and (max-width: 599px) {
        .appBox {
            max-width: 100%;
            width: 100%;
        }
    }
</style>
<div class="helpShow-header">客户端使用教程</div>
<div fxLayout="row wrap" fxLayoutGap="10px">
    <div *ngFor="let client of clientDataArr" class="appBox">
        <div fxLayout="column" class="appVersion">
            <div *ngIf="!simpleMode" class="appBox" fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="start center">
                <div class="highlighted-button">
                    <mat-icon color="primary" [svgIcon]="client.icon"></mat-icon>
                </div>
                <div class="client-title">{{client.attributes.name}}</div>
            </div>
            <div>
                <p class="client-subTitle" (click)="switchDetail(child,'article')" *ngFor="let child of client.subTitleArr">{{child.title}}</p>
            </div>

            <br/>
            <!-- <mat-divider *ngIf="!simpleMode"></mat-divider> -->
        </div>
    </div>
    <mat-progress-bar *ngIf="submitted" mode="indeterminate"></mat-progress-bar>
</div>
<div class="helpShow-header" style="padding-top: 25px;">常见问题</div>
<ul class="helps-question">
    <li *ngFor="let question of questionDataArr" (click)="switchDetail(question,'question')">{{question.title}}</li>
</ul>