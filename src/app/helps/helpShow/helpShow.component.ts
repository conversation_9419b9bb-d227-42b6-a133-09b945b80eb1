/*
 * @Author: ji<PERSON><PERSON>
 * @Date: 2022-12-03 10:30:26
 * @LastEditors: jiangnan
 * @LastEditTime: 2022-12-08 21:49:14
 * @Describle: 描述
 */
import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { APIManager } from "@flashvpn-io/web-core";

@Component({
  selector: "app-helpShow",
  templateUrl: "./helpShow.component.html",
  styleUrls: ["./helpShow.component.css"],
})
export class HelpShowComponent implements OnInit {
  public submitted = false;
  public simpleMode = false;
  public clientDataArr = [];
  public questionClassArr = [];
  public questionDataArr = [];
  constructor(private apiManager: APIManager, private router: Router) {}

  helpsCategoriesData() {
    this.submitted = true;
    this.apiManager.getHelpsCategories().subscribe(
      (value) => {
        value.data.forEach((el) => {
          if (el.attributes.slug === "client") {
            el.attributes.subcategories.data.forEach((element) => {
              if (element.attributes.slug === "iOS") {
                element.icon = "ios";
              } else if (element.attributes.slug === "macOS") {
                element.icon = "macos";
              } else if (element.attributes.slug === "browser") {
                element.icon = "chrome";
              } else if (element.attributes.slug === "router") {
                element.icon = "bars";
              } else if (element.attributes.slug === "linux") {
                element.icon = "systemProxy";
              } else if (!element.attributes.slug) {
                element.icon = "bars";
              } else {
                element.icon = element.attributes.slug;
              }

              // if(element.attributes.name.indexOf('iOS')!==-1){
              //   element['icon'] = 'ios'
              // }else if(element.attributes.name.indexOf('Windows')!==-1){
              //   element['icon'] = 'windows'
              // }else if(element.attributes.name.indexOf('Android')!==-1){
              //   element['icon'] = 'android'
              // }else if(element.attributes.name.indexOf('Mac OX')!==-1){
              //   element['icon'] = 'macos'
              // }else if(element.attributes.name.indexOf('浏览器')!==-1){
              //   element['icon'] = 'chrome'
              // }else if(element.attributes.name.indexOf('Linux')!==-1|| element.attributes.name.indexOf('路由器')!==-1){
              //   element['icon'] = 'systemProxy'
              // }else{
              //   element['icon'] = 'bars'
              // }
              element = { ...element, ...element.attributes };
            });
            this.clientDataArr = el.attributes.subcategories.data || [];
          }
          if (el.attributes.slug === "question") {
            this.questionClassArr = el.attributes.subcategories.data || [];
          }
          this.submitted = false;
        });
        // 调用
        this.helpsArticlesData();
      },
      (error) => {
        this.submitted = false;
        console.log(`error->`, error);
      }
    );
  }

  helpsArticlesData() {
    const clientSaveList = [];
    this.questionDataArr = [];
    this.apiManager.getHelpsArticles({}).subscribe(
      (value) => {
        const valueData = JSON.parse(JSON.stringify(value.data));
        this.clientDataArr.forEach((el) => {
          el.subTitleArr = [];
          valueData.forEach((second) => {
            if (el.id === second.attributes.subcategory.data.id) {
              el.subTitleArr.push({ title: second.attributes.title, html: second.attributes.content, articleId: second.id });
              clientSaveList.push({ title: second.attributes.title, html: second.attributes.content, articleId: second.id });
            }
          });
        });
        // 常见问题：
        this.questionClassArr.forEach((qes) => {
          valueData.forEach((second) => {
            if (qes.id === second.attributes.subcategory.data.id) {
              this.questionDataArr.push({ ...second.attributes, ...{ id: second.id } });
            }
          });
        });
        localStorage.setItem("flash_clientSaveList", JSON.stringify(clientSaveList));
        localStorage.setItem("flash_questionDataArr", JSON.stringify(this.questionDataArr));
      },
      (error) => {
        console.log(`error->`, error);
      }
    );
  }

  switchDetail(item, type) {
    if (type === "article") {
      // 取articleId
      this.router.navigate([`/helps/article/${item.articleId}`]);
    } else {
      // 取id
      this.router.navigate([`/helps/article/${item.id}`]);
    }
    // console.log("item==>",item);
  }

  ngOnInit() {
    this.helpsCategoriesData();
  }
}
