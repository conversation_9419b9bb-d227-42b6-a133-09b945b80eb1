import { Component, OnInit } from "@angular/core";
import { APIManager } from "@flashvpn-io/web-core";
import { AppService } from "src/app/app.service";
import { Transaction } from "@flashvpn-io/web-core";

@Component({
  selector: "app-transactions",
  templateUrl: "./transactions.component.html",
  styleUrls: ["./transactions.component.css"],
})
export class TransactionsComponent implements OnInit {
  public submitted = false;
  transactions: Transaction[] = [];
  displayedColumns = ["date", "amount", "context"];
  constructor(private apiManager: APIManager, public appService: AppService) {}

  ngOnInit(): void {
    this.apiManager.fetchTransactions().subscribe(
      (value) => (this.transactions = value),
      (res) => this.appService.snackUp(res?.error?.message),
      () => (this.submitted = false)
    );
  }
}
