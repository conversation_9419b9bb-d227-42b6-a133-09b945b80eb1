<style>
  table {
    width: 100%;
    box-shadow: none;
  }
  :host ::ng-deepth.mat-header-cell, :host ::ng-deep td.mat-cell {
    text-align: center;
  }
  :host ::ng-deep th.mat-header-cell:first-of-type {
    text-align: left;
  }
  :host ::ng-deep td.mat-cell:first-of-type {
    text-align: left;
  }
  :host ::ng-deep th.mat-header-cell:last-of-type {
    text-align: right;
  }
  :host ::ng-deep td.mat-cell:last-of-type {
    text-align: right;
  }
</style>
<section>

  <table mat-table [dataSource]="transactions" class="mat-elevation-z8">
    <!-- Name Column -->
    <ng-container matColumnDef="date">
      <th mat-header-cell *matHeaderCellDef i18n>Date</th>
      <td mat-cell *matCellDef="let element"> {{element.createdAt | date: 'yyyy-MM-dd' }} </td>
    </ng-container>

    <!-- Weight Column -->
    <ng-container matColumnDef="amount">
      <th mat-header-cell *matHeaderCellDef i18n>Amount</th>
      <td mat-cell *matCellDef="let element">{{element.direction === '1' ? '+': '-'}} {{element.amount}} </td>
    </ng-container>

    <!-- Symbol Column -->
    <ng-container matColumnDef="context">
      <th mat-header-cell *matHeaderCellDef i18n>Context</th>
      <td mat-cell *matCellDef="let element">
        <span *ngIf="element.context.hasOwnProperty('reward')">{{appService.translate(element.context['reward']['context']['campaign']['name'])}}</span>
        <span *ngIf="element.context.hasOwnProperty('sender')" i18n>Transfer</span>
        <span *ngIf="element.context.hasOwnProperty('redeem')" i18n >Redeem</span>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  </table>
</section>
