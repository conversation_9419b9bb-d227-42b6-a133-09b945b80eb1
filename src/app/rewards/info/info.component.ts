import { Component, OnInit, Inject, LOCALE_ID } from "@angular/core";
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { APIManager } from "@flashvpn-io/web-core";
import { UsersService } from "@flashvpn-io/web-core";
import { AppService } from "src/app/app.service";
import { AFTER_RELOAD_FUND, AFTER_TRANSFER_FUND, NotificationService } from "@flashvpn-io/web-core";
import { WalletsService } from "@flashvpn-io/web-core";
import { lookupService } from "dns";
import { ancestorWhere } from "tslint";
import { DialogsService } from "../../utils/dialogs/dialogs.service";
import { environment } from "../../../environments/environment";
import { Wallet } from "@flashvpn-io/web-core";
import { Campaign } from "@flashvpn-io/web-core";

@Component({
  selector: "app-info",
  templateUrl: "./info.component.html",
  styleUrls: ["./info.component.css"],
})
export class InfoComponent implements OnInit {
  public wallet?: Wallet = null;
  public campaigns: Campaign[] = [];
  public inviteCampaign?: Campaign = null;
  public submitted = false;
  constructor(
    public dialog: MatDialog,
    private apiManager: APIManager,
    private usersService: UsersService,
    public appService: AppService,
    public walletService: WalletsService,
    private notificationService: NotificationService,
    public dialogsService: DialogsService,
    @Inject(LOCALE_ID) public localeId: string
  ) { }

  async ngOnInit(): Promise<void> {
    const user = await this.usersService.currentUser();
    if (user.inviterCode === undefined) {
      // for user who doesn't have inviter code, reload info will trigger creating one
      await this.usersService.reloadUser();
    }

    this.apiManager.fetchCampaigns().subscribe(
      (value) => {
        this.campaigns = value;
        this.inviteCampaign = this.campaigns.filter((x) => x.name === "invite").pop();
      },
      (error) => this.appService.snackUp(error?.error?.message),
      () => (this.submitted = false)
    );

    this.notificationService.register(AFTER_RELOAD_FUND).subscribe((_) => (this.wallet = this.walletService.wallet$.value));
  }

  async share() {
    const user = await this.usersService.currentUser();
    const sharingText = $localize`:@@sharing-text:FlashVPN - up to 30 days free trial, sign up here: `;
    const host = environment.apiUrl === "/" ? `https://${window.location.host}/` : environment.apiUrl;
    const url = `${sharingText} https://flashvpn.io/${this.localeId}/users/signup?inviterCode=${user.inviterCode}`;
    this.appService.copyMessage(url);
    this.appService.snackUp($localize`:@@sharing-url-copied:You have copied your sharing link, now invite your friends and earn flash points`);
  }
}
