<style>
  section {
    width: 100%;
  }

  .mat-card {
    background: #FFFFFF;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.15);
    border-radius: 10px;
  }

  .mat-card h5 {
    margin: 0;
  }

  .mat-card .points {
    margin-top: 15px;
  }

  .mat-card .points span {
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: 500;
    font-size: 24px;
    line-height: 29px;
    margin-right: 10px;
    /* identical to box height */


    color: #FF5E5E;
  }

  .mat-card .describe {
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;

    color: #333333;
  }

  .operator {
    font-family: 'Libre Franklin';
    padding: 0 10px;
    margin-top: 28px;
  }

  .operator-item {
    width: 47px;
    height: 48px;
    left: 49px;
    top: 265px;

    border-radius: 10px;
    background: #000;
  }

  .operator-text {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    margin-top: 7px;
    /* identical to box height */

    text-align: center;
    color: #000000;
  }

  .more {
    margin-top: 56px;
  }

  .more-title {
    font-family: <PERSON><PERSON> Franklin;
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 22px;
    white-space: nowrap;
    padding-right: 10px;
    /* identical to box height */


    color: #000000;
  }

  .more-block {
    height: 0;
    border: 2px solid #000000;
    width: 100%;
  }

  .outlink-title span {
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: normal;
    font-size: 18px;
    line-height: 22px;
    color: #000000;
    margin-left: 8.5px;
  }

  .outlink-describe {
    font-family: Libre Franklin;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    margin-top: 14px;

    color: #333333;

  }

  .btn-black {
    background: #000;
    color: #fff;
  }

  mat-divider {
    width: 100%;
  }

</style>
<div fxFill fxLayout="column" fxLayoutAlign="start center">
  <section>
    <mat-card>
      <div class="card-header" fxLayoutAlign="space-between center">
        <h5 i18n>Flash Points</h5>
        <div routerLink="/transactions">
          <svg width="21" height="18" viewBox="0 0 21 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd"
                  d="M3 9C3 4.03 7.03 0 12 0C16.97 0 21 4.03 21 9C21 13.97 16.97 18 12 18C9.51 18 7.27 16.99 5.64 15.36L7.06 13.94C8.32 15.21 10.07 16 12 16C15.87 16 19 12.87 19 9C19 5.13 15.87 2 12 2C8.13 2 5 5.13 5 9H8L3.96 13.03L3.89 12.89L0 9H3ZM11 10V5H12.5V9.15L16.02 11.24L15.25 12.52L11 10Z"
                  fill="#333333" />
          </svg>
        </div>

      </div>
      <div class="points">
        <span *ngIf="wallet">{{wallet?.balance}}</span>
        <span *ngIf="!wallet">--</span>
        <span>Pts</span>
      </div>
      <p class="describe" i18n>You can redeem your Flash Points for data, or send and receive among friends</p>
    </mat-card>
  </section>
  <section fxLayoutAlign="space-between center" class="operator">
    <div fxLayout="column" fxLayoutAlign="center center" (click)="dialogsService.openDialog('redeem')">
      <span fxLayoutAlign="center center" class="operator-item" style="background: #FFBD2F;">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M19.25 5.5C19.6297 5.5 19.9435 5.78215 19.9932 6.14823L20 6.25V14.75C20 16.483 18.6435 17.8992 16.9344 17.9949L16.75 18H4.061L4.78033 18.7197C5.0466 18.9859 5.0708 19.4026 4.85295 19.6962L4.78033 19.7803C4.51406 20.0466 4.0974 20.0708 3.80379 19.8529L3.71967 19.7803L1.71967 17.7803L1.65627 17.7083C1.65382 17.7051 1.6514 17.7019 1.649 17.6987L1.71967 17.7803C1.68262 17.7433 1.65025 17.7033 1.62257 17.6612C1.60941 17.641 1.59703 17.62 1.58567 17.5984C1.58084 17.5893 1.57648 17.5806 1.5723 17.5718C1.55956 17.545 1.54805 17.5165 1.5383 17.4873C1.53604 17.4803 1.53412 17.4742 1.53227 17.4682C1.51135 17.3997 1.5 17.3262 1.5 17.25C1.5 17.2116 1.50288 17.1739 1.50845 17.137C1.51025 17.1255 1.51231 17.1138 1.51464 17.1021C1.51959 17.0771 1.52576 17.0528 1.53309 17.029C1.53671 17.0174 1.54074 17.0053 1.54509 16.9933C1.55357 16.97 1.56288 16.948 1.57317 16.9265C1.57867 16.9149 1.58497 16.9025 1.59163 16.8903C1.60487 16.8663 1.61898 16.8436 1.63422 16.8217C1.63814 16.816 1.64254 16.8099 1.64705 16.8038C1.67217 16.7706 1.69507 16.7443 1.71967 16.7197L3.71967 14.7197C4.01256 14.4268 4.48744 14.4268 4.78033 14.7197C5.0466 14.9859 5.0708 15.4026 4.85295 15.6962L4.78033 15.7803L4.06 16.5H16.75C17.6682 16.5 18.4212 15.7929 18.4942 14.8935L18.5 14.75V6.25C18.5 5.83579 18.8358 5.5 19.25 5.5ZM16.1962 0.147052L16.2803 0.21967L18.2803 2.21967C18.3058 2.24512 18.3294 2.27239 18.351 2.30126L18.2803 2.21967C18.3174 2.25672 18.3498 2.29669 18.3774 2.33883C18.3906 2.35898 18.403 2.37997 18.4143 2.40158C18.4192 2.4107 18.4235 2.41943 18.4277 2.42823C18.4404 2.45502 18.4519 2.48348 18.4617 2.51274C18.464 2.51968 18.4659 2.52575 18.4677 2.53184C18.4886 2.60032 18.5 2.67384 18.5 2.75C18.5 2.78839 18.4971 2.82611 18.4916 2.86295C18.4901 2.8725 18.4884 2.88219 18.4865 2.89186C18.4814 2.91897 18.4748 2.94527 18.4669 2.97098C18.4633 2.98264 18.4593 2.99469 18.4549 3.00665C18.4464 3.02995 18.4371 3.05202 18.4268 3.07352C18.4213 3.08512 18.415 3.09746 18.4084 3.10965C18.3951 3.13367 18.381 3.15639 18.3658 3.17826C18.3619 3.18401 18.3575 3.19014 18.3529 3.19621C18.3292 3.22765 18.3078 3.25237 18.285 3.2756L18.2803 3.28033L16.2803 5.28033C15.9874 5.57322 15.5126 5.57322 15.2197 5.28033C14.9534 5.01406 14.9292 4.5974 15.1471 4.30379L15.2197 4.21967L15.938 3.5H3.25C2.33183 3.5 1.57881 4.20711 1.5058 5.10647L1.5 5.25V13.75C1.5 14.1642 1.16421 14.5 0.75 14.5C0.370304 14.5 0.0565091 14.2178 0.00684667 13.8518L0 13.75V5.25C0 3.51697 1.35645 2.10075 3.06558 2.00514L3.25 2H15.939L15.2197 1.28033C14.9534 1.01406 14.9292 0.5974 15.1471 0.303788L15.2197 0.21967C15.4859 -0.0465967 15.9026 -0.0708026 16.1962 0.147052ZM10 6C12.2091 6 14 7.79086 14 10C14 12.2091 12.2091 14 10 14C7.79086 14 6 12.2091 6 10C6 7.79086 7.79086 6 10 6ZM10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5Z"
            fill="#212121" />
        </svg>
      </span>
      <span class="operator-text" i18n>Redeem</span>
    </div>
    <div fxLayout="column" fxLayoutAlign="center center" (click)="dialogsService.openDialog('send')">
      <span fxLayoutAlign="center center" class="operator-item">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M11 8.83L11 15L9 15L9 8.83L6.41 11.41L5 10L10 5L15 10L13.59 11.41L11 8.83Z" fill="white" />
          <path fill-rule="evenodd" clip-rule="evenodd"
                d="M10 20C4.47715 20 0 15.5228 0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20ZM10 18C5.58172 18 2 14.4183 2 10C2 5.58172 5.58172 2 10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18Z"
                fill="white" />
        </svg>
      </span>
      <span class="operator-text" i18n>Send</span>
    </div>
    <div fxLayout="column" fxLayoutAlign="center center" (click)="dialogsService.openDialog('copy', wallet)">
      <span fxLayoutAlign="center center" class="operator-item">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9 11.17V5H11V11.17L13.59 8.59L15 10L10 15L5 10L6.41 8.59L9 11.17Z" fill="white" />
          <path fill-rule="evenodd" clip-rule="evenodd"
                d="M10 0C15.5228 -2.41411e-07 20 4.47715 20 10C20 15.5228 15.5228 20 10 20C4.47715 20 2.41411e-07 15.5228 0 10C-2.41411e-07 4.47715 4.47715 2.41411e-07 10 0ZM10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10C2 5.58172 5.58172 2 10 2Z"
                fill="white" />
        </svg>
      </span>
      <span class="operator-text" i18n>Receive</span>
    </div>
  </section>
  <section class="more" fxLayoutAlign="start center">
    <div class="more-title" i18n>GET MORE POINTS</div>
    <div class="more-block">

    </div>
  </section>
  <section class="share outline" style="margin-top: 45px" fxLayout="column" fxLayoutAlign="start start">
    <div class="outlink-title" fxLayoutAlign="start center">
      <svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M14 0C15.934 0 17.5018 1.5678 17.5018 3.50177C17.5018 5.43574 15.934 7.00354 14 7.00354C12.97 7.00354 12.0439 6.55887 11.4032 5.85104L6.84455 8.45547C6.94786 8.78587 7.00354 9.13732 7.00354 9.50177C7.00354 9.86622 6.94786 10.2177 6.84455 10.5481L11.4023 13.1535C12.043 12.4451 12.9695 12 14 12C15.934 12 17.5018 13.5678 17.5018 15.5018C17.5018 17.4357 15.934 19.0035 14 19.0035C12.066 19.0035 10.4982 17.4357 10.4982 15.5018C10.4982 15.1373 10.5539 14.7859 10.6572 14.4555L6.09951 11.85C5.45873 12.5584 4.53223 13.0035 3.50177 13.0035C1.5678 13.0035 0 11.4357 0 9.50177C0 7.56779 1.5678 6 3.50177 6C4.53173 6 5.45784 6.44466 6.09859 7.15249L10.6572 4.54807C10.5539 4.21767 10.4982 3.86622 10.4982 3.50177C10.4982 1.5678 12.066 0 14 0ZM14 13.5C12.8945 13.5 11.9982 14.3962 11.9982 15.5018C11.9982 16.6073 12.8945 17.5035 14 17.5035C15.1055 17.5035 16.0018 16.6073 16.0018 15.5018C16.0018 14.3962 15.1055 13.5 14 13.5ZM3.50177 7.5C2.39622 7.5 1.5 8.39622 1.5 9.50177C1.5 10.6073 2.39622 11.5035 3.50177 11.5035C4.60732 11.5035 5.50354 10.6073 5.50354 9.50177C5.50354 8.39622 4.60732 7.5 3.50177 7.5ZM14 1.5C12.8945 1.5 11.9982 2.39622 11.9982 3.50177C11.9982 4.60732 12.8945 5.50354 14 5.50354C15.1055 5.50354 16.0018 4.60732 16.0018 3.50177C16.0018 2.39622 15.1055 1.5 14 1.5Z"
          fill="#212121" />
      </svg>
      <span i18n>Invite friends and earn point</span>
    </div>
    <div class="outlink-describe">You'll receive <span style="color: #FF5E5E;">{{inviteCampaign?.points}} Pts</span> when the person you invite signs up.</div>
    <div class="outlink-describe">And they’ll also get <span style="color: #FF5E5E;">{{inviteCampaign?.points}} Pts</span> as well.</div>
    <button mat-raised-button class="btn-black" style="margin-top: 30px;" (click)="share()" i18n>Copy Link and Share</button>
  </section>
<!--  <mat-divider style="margin-top: 44px;"></mat-divider>-->
<!--  <section class="outline" style="margin-top: 27px" fxLayout="column" fxLayoutAlign="start start">-->
<!--    <div class="outlink-title" fxLayoutAlign="start center">-->
<!--      <svg width="20" height="14" viewBox="0 0 20 14" fill="none" xmlns="http://www.w3.org/2000/svg">-->
<!--        <path-->
<!--          d="M12.9958 0C16.8618 0 19.9958 3.13401 19.9958 7C19.9958 10.7855 16.991 13.8691 13.2364 13.9959L12.9958 14H7C3.13401 14 0 10.866 0 7C0 3.21455 3.00478 0.130901 6.75935 0.00405884L7 0H12.9958ZM12.9958 1.5H7C3.96243 1.5 1.5 3.96243 1.5 7C1.5 9.96348 3.84378 12.3795 6.77879 12.4956L7 12.5H12.9958C16.0333 12.5 18.4958 10.0376 18.4958 7C18.4958 4.03652 16.152 1.62046 13.217 1.50437L12.9958 1.5ZM5.99788 4C6.4121 4 6.74788 4.33579 6.74788 4.75L6.747 6.248L8.24788 6.24875C8.6621 6.24875 8.99788 6.58453 8.99788 6.99875C8.99788 7.41296 8.6621 7.74875 8.24788 7.74875L6.747 7.748L6.74788 9.25C6.74788 9.66421 6.4121 10 5.99788 10C5.58367 10 5.24788 9.66421 5.24788 9.25L5.247 7.748L3.74788 7.74875C3.33367 7.74875 2.99788 7.41296 2.99788 6.99875C2.99788 6.58453 3.33367 6.24875 3.74788 6.24875L5.247 6.248L5.24788 4.75C5.24788 4.33579 5.58367 4 5.99788 4ZM12.7479 7.5C13.4382 7.5 13.9979 8.05964 13.9979 8.75C13.9979 9.44036 13.4382 10 12.7479 10C12.0575 10 11.4979 9.44036 11.4979 8.75C11.4979 8.05964 12.0575 7.5 12.7479 7.5ZM14.7479 4C15.4382 4 15.9979 4.55964 15.9979 5.25C15.9979 5.94036 15.4382 6.5 14.7479 6.5C14.0575 6.5 13.4979 5.94036 13.4979 5.25C13.4979 4.55964 14.0575 4 14.7479 4Z"-->
<!--          fill="#212121" />-->
<!--      </svg>-->

<!--      <span>-->
<!--        Solve a puzzle-->
<!--      </span>-->
<!--    </div>-->
<!--    <div class="outlink-describe">-->
<!--      Receive <span style="color: #FF5E5E;">1 Pts</span> by solve a Hcaptcha each time. And you can play infinite times.-->
<!--    </div>-->
<!--    <button mat-raised-button class="btn-black" style="margin-top: 14px;">Play Now</button>-->
<!--  </section>-->
<!--  <mat-divider style="margin-top: 26.5px;"></mat-divider>-->
<!--  <section class="outline" style="margin-top: 27px" fxLayout="column" fxLayoutAlign="start start">-->
<!--    <div class="outlink-title" fxLayoutAlign="start center">-->
<!--      <svg width="20" height="14" viewBox="0 0 20 14" fill="none" xmlns="http://www.w3.org/2000/svg">-->
<!--        <path-->
<!--          d="M12.9958 0C16.8618 0 19.9958 3.13401 19.9958 7C19.9958 10.7855 16.991 13.8691 13.2364 13.9959L12.9958 14H7C3.13401 14 0 10.866 0 7C0 3.21455 3.00478 0.130901 6.75935 0.00405884L7 0H12.9958ZM12.9958 1.5H7C3.96243 1.5 1.5 3.96243 1.5 7C1.5 9.96348 3.84378 12.3795 6.77879 12.4956L7 12.5H12.9958C16.0333 12.5 18.4958 10.0376 18.4958 7C18.4958 4.03652 16.152 1.62046 13.217 1.50437L12.9958 1.5ZM5.99788 4C6.4121 4 6.74788 4.33579 6.74788 4.75L6.747 6.248L8.24788 6.24875C8.6621 6.24875 8.99788 6.58453 8.99788 6.99875C8.99788 7.41296 8.6621 7.74875 8.24788 7.74875L6.747 7.748L6.74788 9.25C6.74788 9.66421 6.4121 10 5.99788 10C5.58367 10 5.24788 9.66421 5.24788 9.25L5.247 7.748L3.74788 7.74875C3.33367 7.74875 2.99788 7.41296 2.99788 6.99875C2.99788 6.58453 3.33367 6.24875 3.74788 6.24875L5.247 6.248L5.24788 4.75C5.24788 4.33579 5.58367 4 5.99788 4ZM12.7479 7.5C13.4382 7.5 13.9979 8.05964 13.9979 8.75C13.9979 9.44036 13.4382 10 12.7479 10C12.0575 10 11.4979 9.44036 11.4979 8.75C11.4979 8.05964 12.0575 7.5 12.7479 7.5ZM14.7479 4C15.4382 4 15.9979 4.55964 15.9979 5.25C15.9979 5.94036 15.4382 6.5 14.7479 6.5C14.0575 6.5 13.4979 5.94036 13.4979 5.25C13.4979 4.55964 14.0575 4 14.7479 4Z"-->
<!--          fill="#212121" />-->
<!--      </svg>-->

<!--      <span>-->
<!--        Join our Telegram-->
<!--      </span>-->
<!--    </div>-->
<!--    <div class="outlink-describe">-->
<!--      Checkin our telegram each day to get <span style="color: #FF5E5E;">0.5 Pts</span>-->
<!--    </div>-->
<!--    <button mat-raised-button class="btn-black" style="margin-top: 31px;">Check In</button>-->
<!--  </section>-->
<!--  <mat-divider style="margin-top: 26.5px;margin-bottom: 26.5px"></mat-divider>-->
</div>
