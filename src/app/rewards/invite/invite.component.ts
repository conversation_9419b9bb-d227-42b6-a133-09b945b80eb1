import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-invite',
  templateUrl: './invite.component.html',
  styleUrls: ['./invite.component.css']
})
export class InviteComponent implements OnInit {
  dataSource = [{
    word: 'もう直ぐ ',
    katagana: 'もうすぐ ',
    meaning: '马上'
  }, {
    word: 'もう直ぐ ',
    katagana: 'もうすぐ ',
    meaning: '马上'
  }, {
    word: 'もう直ぐ ',
    katagana: 'もうすぐ ',
    meaning: '马上'
  }];
  displayedColumns = ['word', 'katagana', 'meaning']
  step = 1;

  constructor(
    private router: Router
  ) { }

  ngOnInit(): void {
  }

  onStep(num) {
    this.step = num;
  }
  linkTo(url) {
    this.router.navigate([url]);
  }

}
