<style>
  .invite {
    min-height: 100%;
    display: grid;
    grid-template-rows: 100px auto 170px;
    grid-template-columns: 100%;
    grid-template-areas:
      "header"
      "main"
      "footer";
  }

  .header {
    grid-area: header;
  }

  .main {
    grid-area: main;
  }

  .footer {
    grid-area: footer;
  }

  h2 {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    font-size: 36px;
    line-height: 42px;

  }

  .mat-card {
    background: #FFFFFF;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.15);
    border-radius: 10px;
    position: relative;
  }

  .get-pts {
    position: absolute;
    top: -36px;
    right: 10px;
    width: 260px;
    height: 36px;

    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    /* identical to box height */

    text-align: center;

    color: #FFFFFF;

    background: #000000;
    border-radius: 10px 10px 0px 0px;
  }

  .card-right p {
    margin: 0;
    margin-bottom: 10px;
  }

  .card-right .label {
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 15px;

    color: #888888;
  }

  .circle {
    position: relative;
  }

  .circle .percent {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 22px;
    /* identical to box height */

    text-align: center;

    color: #000000;
  }

  .words {
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    text-align: center;

    color: #000000;

  }

  .btn-black {
    background: #000;
    color: #fff;
    background: #010101;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.15);
    border-radius: 10px;
  }

  .setup h5 {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    font-size: 24px;
    line-height: 28px;
    color: #000000;
    margin: 0;
    margin-right: 10px;
  }

  .setup span {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 16px;
    padding-top: 5px;

    color: #000000;
  }

  .setup-describe p {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 16px;
    margin: 0;

    color: #949494;
  }

  .bottom {
    width: 100%;
    padding: 30px 0;
  }

  table {
    width: 100%;
    box-shadow: none;
  }

  :host ::ng-deep table tbody tr:nth-child(2n) td:first-child {
    position: relative;
  }
  :host ::ng-deep table tbody tr:nth-child(2n)  td:first-child::after {
    position: absolute;
    content: ' ';
    width: 4px;
    height: 100%;
    background: #FFBD2F;
    left: 0;
    top: 0;
  }
  :host ::ng-deepth.mat-header-cell, :host ::ng-deep td.mat-cell {
    text-align: center;
  }
  :host ::ng-deep th.mat-header-cell:first-of-type {
    text-align: left;
  }
  :host ::ng-deep td.mat-cell:first-of-type {
    text-align: left;
  }
  :host ::ng-deep th.mat-header-cell:last-of-type {
    text-align: right;
  }
  :host ::ng-deep td.mat-cell:last-of-type {
    text-align: right;
  }
  
</style>
<div class="invite">
  <section class="header" fxLayoutAlign="space-between center">
    <h2>KIKUNI</h2>
    <svg (click)="linkTo('/setting')" width="24" height="25" viewBox="0 0 24 25" fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd"
        d="M20.6425 12.7451C20.6425 13.1466 20.607 13.5244 20.5598 13.9023L23.0543 15.8506C23.279 16.0278 23.3381 16.3466 23.1962 16.6064L20.8317 20.692C20.7253 20.881 20.5243 20.9872 20.3233 20.9872C20.2524 20.9872 20.1814 20.9754 20.1105 20.9518L17.1667 19.771C16.5519 20.2315 15.8898 20.633 15.1686 20.9282L14.7194 24.0574C14.6839 24.3408 14.4356 24.5533 14.1401 24.5533H9.41104C9.11547 24.5533 8.8672 24.3408 8.83173 24.0574L8.38247 20.9282C7.66129 20.633 6.99922 20.2433 6.38445 19.771L3.44062 20.9518C3.38151 20.9754 3.31057 20.9872 3.23963 20.9872C3.02683 20.9872 2.82584 20.881 2.71944 20.692L0.354917 16.6064C0.213046 16.3466 0.272159 16.0278 0.496789 15.8506L2.99136 13.9023C2.94407 13.5244 2.9086 13.1347 2.9086 12.7451C2.9086 12.3554 2.94407 11.9657 2.99136 11.5879L0.496789 9.63951C0.272159 9.46238 0.201223 9.14356 0.354917 8.88378L2.71944 4.79813C2.82584 4.60919 3.02683 4.50292 3.22781 4.50292C3.29875 4.50292 3.36968 4.51473 3.44062 4.53834L6.38445 5.71917C6.99922 5.25865 7.66129 4.85717 8.38247 4.56196L8.83173 1.43278C8.8672 1.14938 9.11547 0.936829 9.41104 0.936829H14.1401C14.4356 0.936829 14.6839 1.14938 14.7194 1.43278L15.1686 4.56196C15.8898 4.85717 16.5519 5.24684 17.1667 5.71917L20.1105 4.53834C20.1696 4.51473 20.2405 4.50292 20.3115 4.50292C20.5243 4.50292 20.7253 4.60919 20.8317 4.79813L23.1962 8.88378C23.3381 9.14356 23.279 9.46238 23.0543 9.63951L20.5598 11.5879C20.607 11.9657 20.6425 12.3436 20.6425 12.7451ZM18.2779 12.7451C18.2779 12.4971 18.2661 12.2491 18.2188 11.8831L18.0533 10.5487L19.1055 9.72215L20.3705 8.71845L19.5429 7.28965L18.0415 7.89187L16.7883 8.39963L15.7124 7.57305C15.2395 7.2188 14.7666 6.94721 14.2582 6.73466L13.005 6.22691L12.8159 4.89258L12.5912 3.29847H10.9479L10.7114 4.89258L10.5223 6.22691L9.26908 6.73466C8.78436 6.9354 8.29963 7.2188 7.79126 7.59667L6.72722 8.39963L5.49767 7.90368L3.99619 7.30146L3.16861 8.73026L4.44545 9.72215L5.49767 10.5487L5.33215 11.8831C5.29668 12.2373 5.27304 12.5089 5.27304 12.7451C5.27304 12.9812 5.29668 13.2528 5.33215 13.6189L5.49767 14.9532L4.44545 15.7798L3.16861 16.7717L3.99619 18.2005L5.49767 17.5982L6.75087 17.0905L7.82672 17.9171C8.29963 18.2713 8.77253 18.5429 9.28091 18.7555L10.5341 19.2632L10.7233 20.5975L10.9479 22.1917H12.6031L12.8395 20.5975L13.0287 19.2632L14.2819 18.7555C14.7666 18.5547 15.2513 18.2713 15.7597 17.8935L16.8237 17.0905L18.0533 17.5864L19.5548 18.1887L20.3824 16.7599L19.1055 15.768L18.0533 14.9414L18.2188 13.6071C18.2543 13.2528 18.2779 12.993 18.2779 12.7451ZM11.7755 8.02177C9.16266 8.02177 7.04642 10.1354 7.04642 12.7451C7.04642 15.3547 9.16266 17.4684 11.7755 17.4684C14.3882 17.4684 16.5045 15.3547 16.5045 12.7451C16.5045 10.1354 14.3882 8.02177 11.7755 8.02177ZM9.41089 12.7451C9.41089 14.044 10.4749 15.1067 11.7754 15.1067C13.0759 15.1067 14.1399 14.044 14.1399 12.7451C14.1399 11.4462 13.0759 10.3834 11.7754 10.3834C10.4749 10.3834 9.41089 11.4462 9.41089 12.7451Z"
        fill="#333333" />
    </svg>
  </section>
  <div class="main">

    <mat-card fxLayoutAlign="start center" style="margin-top: 30px;">
      <div style="margin-right: 40px;">
        <div class="circle">
          <svg width="69" height="69" viewBox="0 0 69 69" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M34.5 2.06923C34.5 0.926426 33.5727 -0.00647009 32.4319 0.0620148C25.4195 0.483006 18.684 3.03706 13.1413 7.40657C7.05556 12.2041 2.76229 18.9109 0.953238 26.4461C-0.855813 33.9814 -0.0752821 41.9062 3.16906 48.9438C6.4134 55.9813 11.9326 61.7216 18.8373 65.2397C25.742 68.7579 33.6302 69.8489 41.2306 68.3371C48.8311 66.8253 55.7012 62.7986 60.734 56.906C65.7668 51.0133 68.6692 43.5978 68.9734 35.8545C69.2505 28.802 67.3577 21.8516 63.5765 15.931C62.9614 14.9679 61.6616 14.766 60.737 15.4377C59.8125 16.1094 59.6142 17.4001 60.2214 18.3682C63.4605 23.5327 65.0787 29.5686 64.8381 35.692C64.5704 42.5065 62.0162 49.0324 57.5871 54.2182C53.158 59.404 47.112 62.9477 40.4232 64.2782C33.7345 65.6086 26.7926 64.6484 20.7161 61.5523C14.6397 58.4562 9.78254 53.4045 6.92737 47.2112C4.07221 41.0178 3.38531 34.0436 4.97735 27.4122C6.5694 20.7809 10.3477 14.8786 15.7034 10.6566C20.5159 6.86269 26.3502 4.624 32.4323 4.20893C33.5724 4.13111 34.5 3.21203 34.5 2.06923Z"
              fill="#FFBD2F" />
          </svg>
          <span class="percent" fxLayoutAlign="center center">
            -- %
          </span>
        </div>
        <p class="words">-- / -- Words</p>

      </div>
      <div fxLayout="column" class="card-right">
        <p class="label">New Words</p>
        <p class="">--</p>
        <p class="label">Reviews</p>
        <p class="">--</p>
      </div>
      <span class="get-pts" fxLayoutAlign="center center" (click)="linkTo('/setting')" *ngIf="step == 3">
        Get Flash Pts to redeem data
      </span>
    </mat-card>
    <div *ngIf="step == 1">

      <section fxLayout="column" style="margin-top: 81px;">
        <div class="setup" fxLayoutAlign="start center">
          <h5>Setup</h5>
          <span>Invitation code: ED185D58</span>
        </div>
        <div class="setup-describe" style="margin-top: 24px;">
          <p>KIKUNI is free, guaranteed forever</p>
          <p style="margin-top: 5px;">No need to sign up or whatsoever</p>
        </div>
      </section>
      <button (click)="onStep(2)" mat-raised-button class="btn-black" style="margin-top: 45px; width: 100%">One lick to
        set up in a few seconds</button>
    </div>
    <div *ngIf="step != 1">
      <section style="margin-top: 59px;">

        <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">
          <!-- Name Column -->
          <ng-container matColumnDef="word">
            <th mat-header-cell *matHeaderCellDef> Word </th>
            <td mat-cell *matCellDef="let element"> {{element.word}} </td>
          </ng-container>

          <!-- Weight Column -->
          <ng-container matColumnDef="katagana">
            <th mat-header-cell *matHeaderCellDef> Katagana </th>
            <td mat-cell *matCellDef="let element"> {{element.katagana}} </td>
          </ng-container>

          <!-- Symbol Column -->
          <ng-container matColumnDef="meaning">
            <th mat-header-cell *matHeaderCellDef> Meaning </th>
            <td mat-cell *matCellDef="let element"> {{element.meaning}} </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </section>

    </div>
  </div>
  <section fxLayoutAlign="space-around end" class="bottom footer">
    <div style="padding-bottom: 10px;">
      <svg width="27" height="28" viewBox="0 0 27 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M23.0319 4.04466C20.5835 1.60076 17.2233 0.0838623 13.4916 0.0838623C6.02814 0.0838623 0 6.11776 0 13.5674C0 21.0171 6.02814 27.051 13.4916 27.051C19.7899 27.051 25.0413 22.7531 26.5441 16.9383H23.0319C21.6473 20.8654 17.8987 23.6801 13.4916 23.6801C7.90244 23.6801 3.36023 19.1463 3.36023 13.5674C3.36023 7.98861 7.90244 3.45476 13.4916 3.45476C16.2946 3.45476 18.7936 4.61772 20.6173 6.45485L15.1801 11.882H27V0.0838623L23.0319 4.04466Z"
          fill="#333333" />
      </svg>

    </div>
    <div (click)="onStep(3)">
      <svg width="81" height="81" viewBox="0 0 81 81" fill="none" xmlns="http://www.w3.org/2000/svg" *ngIf="step != 3">
        <circle cx="40.5" cy="40.5" r="40.5" fill="black" />
        <path d="M40.4997 20.6471L50.8155 40.3015H30.1838L40.4997 20.6471Z" fill="#FFBD2F" />
        <path d="M40.5003 61.1471L30.1845 41.4927H50.8162L40.5003 61.1471Z" fill="#FFBD2F" />
      </svg>
      <svg width="81" height="81" viewBox="0 0 81 81" fill="none" xmlns="http://www.w3.org/2000/svg" *ngIf="step == 3">
        <circle cx="40.5" cy="40.5" r="40.5" fill="#FFBD2F" />
        <path d="M40.4997 20.6471L50.8155 40.3015H30.1838L40.4997 20.6471Z" fill="black" />
        <path d="M40.5003 61.1471L30.1845 41.4927H50.8162L40.5003 61.1471Z" fill="black" />
      </svg>



    </div>
    <div style="padding-bottom: 10px;">
      <svg width="33" height="24" viewBox="0 0 33 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd"
          d="M0 6.79495L2.95969 9.75104C7.22165 5.49426 13.0079 3.72061 18.5425 4.40051L20.3035 0.439346C13.1558 -0.831774 5.53463 1.28183 0 6.79495ZM22.0497 0.882751C21.7981 0.882751 21.5761 1.01578 21.4429 1.2227L21.3393 1.44441L13.6737 18.6637C13.437 19.0923 13.289 19.5653 13.289 20.0826C13.289 21.7232 14.6208 23.0535 16.2635 23.0535C17.6841 23.0535 18.8828 22.0484 19.164 20.7034L19.1788 20.659L22.7896 1.62178C22.7896 1.20792 22.464 0.882751 22.0497 0.882751ZM32.5564 6.79494L29.5967 9.75104C27.7913 7.94782 25.7196 6.6028 23.4998 5.68641L24.2841 1.51831C27.303 2.62685 30.1295 4.37094 32.5564 6.79494ZM23.6776 15.6632L26.6373 12.7071C25.4534 11.5247 24.1215 10.6083 22.7009 9.91357L21.887 14.2295C22.5085 14.6286 23.1152 15.1015 23.6776 15.6632ZM8.87907 15.6632L5.91937 12.7072C8.89387 9.73631 12.8451 8.33219 16.7371 8.45043L14.8429 12.7072C12.6675 13.0175 10.5513 13.993 8.87907 15.6632Z"
          fill="#333333" />
      </svg>

    </div>
  </section>
</div>
