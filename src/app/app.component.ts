import { Location } from "@angular/common";
import { Component, OnDestroy, OnInit, ViewChild } from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { MatDrawer } from "@angular/material/sidenav";
import { Event, NavigationEnd, PRIMARY_OUTLET, Router } from "@angular/router";
import {
  AFTER_USER_LOGOUT, APIManager, FcmService, NotificationService,
  OPEN_DRAWER, ServiceService, TOKEN_EXPRIED, UsersService, Utm, WINDOW_SRCOLL
} from "@flashvpn-io/web-core";
import * as _ from "lodash";
import { Subscription } from "rxjs";
import { filter } from "rxjs/operators";
import { environment } from "../environments/environment";
import { AppService } from "./app.service";
import { WinDialogComponent } from "./inbox/win-dialog/winDialog.component";
import { DialogsService } from "./utils/dialogs/dialogs.service";
import { IconService } from "./utils/icon.service";

@Component({
  selector: "app-root",
  templateUrl: "./app.component.html",
  styleUrls: ["./app.component.css"],
})
export class AppComponent implements OnInit, OnDestroy {
  // active primary component, first level router
  env = environment;
  activeModule = "";
  isWake = false;
  scrollDisabled = false;
  public showBack = false;
  @ViewChild("drawer") drawer?: MatDrawer;
  public submitted = false;
  public currentPath = "";
  public unSupportBrowserAgent = ["MQQBrowser"];
  public unSupportBrowserOs = ["iPhone"];
  public isSupportBrowser = true;
  showTrialEntry = false;
  public deliveryIPs: string[] = [];

  // Subscriptions
  private routerSubscription: Subscription;
  private tokenExpriedSubscription: Subscription;
  private logoutSubscription: Subscription;
  private drawerSubscription: Subscription;
  private deliveryIPsSubscription: Subscription;

  constructor(
    public router: Router,
    public usersService: UsersService,
    private notification: NotificationService,
    public apiManager: APIManager,
    public appService: AppService,
    public dialogsService: DialogsService,
    private location: Location,
    public dialog: MatDialog,
    public fcmService: FcmService,
    public serviceService: ServiceService,
    private iconService: IconService,
  ) {
    // Immediately subscribe to router events in constructor to catch initial navigation
    this.subscribeToRouterEvents();
  }

  private subscribeToRouterEvents(): void {
    // Subscribe to router events in a way that persists until component destruction
    this.routerSubscription = this.router.events.pipe(
      filter((event: Event): event is NavigationEnd => event instanceof NavigationEnd)
    ).subscribe(async (event: NavigationEnd) => {
      this.currentPath = event.url;
      const actualUrl = event.urlAfterRedirects || event.url;
      const urlTree = this.router.parseUrl(actualUrl);

      // set activeModule
      if (urlTree.root.children[PRIMARY_OUTLET] !== undefined) {
        const m = urlTree.root.children[PRIMARY_OUTLET].segments[0].path;

        // Handle all routes that should show a module in the UI
        if (
          _.includes(
            [
              "users",
              "services",
              "billings",
              "products",
              "apps",
              "invoices",
              "rewards",
              "transactions",
              "settings",
              "plans",
              "wallet",
              "recharge",
              "wake",
              "claim",
              "payment-status",
              "register-buy",
              "payment-methods",
              "helps",
              "tg",
              "fcm",
              "welfare",
              "invite-record",
              "my-voucher",
              "select-voucher",
              "trial",
              "purchase-data",
              "repurchase",
              "upgrade",
              "center",
            ],
            m
          )
        ) {
          this.activeModule = this.appService.translate(m);
        } else {
          this.activeModule = "";
        }

        if (_.includes(["services", "invite-record"], m) && (this.dialogsService.isMobile() || window.innerWidth <= 599)) {
          if (urlTree.root.children[PRIMARY_OUTLET].segments.length > 1) {
            const m2 = urlTree.root.children[PRIMARY_OUTLET].segments[1].path;
            this.scrollDisabled = !_.includes(["dashboard"], m2);
          } else {
            this.scrollDisabled = false;
          }
        } else {
          this.scrollDisabled = true;
        }
        this.showBack = _.includes(["register-buy", "payment-methods", "invite-record", "my-voucher", "select-voucher", "purchase-data", "upgrade"], m);
      } else {
        this.activeModule = "";
      }

      // strip app and save it
      const app = urlTree.queryParams.app;
      if (app !== undefined) {
        await this.appService.fromApp(app);
      }

      // login user if we have a token appended
      // strip auth token and save it
      const tokenFromUrl = urlTree.queryParams.token;
      const emailFromUrl = urlTree.queryParams.email;
      if (tokenFromUrl !== undefined && emailFromUrl !== undefined) {
        await this.usersService.login(tokenFromUrl, emailFromUrl);
        // clear token in the query
        delete urlTree.queryParams.token;
        delete urlTree.queryParams.email;
      }

      const utmSource = urlTree.queryParams.utm_source;
      const utmMedium = urlTree.queryParams.utm_medium;
      const utmCampaign = urlTree.queryParams.utm_campaign;
      const utmTerm = urlTree.queryParams.utm_term;
      const utmContent = urlTree.queryParams.utm_content;
      if (utmSource || utmMedium || utmCampaign || utmTerm || utmContent) {
        await this.usersService.setUtm({
          utmSource,
          utmMedium,
          utmCampaign,
          utmTerm,
          utmContent,
        });
      }

      // handle oauth redirect isNew is set by oauth redirect
      const isNew = urlTree.queryParams.isNew;
      if (isNew !== undefined) {
        if (isNew === "1") {
          const utm: Utm = {
            utmSource: utmMedium || localStorage.getItem("utmSource") || undefined,
            utmMedium: utmMedium || localStorage.getItem("utmMedium") || undefined,
            utmCampaign: utmCampaign || localStorage.getItem("utmCampaign") || undefined,
            utmTerm: utmTerm || localStorage.getItem("utmTerm") || undefined,
            utmContent: utmContent || localStorage.getItem("utmContent") || undefined,
          };
          this.apiManager.updateUTM(utm).subscribe(
            () => {
              this.usersService.removeUtm();
            },
            (error) => {
              console.log(error);
            }
          );
          await this.router.navigate(["users", "signup-success"]);
        } else {
          await this.router.navigate(["center"]);
        }
        return;
      }

      // close drawer on mobile
      if (this.drawer && !this.appService.isLarge) {
        await this.drawer.close();
      }

      this.serviceService.checkUserTrial();
    });
  }

  // 查看公告
  handleShowToldDialog() {
    this.dialog.open(WinDialogComponent);
  }

  redirectToSignin() {
    const currentPath = this.router.routerState.snapshot.url.split("?")[0];
    if (['/center', '/wallet', '/welfare', '/settings/base', '/payment-methods/service', '/recharge'].includes(currentPath)) {
      this.router.navigate(['/users/signin'], { queryParams: { redirect: currentPath } });
    }
  }

  async ngOnInit(): Promise<void> {
    // check the browser
    const userAgent = navigator.userAgent;
    for (let i = 0; i < this.unSupportBrowserAgent.length; i++) {
      if (userAgent.indexOf(this.unSupportBrowserAgent[i]) > -1 && userAgent.indexOf(this.unSupportBrowserOs[i]) > -1) {
        this.isSupportBrowser = false;
        break;
      }
    }

    // handle auth for extension
    const tokenFromDB = localStorage.getItem("token");
    const emailFromDB = localStorage.getItem("email");
    if (tokenFromDB !== null && emailFromDB !== null) {
      await this.usersService.login(tokenFromDB, emailFromDB);
      // clear token after login
      localStorage.removeItem("token");
      localStorage.removeItem("email");
      await this.router.navigate(["center"]);
    }

    if (!this.usersService.isLoggedIn()) {
      this.redirectToSignin();
    }

    // Other subscriptions
    this.tokenExpriedSubscription = this.notification.register(TOKEN_EXPRIED).subscribe(e => {
      this.redirectToSignin();
    });

    this.logoutSubscription = this.notification.register(AFTER_USER_LOGOUT).subscribe(() => {
      this.serviceService.isTried$.next(false);
    });

    this.drawerSubscription = this.notification.register(OPEN_DRAWER).subscribe(() => {
      if (this.drawer) {
        this.drawer.open();
      }
    });

    this.deliveryIPsSubscription = this.apiManager.getDeliveryIPs().subscribe((value) => {
      this.deliveryIPs = value;
    });
  }

  ngOnDestroy(): void {
    // Clean up all subscriptions when component is destroyed
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
    if (this.tokenExpriedSubscription) {
      this.tokenExpriedSubscription.unsubscribe();
    }
    if (this.logoutSubscription) {
      this.logoutSubscription.unsubscribe();
    }
    if (this.drawerSubscription) {
      this.drawerSubscription.unsubscribe();
    }
    if (this.deliveryIPsSubscription) {
      this.deliveryIPsSubscription.unsubscribe();
    }
  }

  openInvoicePayDialog(id, buyType) {
    this.dialogsService.openDialog("trial-invoice-check-dialog", { invoiceId: id, buyType });
  }

  goBack() {
    const currentUrl = this.router.url;
    const urlTree = this.router.parseUrl(currentUrl);
    const m = urlTree.root.children[PRIMARY_OUTLET].segments[0].path;
    this.location.back();
    if (_.includes(["payment-methods"], m)) {
      this.router.navigate(["center"]);
    }
  }

  async logout() {
    this.dialogsService.openDialog("signout-check-dialog");
  }

  async verify() {
    this.submitted = true;
    await this.usersService.resendEmail();
    await this.router.navigate(["users", "signup-verify"]);
    this.submitted = false;
  }

  async onScroll() {
    this.notification.post(WINDOW_SRCOLL);
  }

  async goTrial() {
    await this.router.navigate(["trial"]);
  }
}
