import { Component, OnInit } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { AFTER_ORDER_CREATED, AFTER_USER_LOGIN, NotificationService, USER_EMAIL_VERIFIED } from "@flashvpn-io/web-core";
import { ActivatedRoute, ParamMap, Router } from "@angular/router";
import { APIManager } from "@flashvpn-io/web-core";
import { UsersService } from "@flashvpn-io/web-core";
import { AppService } from "src/app/app.service";
import { MatSnackBar } from "@angular/material/snack-bar";

@Component({
  selector: "app-services",
  templateUrl: "./services.component.html",
  styleUrls: ["./services.component.css"],
})
export class ServicesComponent implements OnInit {
  constructor(
    private http: HttpClient,
    private notificationService: NotificationService,
    private userService: UsersService,
    private router: Router,
    private route: ActivatedRoute,
    private appService: AppService,
    private snackBar: MatSnackBar,
    private apiManager: APIManager
  ) {}

  async ngOnInit() {
    // load the latest created service
    this.notificationService.register(AFTER_ORDER_CREATED).subscribe((_) => this.apiManager.fetchServices().subscribe());
    // load the trial product
    this.notificationService.register(USER_EMAIL_VERIFIED).subscribe((_) => this.apiManager.fetchServices().subscribe());

    // load invoices so we have bills to pay
    // this.apiManager.fetchInvoices(null).subscribe();
  }
}
