<div class="paymentStatus">
  <div *ngIf="status === IN_PAYMENT">
    <div class="content">
      <svg width="84" height="84" viewBox="0 0 84 84" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_8358_7457)">
          <path d="M23.2861 7.07828C30.1929 2.46326 38.3132 0 46.62 0C57.0434 0.0131525 67.0635 3.89554 74.7601 10.8503V4.2H78.9601V18.48H64.2601V14.28H70.7181C67.7407 11.6866 64.348 9.57683 60.6608 8.04952C53.9565 5.27252 46.5793 4.54593 39.4621 5.96163C32.3449 7.37733 25.8073 10.8717 20.6761 16.003C15.5449 21.1342 12.0504 27.6718 10.6347 34.789C9.21904 41.9062 9.94563 49.2834 12.7226 55.9877C15.4996 62.6919 20.2023 68.4222 26.236 72.4537C32.2697 76.4853 39.3634 78.6372 46.62 78.6372C51.4383 78.6372 56.2093 77.6881 60.6608 75.8443C62.0197 75.2814 63.3406 74.6385 64.6171 73.92H73.9168C70.5784 76.7748 66.7949 79.1038 62.6927 80.8029C55.0182 83.9818 46.5734 84.8136 38.4262 83.193C30.279 81.5724 22.7953 77.5723 16.9215 71.6985C11.0477 65.8247 7.04761 58.341 5.42703 50.1938C3.80645 42.0466 4.63819 33.6018 7.81707 25.9273C10.996 18.2528 16.3792 11.6933 23.2861 7.07828Z" fill="#FFC039"/>
          <path d="M24.7369 44.2903C24.1295 43.6829 23.7882 42.8591 23.7882 42C23.7882 41.141 24.1295 40.3172 24.7369 39.7098C25.3443 39.1023 26.1682 38.7611 27.0272 38.7611C27.8862 38.7611 28.71 39.1023 29.3175 39.7098C29.9249 40.3172 30.2661 41.141 30.2661 42C30.2661 42.8591 29.9249 43.6829 29.3175 44.2903C28.71 44.8977 27.8862 45.239 27.0272 45.239C26.1682 45.239 25.3443 44.8977 24.7369 44.2903Z" fill="#FFC039"/>
          <path d="M44.3298 44.2903C43.7224 43.6829 43.3812 42.8591 43.3812 42C43.3812 41.141 43.7224 40.3172 44.3298 39.7098C44.9373 39.1023 45.7611 38.7611 46.6201 38.7611C47.4791 38.7611 48.303 39.1023 48.9104 39.7098C49.5178 40.3172 49.8591 41.141 49.8591 42C49.8591 42.8591 49.5178 43.6829 48.9104 44.2903C48.303 44.8977 47.4791 45.239 46.6201 45.239C45.7611 45.239 44.9373 44.8977 44.3298 44.2903Z" fill="#FFC039"/>
          <path d="M62.921 42C62.921 42.8591 63.2622 43.6829 63.8697 44.2903C64.4771 44.8977 65.3009 45.239 66.1599 45.239C67.0189 45.239 67.8428 44.8977 68.4502 44.2903C69.0576 43.6829 69.3989 42.8591 69.3989 42C69.3989 41.141 69.0576 40.3172 68.4502 39.7098C67.8428 39.1023 67.0189 38.7611 66.1599 38.7611C65.3009 38.7611 64.4771 39.1023 63.8697 39.7098C63.2622 40.3172 62.921 41.141 62.921 42Z" fill="#FFC039"/>
        </g>
        <defs>
          <clipPath id="clip0_8358_7457">
            <rect width="84" height="84" fill="white"/>
          </clipPath>
        </defs>
      </svg>
      <div class="payTitle">
        支付中...
      </div>
      <div class="paySubTitle nonmobile">
        <p>当前账单正在支付中，您可继续等待或<a style="cursor: pointer" (click)="goIndex($event)" >返回首页</a></p>
        <p>支付成功后，我们将会通知您</p>
      </div>
    </div>
  </div>
  <div *ngIf="status === PAYMENT_SUCCESS">
    <div class="content">
      <svg width="84" height="84" viewBox="0 0 84 84" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M78.2218 63.26C83.3754 54.4794 85.1641 44.1258 83.2551 34.1251L77.4376 35.2356C79.0774 43.826 77.5409 52.7197 73.114 60.262C68.6871 67.8044 61.6712 73.482 53.3715 76.2385C45.0717 78.9951 36.0532 78.643 27.9936 75.2477C19.9341 71.8523 13.3822 65.6451 9.55687 57.7805C5.73154 49.9159 4.8932 40.9295 7.1978 32.4931C9.5024 24.0567 14.793 16.7446 22.0855 11.9171C29.3779 7.08955 38.1757 5.07534 46.8421 6.24911C55.5086 7.42288 63.4536 11.7047 69.1992 18.2981L73.6643 14.4071C66.9755 6.73133 57.7262 1.74657 47.637 0.380102C37.5479 -0.986363 27.3059 1.35851 18.8162 6.97852C10.3266 12.5985 4.16747 21.111 1.48454 30.9324C-1.19839 40.7538 -0.222423 51.2154 4.23089 60.371C8.6842 69.5267 16.3116 76.753 25.6943 80.7057C35.077 84.6584 45.576 85.0683 55.2383 81.8592C64.9005 78.6501 73.0682 72.0405 78.2218 63.26ZM32.5694 61.0248L82.5711 24.3215L78.4289 18.6786L34.4306 50.9752L22.9441 33.1074L17.0559 36.8927L32.5694 61.0248Z" fill="#38DC83"/>
      </svg>
      <div class="payTitle">
        支付成功
      </div>
      <div class="paySubTitle">
        <p>您的账单已成功支付</p>
      </div>
      <div class="botton_box nonmobile" >
        <button *ngIf="!submitted" class="payment-button" mat-raised-button color="warn" (click)="goIndex($event)" >返回首页({{this.time}}s)</button>
      </div>
    </div>
  </div>
  <div *ngIf="status === PAYMENT_FAILURE">
    <div class="content">
      <svg width="84" height="84" viewBox="0 0 84 84" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M78.2218 63.26C83.3754 54.4794 85.1641 44.1258 83.2551 34.1251L77.4376 35.2356C79.0774 43.826 77.5409 52.7197 73.114 60.262C68.6871 67.8044 61.6712 73.482 53.3715 76.2385C45.0717 78.9951 36.0532 78.643 27.9936 75.2477C19.9341 71.8523 13.3822 65.6451 9.55687 57.7805C5.73154 49.9159 4.8932 40.9295 7.1978 32.4931C9.5024 24.0567 14.793 16.7446 22.0855 11.9171C29.3779 7.08955 38.1757 5.07534 46.8421 6.24911C55.5086 7.42288 63.4536 11.7047 69.1992 18.2981L73.6643 14.4071C66.9755 6.73133 57.7262 1.74657 47.637 0.380102C37.5479 -0.986363 27.3059 1.35851 18.8162 6.97852C10.3266 12.5985 4.16747 21.111 1.48454 30.9324C-1.19839 40.7538 -0.222423 51.2154 4.23089 60.371C8.6842 69.5267 16.3116 76.753 25.6943 80.7057C35.077 84.6584 45.576 85.0683 55.2383 81.8592C64.9005 78.6501 73.0682 72.0405 78.2218 63.26ZM21.6388 58.3612L37.5251 42.4749L21.6388 26.5885L26.5885 21.6388L42.4749 37.5251L58.3612 21.6388L63.311 26.5885L47.4246 42.4749L63.311 58.3612L58.3612 63.311L42.4749 47.4246L26.5885 63.311L21.6388 58.3612Z" fill="#FF5E5E"/>
      </svg>
      <div class="payTitle">
        支付失败
      </div>
      <div class="paySubTitle">
        <p>很抱歉，您的账单支付失败</p>
        <p>请回到账单页重新支付</p>
      </div>
      <div class="botton_box nonmobile" >
        <button *ngIf="!submitted" class="payment-button" mat-raised-button color="warn" (click)="buyAgain($event)" >重新支付</button>
        <button *ngIf="submitted" mat-fab color="warn" class="bounce-in-fwd" >
          <mat-spinner diameter="30"></mat-spinner>
        </button>
      </div>
      <div class="footerTitle">
        <p>提示：</p>
        <p>1.如网络异常、信号异常等短暂的网络波动会导致系统提示支付失败，此情况下请您放心，不会产生扣费。您只需要刷新几次支付页面重试即可</p>
        <p>2.账户余额不足，也会导致支付失败，您可以尝试切换支付方式支付若已产生扣费，但在【我的套餐】中没有显示购买记录，请联系客服为您处理</p>
      </div>
    </div>
  </div>
</div>
<div class="paymentStatusFooter">
  <div *ngIf="status === IN_PAYMENT" class="inPaymentFooter">
    <p>当前账单正在支付中，您可继续等待或<a (click)="goIndex($event)" >返回首页</a></p>
    <p>支付成功后，我们将会通知您</p>
  </div>
  <div *ngIf="status === PAYMENT_SUCCESS" style="width: 100%">
<!--    <button *ngIf="!submitted" class="payment-button" mat-raised-button color="warn" (click)="goIndex($event)" >返回首页({{this.time}}s)</button>-->
    <a id="payment-balance" *ngIf="!submitted" (click)="goIndex($event)"  class="callout-button payment-button-mobile">返回首页({{this.time}}s)</a>
  </div>
  <div *ngIf="status === PAYMENT_FAILURE" style="width: 100%">
    <div class="botton_box">
      <button *ngIf="!submitted" class="payment-button" mat-raised-button color="warn" (click)="buyAgain($event)" >重新支付</button>
      <button *ngIf="submitted" mat-fab color="warn" class="bounce-in-fwd" >
        <mat-spinner diameter="30"></mat-spinner>
      </button>
    </div>
  </div>
</div>
