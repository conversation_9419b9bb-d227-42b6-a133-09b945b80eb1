.paymentStatus{
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  min-height: 80%;
}
.content{
  width: 400px;
}
.nonmobile{
  display: block;
}
@media screen and (max-width: 599px) {
  .content{
    width: 100%;
  }
  .nonmobile{
    display: none !important;
  }
}
.payTitle{
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 600;
  font-size: 30px;
  line-height: 34px;
  text-align: center;
  color: #000000;
  margin-top: 30px;
}
.paySubTitle{
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 16px;
  text-align: center;
  color: #C4C4C4;
  margin-top: 30px;
}
.footerTitle{
  text-align: left;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
  color: #C4C4C4;
  margin-top: 30px;
}
.payment-button{
  width: 400px;
  height: 50px;
}
.paymentStatusFooter{
  display: none;
  width: 100%;
  min-height: 20%;
  align-items: center;
  text-align: center;
}
.inPaymentFooter{
  width: 100%;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  color: #C4C4C4;
}
@media screen and (max-width: 599px) {
  .payment-button{
    width: 100%;
    height: 50px;
  }
  .paymentStatusFooter{
    display: flex;
  }
}
/deep/.mat-spinner circle {
  stroke: #FFFFFF;
}
.botton_box{
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 50px;
}
.bounce-in-fwd {
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-animation: bounce-in-fwd 0.8s both;
  animation: bounce-in-fwd 0.8s both;
}
@-webkit-keyframes bounce-in-fwd {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  38% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
    opacity: 1;
  }
  55% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  72% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  81% {
    -webkit-transform: scale(0.84);
    transform: scale(0.84);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  89% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  95% {
    -webkit-transform: scale(0.95);
    transform: scale(0.95);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}
.payment-button-mobile{
  width: auto;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  box-shadow: 0px 3px 1px -2px rgb(0 0 0 / 20%), 0px 2px 2px 0px rgb(0 0 0 / 14%), 0px 1px 5px 0px rgb(0 0 0 / 12%);
}
