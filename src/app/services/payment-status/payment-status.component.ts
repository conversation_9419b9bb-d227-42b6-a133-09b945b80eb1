import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { PAYMENT_SUBMIT, IN_PAYMENT, PAYMENT_SUCCESS, PAYMENT_FAILURE, PaymentService } from "../payment.service";
import { Rule, UsersService, PaymentMethodService, PayType } from "@flashvpn-io/web-core";

@Component({
  selector: 'app-payment-status',
  templateUrl: './payment-status.component.html',
  styleUrls: ['./payment-status.component.css']
})
export class PaymentStatusComponent implements OnInit {

  public invoiceId: number;

  public paymentMethod: string;

  public paymentToken: string;

  public billingCycles: string;

  public status: string;

  public buyType: number;

  public submitted = false;

  public PAYMENT_SUBMIT = PAYMENT_SUBMIT;

  public IN_PAYMENT = IN_PAYMENT;

  public PAYMENT_SUCCESS = PAYMENT_SUCCESS;

  public PAYMENT_FAILURE = PAYMENT_FAILURE;
  public time = 10;
  public timer;

  constructor(
    public router: Router,
    public route: ActivatedRoute,
    public location: Location,
    public paymentService: PaymentService,
    private usersService: UsersService,
    private paymentMethodService: PaymentMethodService
  ) { }

  async ngOnInit() {
    this.route.queryParamMap.subscribe(
      async (params) => {
        console.log('init payment status');
        this.invoiceId = Number(params.get('invoiceId'));
        this.paymentMethod = params.get('paymentMethod');
        this.paymentToken = params.get('paymentToken');
        this.billingCycles = params.get('billingCycles');
        this.status = params.get('status');
        if (this.status === PAYMENT_SUCCESS) {
          this.timer = setInterval(() => {
            if (this.time > 0) {
              this.time--;
            } else {
              clearInterval(this.timer);
              this.router.navigate(['center']);
            }
          }, 1000);
        }
      });
  }

  async ngAfterViewInit(): Promise<void> {
    this.route.queryParamMap.subscribe(
      async (params) => {
        const id = Number(params.get('id'));
        this.status = params.get('status');
        if (this.status === IN_PAYMENT && id) {
          this.paymentService.pullingInvoice(id);
        }
      });
  }

  buyAgain = (e) => {
    e.preventDefault();
    this.submitted = true;
    if (this.invoiceId && this.paymentMethod && this.billingCycles && this.buyType) {
      const rule = new Rule();
      rule.id = this.billingCycles;
      // if( this.buyType ===1 ){
      //   this.paymentService.servicePay(rule, this.paymentMethod, this.paymentToken);
      // }else if ( this.buyType ===2 ) {
      //   this.paymentService.flowPay(rule, this.paymentMethod, this.paymentToken);
      // }else{
      // this.router.navigate(['services', 'dashboard']);
      this.router.navigate(['center']);
      // }
    } else {
      // this.router.navigate(['services', 'dashboard']);
      this.router.navigate(['center']);
    }
  }
  goIndex = (e) => {
    e.preventDefault();
    if (this.timer) {
      clearInterval(this.timer);
    }
    // this.router.navigate(['services', 'dashboard']);
    this.router.navigate(['center']);
  }
}
