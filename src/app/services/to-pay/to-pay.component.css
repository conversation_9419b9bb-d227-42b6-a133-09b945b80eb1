.to-pay {
  width: 100%;
  margin: 20px;
}

.title {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: 0em;
  text-align: left;
  margin-bottom: 10px;
}

.content {
  width: 100%;
  height: 183px;
  display: flex;
}

.left-box {
  width: 65%;
  justify-content: space-around;
  display: flex;
  align-items: center;
}

.right-box {
  width: 35%;
}

.payment-button {
  padding-bottom: 30px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.payment-btn {
  width: 300px;
  height: 40px;
  margin-bottom: 10px;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.discount {
  width: 250px;
  margin-top: 30px;
}

.left-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}

.content-item {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 20px;
  display: flex;
  align-items: center;
  text-align: right;
  color: #000000;
}

.content-item-title {
  display: flex;
  align-items: center;
  width: 130px;
  color: #000;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.content-item-title-2 {
  display: flex;
  align-items: center;
  width: 130px;
  color: #000;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}

.content-item-value {
  color: #FF5E5E;
  text-align: right;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.mobile {
  display: none;
}

.no-mobile {
  display: flex;
}

.amount-info {
  margin: 20px 0;
  width: 300px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #A0AEC0;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.amount-info-value {
  color: #000;
  text-align: right;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px;
}

.mobile-amount-info {
  display: flex;
  color: #FF5E5E;
  align-items: center;
  justify-content: center;
}

.to-pay-mobile-info {
  position: fixed;
  bottom: 150px;
  height: auto;
  left: 0;
  right: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
  border-radius: 20px 20px 0 0;
  z-index: 3;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.to-pay-mobile-botton {
  position: fixed;
  bottom: 0;
  height: 150px;
  left: 0;
  right: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #FFFFFF;
  margin: 0;
  z-index: 999;
}

.pay-info-box {
  margin: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.pay-info-item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

@media screen and (max-width: 599px) {
  .title {
    margin-bottom: 0;
  }

  .content-item-title-2 {
    width: auto;
  }

  .payment-btn {
    width: 90%;
  }

  .mobile {
    display: flex;
  }

  .no-mobile {
    display: none;
  }

  .amount-info {
    width: 90%;
  }
}

.bounce-in-bottom {
  -webkit-animation: bounce-in-bottom 1.1s both;
  animation: bounce-in-bottom 1.1s both;
}

.buttons {
  display: flex;
  flex-direction: row;
}

@-webkit-keyframes bounce-in-bottom {
  0% {
    -webkit-transform: translateY(500px);
    transform: translateY(500px);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }

  38% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
    opacity: 1;
  }

  55% {
    -webkit-transform: translateY(65px);
    transform: translateY(65px);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }

  72% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }

  81% {
    -webkit-transform: translateY(28px);
    transform: translateY(28px);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }

  90% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }

  95% {
    -webkit-transform: translateY(8px);
    transform: translateY(8px);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}

@keyframes bounce-in-bottom {
  0% {
    -webkit-transform: translateY(500px);
    transform: translateY(500px);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }

  38% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
    opacity: 1;
  }

  55% {
    -webkit-transform: translateY(65px);
    transform: translateY(65px);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }

  72% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }

  81% {
    -webkit-transform: translateY(28px);
    transform: translateY(28px);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }

  90% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }

  95% {
    -webkit-transform: translateY(8px);
    transform: translateY(8px);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}

.slide-out-bottom {
  -webkit-animation: slide-out-bottom 0.5s cubic-bezier(0.550, 0.085, 0.680, 0.530) both;
  animation: slide-out-bottom 0.5s cubic-bezier(0.550, 0.085, 0.680, 0.530) both;
}

@-webkit-keyframes slide-out-bottom {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
  }

  100% {
    -webkit-transform: translateY(1000px);
    transform: translateY(1000px);
    opacity: 0;
  }
}

@keyframes slide-out-bottom {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
  }

  100% {
    -webkit-transform: translateY(1000px);
    transform: translateY(1000px);
    opacity: 0;
  }
}