<div fxLayout="row" fxLayoutAlign="start center" fxLayout.lt-sm="column" fxLayoutGap.lt-sm="5px"
  fxLayoutAlign.lt-sm="start start">
  <div class="to-pay no-mobile">
    <div class="content">
      <div class="left-box">
        <div class="left-content">
          <div class="title">金额明细</div>
          <!--          <div class="content-item"><span class="content-item-title">服务名称</span>{{getOrderName()}}</div>-->
          <div class="content-item"><span class="content-item-title">服务周期</span><span
              style="color: #505050">{{payInfo?.description}}</span></div>
          <!--          <div class="content-item"><span class="content-item-title">订单日期</span>{{today}}</div>-->
          <!--          <div class="content-item"><span class="content-item-title">有效日期</span>{{effectiveDate()}}</div>-->
          <div class="content-item"><span class="content-item-title">总价</span><span
              style="color: #FF5E5E">{{getOrderAmount()}}港币</span></div>
          <div>&nbsp;</div>
          <div>&nbsp;</div>
        </div>
        <div class="left-content">
          <div class="title">&nbsp;</div>
          <div class="content-item"><span class="content-item-title">共减</span><span
              style="color: #FF5E5E">减{{deductionAmount || 0}}港币</span></div>
          <!--        <div class="content-item-red"><span class="content-item-title">折扣码</span>-0%(-{{discountAmount}}港币)</div>-->
          <div class="content-item"><span class="content-item-title-2">优惠券</span><span
              class="content-item-value">减{{deductionType.voucher === currentDeduction ? deductionAmount || 0 :
              0}}港币</span></div>
          <div class="content-item"><span class="content-item-title-2">余额抵扣</span><span
              class="content-item-value">减{{deductionType.balance === currentDeduction ? deductionAmount || 0 :
              0}}港币</span></div>
          <div class="content-item"><span class="content-item-title-2">折扣码抵扣</span><span
              class="content-item-value">减{{deductionType.discount_code === currentDeduction ? deductionAmount || 0 :
              0}}港币</span></div>
          <!--          <div>&nbsp;</div>-->
        </div>
      </div>
      <div class="right-box">
        <div class="payment-button">
          <div class="amount-info">
            <div><span>合计：</span><span class="amount-info-value">{{calculate()}}港币</span></div>
            <div><span>已抵扣：</span><span class="amount-info-value">{{deductionAmount || 0}}港币</span></div>
          </div>
          <!--          <div class="buttons">-->
          <button *ngIf="!submitted" [disabled]="loading||(currentMethod === 'creditcard'&&!card)" class="payment-btn"
            mat-raised-button color="warn" id="order" (click)="buy()">支付{{calculate()}}港币</button>
          <!--            <button *ngIf="hasDeduct && !submitted" [disabled]="loading||(currentMethod === 'creditcard'&&!card)" class="payment-btn" mat-raised-button color="warn" id="cancel" (click)="cancel()">取消支付</button>-->
          <!--          </div>-->
          <app-load-botton *ngIf="submitted"></app-load-botton>
        </div>
      </div>
    </div>
  </div>
</div>

<div *ngIf="!init"
  [class]="showMobilePayInfo?'to-pay-mobile-info bounce-in-bottom mobile':'to-pay-mobile-info slide-out-bottom mobile'">
  <div fxLayout="row" fxLayoutAlign="space-between center" style="margin: 20px;">
    <div class="title">金额明细</div>
    <span class="material-icons" (click)="closeMobilePayInfo()" style="cursor: pointer;">
      close
    </span>
  </div>
  <div class="pay-info-box">
    <div class="pay-info-item">
      <div class="content-item">支付周期</div>
      <div class="content-item" style="color: #505050">按季度支付</div>
    </div>
    <div class="pay-info-item">
      <div class="content-item">总价</div>
      <div class="content-item" style="color: #FF5E5E">{{getOrderAmount()}}港币</div>
    </div>
    <div class="pay-info-item">
      <div class="content-item-title-2">优惠券</div>
      <div class="content-item-value">减{{deductionType.voucher === currentDeduction ? deductionAmount || 0 : 0}}港币</div>
    </div>
    <div class="pay-info-item">
      <div class="content-item-title-2">余额抵扣</div>
      <div class="content-item-value">减{{deductionType.balance === currentDeduction ? deductionAmount || 0 : 0}}港币</div>
    </div>
    <div class="pay-info-item">
      <div class="content-item-title-2">折扣码抵扣</div>
      <div class="content-item-value">减{{deductionType.discount_code === currentDeduction ? deductionAmount || 0 : 0}}港币
      </div>
    </div>
    <div class="pay-info-item">
      <div class="content-item">合计</div>
      <div class="content-item" style="color: #FF5E5E">{{calculate()}}港币</div>
    </div>
  </div>
</div>
<div class="to-pay-mobile-botton mobile"
  [style]="{'border-top': showMobilePayInfo?'0':'1px solid rgba(0, 0, 0, 0.12)'}">
  <div class="payment-button">
    <div class="amount-info">
      <div><span>合计：</span><span class="amount-info-value">{{calculate()}}港币</span></div>
      <div><span>已抵扣：</span><span class="amount-info-value">{{deductionAmount || 0}}港币</span></div>
      <div *ngIf="!showMobilePayInfo" class="mobile-amount-info" (click)="openMobilePayInfo()">
        <span style="margin-right: 5px">查看详情</span>
        <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 5L5 1L9 5" stroke="#FF5E5E" />
        </svg>
      </div>
    </div>
    <button *ngIf="!submitted" [disabled]="loading||(currentMethod === 'creditcard'&&!card)" class="payment-btn"
      mat-raised-button color="warn" id="mobile-order" (click)="buy()">支付{{calculate()}}港币</button>
    <app-load-botton *ngIf="submitted"></app-load-botton>
  </div>
</div>
<div *ngIf="showMobilePayInfo" id="Mask"
  style="overflow: hidden; z-index: 1000; width: 100%; height: 1600px; position: fixed; top: 0px; left: 0px; background: rgb(0, 0, 0); opacity: 0.6;">
</div>