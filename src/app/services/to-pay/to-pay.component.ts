import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { LoadBottonComponent } from "../../utils/load-botton/load-botton.component";
import { AppService } from "src/app/app.service";
import { CANCEL_ORDER, DESHBOARD_SEND_DATA, IN_PAYMENT, PAYMENT_FAILURE, PaymentService } from "../payment.service";
import * as Big from "big.js";
import * as moment from "moment";
import { APIManager } from "@flashvpn-io/web-core";
import { environment } from "src/environments/environment";
import { MatSnackBar } from "@angular/material/snack-bar";
import { Rule } from "@flashvpn-io/web-core";
import { Service } from "@flashvpn-io/web-core";
import { Product } from "@flashvpn-io/web-core";
import { BuyType, DeductionType, Invoice } from "@flashvpn-io/web-core";
import { Reward } from "@flashvpn-io/web-core";

@Component({
  selector: "app-to-pay",
  templateUrl: "to-pay.component.html",
  styleUrls: ["./to-pay.component.css"],
})
export class ToPayComponent implements OnInit {
  @ViewChild("loadBottonComponent") loadBottonComponent: LoadBottonComponent;

  public today: any;

  public discountAmount = 0;

  public submitted = false;

  @Input() public currentMethod: string;

  @Input() public buyType: number;

  @Input() public card: any;

  @Input() public payInfo: Rule;

  @Input() public deductionAmount = 0;

  @Input() public service: Service;

  @Input() public services: Service[];

  @Input() public products: Product[];

  @Input() public currentDeduction: string;

  @Input() public currentVoucher: Reward;

  @Input() public deductionError;

  @Input() public discountCode;

  @Input() public eDate;

  @Input() public loading = false;

  @Input() public hasDeduct = false;

  @Input() public invoiceId;

  @Input() public stripe;

  public readonly deductionType = DeductionType;

  public showMobilePayInfo = false;

  public init = true;
  public invoice: Invoice;

  constructor(public appService: AppService, private apiManager: APIManager, private paymentService: PaymentService, private snackBar: MatSnackBar) { }

  ngOnInit(): void {
    this.today = moment().utc().format("YYYY-MM-DD");
    if (this.invoiceId) {
      this.invoice = Invoice.fromData({ id: this.invoiceId });
    }
  }

  getOrderName() {
    let title;
    if (this.buyType === BuyType.SERVICE.valueOf()) {
      if (!this.service) {
        if (this.products && this.products.length > 0) {
          title = this.products[0]?.name ? this.products[0]?.name + "-" + this.payInfo.description : "";
        }
      } else {
        const product = this.products?.find((p) => p.id === this.service?.productId);
        title = product?.name ? product?.name + "-" + this.payInfo?.description : "";
      }
    } else if (this.buyType === BuyType.FLOWDATA.valueOf()) {
      title = "充值流量包-" + this.payInfo?.description;
    }
    return title;
  }

  calculate() {
    let amount = 0;
    const orderAmount = this.getOrderAmount();
    if (orderAmount) {
      amount = Number(new Big(orderAmount)
        .minus(this.deductionAmount ? this.deductionAmount : 0)
        .minus(this.discountAmount ? this.discountAmount : 0));
    }
    return amount;
  }

  effectiveDate() {
    let date;
    if (this.buyType === BuyType.SERVICE.valueOf()) {
      switch (this.payInfo?.id) {
        case "monthly":
          date = moment().add(30, "days").format("YYYY-MM-DD");
          break;
        case "quarterly":
          date = moment()
            .add(30 * 3, "days")
            .format("YYYY-MM-DD");
          break;
        case "semiannually":
          date = moment()
            .add(30 * 6, "days")
            .format("YYYY-MM-DD");
          break;
        case "annually":
          date = moment()
            .add(30 * 12, "days")
            .format("YYYY-MM-DD");
          break;
      }
    } else if (this.buyType === BuyType.FLOWDATA.valueOf()) {
      date = this.eDate;
    }
    return date;
  }

  duration() {
    let monthly;
    if (this.buyType === BuyType.SERVICE.valueOf()) {
      switch (this.payInfo?.id) {
        case "monthly":
          monthly = "1个月";
          break;
        case "quarterly":
          monthly = "3个月";
          break;
        case "semiannually":
          monthly = "6个月";
          break;
        case "annually":
          monthly = "12个月";
          break;
      }
    } else if (this.buyType === BuyType.FLOWDATA.valueOf()) {
      monthly = this.eDate ? moment(this.eDate).diff(moment(), "days") + "天" : "";
    }
    return monthly;
  }

  async buy() {
    this.submitted = true;

    if (this.invoice) {
      await this.paymentService.payInvoice(this.invoice, this.currentMethod, "");
      return;
    }

    if (this.currentDeduction === this.deductionType.balance) {
      this.deductionError?.markAsTouched();
      if (this.deductionError?.invalid) {
        this.submitted = false;
        return;
      }
    } else if (this.currentDeduction === this.deductionType.voucher && !this.currentVoucher) {
      this.appService.snackUp("请选择需要抵扣的优惠券");
      this.submitted = false;
      return;
    }
    try {
      let paymentMethodId = "";
      if (this.currentMethod === "creditcard") {
        const paymentMethodObj = await this.stripe.createPaymentMethod({
          type: "card",
          card: this.card,
        });
        if (paymentMethodObj?.error) {
          this.snackBar.open(paymentMethodObj.error.message, "Okay", {
            duration: 2000,
            verticalPosition: "top",
          });
          return;
        } else {
          paymentMethodId = paymentMethodObj.paymentMethod.id;
        }
      }
      switch (this.buyType) {
        case BuyType.SERVICE.valueOf():
          await this.paymentService.servicePay(
            this.payInfo,
            this.currentMethod,
            paymentMethodId,
            this.currentDeduction ? this.deductionAmount : 0,
            this.service,
            this.currentVoucher,
            this.discountCode
          );
          break;
        case BuyType.FLOWDATA.valueOf():
          await this.paymentService.flowPay(
            this.payInfo,
            this.currentMethod,
            paymentMethodId,
            this.currentDeduction ? this.deductionAmount : 0,
            this.service,
            this.currentVoucher,
            this.discountCode
          );
          break;
        default:
          await this.paymentService.servicePay(
            this.payInfo,
            this.currentMethod,
            paymentMethodId,
            this.currentDeduction ? this.deductionAmount : 0,
            this.service,
            this.currentVoucher,
            this.discountCode
          );
          break;
      }
    } catch (error) {
      console.log(error);
      this.appService.snackUp(error?.error?.message);
      this.submitted = false;
    }
  }

  cancel() {
    this.paymentService.post(CANCEL_ORDER, null);
  }

  getOrderAmount(): number {
    let amount = 0;
    if (this.invoice) {
      return Number(this.payInfo.amount) + Number(this.deductionAmount);
    }
    if (this.buyType === BuyType.SERVICE.valueOf()) {
      if (!this.service) {
        if (this.products && this.products.length > 0) {
          amount = this.products[0]?.pricing?.HKD[this.payInfo.id];
        }
      } else {
        if (this.payInfo.id === this.service.billingcycle.toLowerCase()) {
          amount = +this.service.recurringamount;
        } else {
          const product = this.products?.find((p) => p.id === this.service?.productId);
          amount = product?.pricing?.HKD[this.payInfo.id];
        }
      }
    } else if (this.buyType === BuyType.FLOWDATA.valueOf()) {
      amount = this.payInfo?.amount;
    }
    return amount;
  }

  openMobilePayInfo = () => {
    console.log("openMobilePayInfo");
    this.showMobilePayInfo = true;
    this.init = false;
  };

  closeMobilePayInfo = () => {
    console.log("closeMobilePayInfo");
    this.showMobilePayInfo = false;
  };
}
