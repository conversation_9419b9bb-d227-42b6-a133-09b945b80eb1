import { Component, OnInit } from '@angular/core';
import { APIManager } from "@flashvpn-io/web-core";
declare var chrome;

@Component({
  selector: 'app-example',
  templateUrl: './example.component.html',
  styleUrls: ['./example.component.css']
})
export class ExampleComponent implements OnInit {
  config = {
    mode: 'fixed_servers',
    rules: {
      proxyForHttps: {
        scheme: 'https',
        host: '',
        port: 443,
      },
      bypassList: ['foobar.com']
    }
  };

  constructor(
    private apiManager: APIManager,
  ) { }

  ngOnInit() {
  }

  switch(value: any) {
    switch (value) {
      case 'on':
        chrome.proxy.settings.set(
          {value: this.config, scope: 'regular'},
          () => {});

        // trigger the authentication page
        fetch('https://abc.xyz').catch();
        break;
      case 'off':
        chrome.proxy.settings.set(
          {value: {mode: 'direct'}, scope: 'regular'},
          () => {});
        break;
      case 'system':
        chrome.proxy.settings.set(
          {value: {mode: 'system'}, scope: 'regular'},
          () => {});
    }
  }
}
