import { AfterViewInit, Component, OnInit, ViewChild } from "@angular/core";
import { AppService } from "src/app/app.service";
import { APIManager } from "@flashvpn-io/web-core";
import { IN_PAYMENT } from "../payment.service";
import { BreakpointObserver, Breakpoints } from "@angular/cdk/layout";
import { ActivatedRoute, Router, NavigationEnd } from "@angular/router";
import { Product } from "@flashvpn-io/web-core";
import { DeductionType } from "@flashvpn-io/web-core";
import { Service } from "@flashvpn-io/web-core";
import { BillingCycles, defaultBillingCycles } from "@flashvpn-io/web-core";
import { ServiceService } from "@flashvpn-io/web-core";
import { combineLatest } from "rxjs";
import { filter } from "rxjs/operators";
import { PaymentMethodService } from "@flashvpn-io/web-core";
import { PaymentDetailsComponent } from "src/app/component/payment-details.component";
import { BuyType } from "@flashvpn-io/web-core";
import { DeductionComponent } from "../../component/deduction.component";
import { Utils } from "src/app/utils/utils";
import { NotificationService, AFTER_INVOICE_CANCEL } from "@flashvpn-io/web-core";

@Component({
  selector: "app-service-renew",
  templateUrl: "./service-renew.component.html",
  styleUrls: ["./service-renew.component.css"],
})
export class ServiceRenewComponent implements OnInit, AfterViewInit {
  @ViewChild("deductionComponent") deductionComponent: DeductionComponent;

  @ViewChild("paymentDetailsComponent") paymentDetailsComponent: PaymentDetailsComponent;

  public hasDeduct = false;
  public submitted = false;
  public isLarge = true;
  public billingCycles: BillingCycles[];
  public currentBillingCycle: BillingCycles;
  public product: Product;
  public service: Service;
  public loading = false;
  protected readonly BuyType = BuyType;
  public openBillingCyclesChange = false

  productFlow = {
    1: "50G",
    2: "100G",
    3: "200G",
  };

  public currentFLow = "50G"

  public readonly deductionType = DeductionType;

  constructor(
    public appService: AppService,
    public apiManager: APIManager,
    private paymentMethodService: PaymentMethodService,
    private breakpointObserver: BreakpointObserver,
    public router: Router,
    private route: ActivatedRoute,
    private serviceService: ServiceService,
    private notificationService: NotificationService
  ) { }

  async ngOnInit() {
    // 此处必须先注册路由，在执行init，否则onNgInit只实例化一次会导致再次回到此页面时不会加载数据
    this.router.events.pipe(filter((event) => event instanceof NavigationEnd)).subscribe(async (event: NavigationEnd) => {
      const currentRoute = "/" + this.route.snapshot.url.join("/");
      if (event.url.split("?")[0] === currentRoute) {
        await this.init();
      }
    });
    await this.init();
  }

  async init() {
    combineLatest([
      this.serviceService.products$.pipe(filter((products) => !!products)),
      this.serviceService.currentService$.pipe(filter((service) => !!service)),
    ]).subscribe(async ([products, service]) => {
      this.product = products.find((p) => p.id === service.productId);
      this.currentFLow = this.productFlow[this.product.id]
      this.service = service;
      this.billingCycles = defaultBillingCycles;
      this.currentBillingCycle = this.billingCycles.find((bc) => bc.id === service.billingcycle);

      // 检查是否有未支付的订单
      try {
        const invoice = await this.apiManager.fetchLatestInvoice(service).toPromise();
        if (invoice.hadDiscount()) {
          this.hasDeduct = true;
        }
      } catch (error) {
        console.error('Failed to check unpaid invoice:', error);
      }

      // 重置 DeductionComponent 的状态
      if (this.deductionComponent) {
        this.deductionComponent.reset();
      }

      // 重置支付方式
      if (this.paymentMethodService) {
        this.paymentMethodService.reset();
      }
    });
  }

  getMonthsForBillingCycle(billingCycle: string): number {
    switch (billingCycle) {
      case "monthly":
        return 1;
      case "quarterly":
        return 3;
      case "semiannually":
        return 6;
      case "annually":
        return 12;
      case "biennially":
        return 24;
      default:
        return 1;
    }
  }

  ngAfterViewInit(): void {
    this.breakpointObserver.observe([Breakpoints.Medium, Breakpoints.Large, Breakpoints.XLarge]).subscribe((x) => {
      this.isLarge = x.matches;
      const footer = document.getElementById("to-pay-footer") as HTMLElement;
      if (footer) {
        if (this.isLarge) {
          footer.style.left = "258px";
        } else {
          footer.style.left = "0";
        }
      }
    });
  }

  async setBillingCycle(billingCycle) {
    this.currentBillingCycle = billingCycle;
  }

  async changeBillingCycle() {
    this.openBillingCyclesChange = !this.openBillingCyclesChange
  }

  async buy() {
    this.submitted = true;
    const currentMethod = this.paymentMethodService.currentMethod$.value;
    try {
      // if the billing cycle is changed, we need to update the billing cycle
      if (this.service.billingcycle !== this.currentBillingCycle.id) {
        await this.apiManager.updateBillingCycle(this.service.id, { billingCycle: this.currentBillingCycle.id }).toPromise();
      }
      const invoice = await this.apiManager.fetchLatestInvoice(this.service).toPromise();

      if (this.deductionComponent?.deductionAmount$.value > 0) {
        if (this.deductionComponent?.currentDeduction$.value === DeductionType.voucher) {
          await this.apiManager
            .deductionInvoice(
              invoice,
              this.deductionComponent?.deductionAmount$.value,
              this.deductionComponent?.selectCouponComponent?.currentCoupon?.id?.toString(),
              undefined,
              this.currentBillingCycle.id
            )
            .toPromise();
        } else if (this.deductionComponent?.currentDeduction$.value === DeductionType.balance) {
          await this.apiManager
            .deductionInvoice(invoice, this.deductionComponent?.deductionAmount$.value, undefined, undefined, this.currentBillingCycle.id)
            .toPromise();
        } else if (this.deductionComponent?.currentDeduction$.value === DeductionType.discount_code) {
          await this.apiManager
            .deductionInvoice(
              invoice,
              this.deductionComponent?.deductionAmount$.value,
              undefined,
              this.deductionComponent?.selectCodeComponent?.discountCode,
              this.currentBillingCycle.id
            )
            .toPromise();
        }
      }

      const data = await this.apiManager.payInvoice(invoice, currentMethod.payType, "").toPromise();
      if (data.redirect_url !== undefined && currentMethod.payType !== 'balance') {
        Utils.openPayWindow();
        if (this.appService.isElectron() || this.appService.isExtension()) {
          window.open(data.redirect_url);
          // Utils.closePayWindow();
        } else {
          // window.location = data.redirect_url;
          Utils.sendPayUrl(data.redirect_url);
        }
      }
      this.router.navigate(["payment-status"], { queryParams: { status: IN_PAYMENT, id: invoice.id } });
    } catch (e) {
      this.appService.snackUp(e.error.message);
      this.submitted = false;
    }
  }

  async cancelInvoice() {
    try {
      this.loading = true;
      const invoice = await this.apiManager.fetchLatestInvoice(this.service).toPromise();
      await this.apiManager.cancelInvoice(invoice.id).toPromise();
      this.notificationService.post(AFTER_INVOICE_CANCEL);
      this.appService.snackUp("取消成功");
      this.hasDeduct = false;
    } catch (error) {
      this.appService.snackUp(error?.error?.message || "取消失败");
    } finally {
      this.loading = false;
    }
  }
}
