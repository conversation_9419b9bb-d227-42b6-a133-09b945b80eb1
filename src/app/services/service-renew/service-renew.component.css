.payment-methods {
  max-width: 1300px;
  margin: 0 auto 250px auto;
}

.visible {
  display: block;
}

.hidden {
  display: none;
}

.card-container {
  max-width: 40%;
}

.card-error-messgae {
  color: #ff5e5e;
  margin-top: 20px;
}

.subTitle {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #808080;
}

.services-content-mobile {
  margin-top: 20px;
  width: 100%;
  flex-wrap: wrap;
  display: none;
  justify-content: space-around;
  min-height: 100px;
  cursor: pointer;
  padding-left: 10px;
}

.services-content-liuliang {
  display: flex;
  margin-top: 20px;
  width: 100%;
  flex-wrap: wrap;
  justify-content: space-around;
  min-height: 100px;
  cursor: pointer;
  padding-left: 10px;
}

.services-content-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  width: 22%;
  height: 100px;
  /*padding-bottom: 30%;*/
  background: rgba(255, 94, 94, 0.1);
  border-radius: 2px;
  box-sizing: border-box;
  margin: 10px;
}

.services-content-item-1 {
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 29px;
  color: #000000;
}

.services-content-item-2 {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 10px;
  width: 70%;
  background: #ffc039;
  border-radius: 4px;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  color: #ffffff;
  padding: 3px;
}

.goBack {
  color: #000;
  display: none;
  grid-template-rows: 100px auto 0px;
  grid-template-columns: 100%;
  grid-template-areas: "header" "main" "footer";
}

.header {
  grid-area: header;
}

.services-content-pc {
  margin-top: 20px;
  display: flex;
}

.services-content-pc .services-content-pc-li {
  cursor: pointer;
  flex: 1;
  position: relative;
  height: 204px;
  margin-right: 24px;
  border: 1px solid #fff;
  border-radius: 4px;
  box-shadow: 0px 2px 10px 0px #ff5e5e1a;
  padding: 32px 16px;
  box-sizing: border-box;
}

.services-content-pc .activeImg {
  position: absolute;
  right: 0px;
  top: 0px;
}

.services-content-pc .noActive {
  border: 1px solid transparent;
  transition: border-color 0.3s ease-in-out;
}

.services-content-pc .activeClick {
  border: 1px solid #ff5e5e;
  transition: border-color 0.3s ease-in-out;
}

.services-content-pc-li .content-pc-li-title {
  font-size: 14px;
  color: #000;
  font-weight: 600;
  padding-bottom: 12px;
}

.services-content-pc-li .content-pc-li-amount {
  color: #ff5e5e;
  font-size: 20px;
  font-weight: 600;
  padding-bottom: 24px;
}

.services-content-pc-li .content-pc-li-discount {
  padding-bottom: 10px;
  font-size: 14px;
  color: #000;
}

.services-content-pc-li .content-pc-li-discount span {
  color: #808080;
}

.li-discount-off {
  width: 70px;
  background: rgba(255, 94, 94, 0.1);
  border-radius: 2px;
  padding: 5px 10px;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 14px;
  color: #ff5e5e;
}

.li-discount-on {
  width: 70px;
  background: #f2f2f2;
  border-radius: 2px;
  padding: 5px 10px;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 14px;
  color: #808080;
}

@media screen and (max-width: 599px) {
  .card-container {
    margin-top: 30px;
  }

  .payment-methods {
    margin: 0 0 150px 0;
  }

  .goBack {
    display: grid;
  }

  .services-content-pc {
    display: none;
  }

  .services-content-mobile {
    display: block;
    cursor: auto;
    padding-left: 0px;
  }

  .services-content-mobile {
    margin-top: 20px;
    display: flex;
  }

  .div1 {
    position: relative;
    line-height: 14px;
    font-weight: 600;
  }

  .icon- {
    width: 14px;
    position: relative;
    height: 12px;
  }

  .div3 {
    position: relative;
    line-height: 100%;
  }

  .div2 {
    border-radius: 20px;
    background-color: #ff5e5e;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    padding: 6px 12px;
    gap: 4px;
    font-size: 12px;
    color: #fff;
  }

  .parent {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .div4 {
    position: relative;
    line-height: 20px;
    font-weight: 600;
  }

  .div5 {
    position: relative;
    text-decoration: line-through;
    line-height: 14px;
  }

  .div6 {
    position: relative;
    line-height: 14px;
    color: #000;
  }

  .container {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: 4px;
  }

  .frame-child {
    position: absolute;
    top: 0px;
    left: 0px;
    border-radius: 2px;
    background-color: rgba(255, 94, 94, 0.1);
    width: 64px;
    height: 18px;
  }

  .div7 {
    position: absolute;
    top: 2px;
    left: 7px;
    line-height: 14px;
  }

  .rectangle-parent {
    width: 64px;
    position: relative;
    height: 18px;
    font-size: 12px;
    color: #ff5e5e;
  }

  .frame-parent {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: 12px;
    font-size: 14px;
    color: #808080;
  }

  .group {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 8px;
    font-size: 20px;
    color: #ff5e5e;
  }

  .div {
    width: 100%;
    position: relative;
    box-shadow: 0px 2px 10px rgba(255, 94, 94, 0.1);
    border-radius: 2px;
    background-color: #fff;
    border: 1px solid #ff5e5e;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    padding: 32px 16px;
    gap: 24px;
    text-align: left;
    font-size: 14px;
    color: #000;
    font-family: 'PingFang SC';
  }

}

.deduction-box {
  display: inline;
  width: 100%;
  margin-bottom: 30px;
  margin-top: 15px;
}

.deduction-top-box {
  color: #808080;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
}

.deduction-left-box {
  width: 100%;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  color: #808080;
}

.deduction-right-box {
  width: 100%;
  text-align: center;
}

.example-full-width {
  width: 384px;
}

.footer {
  position: fixed;
  bottom: 0;
  height: 260px;
  left: 0;
  right: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  background-color: #ffffff;
  border-radius: 20px;
}

.voucher-box,
.voucher-box-disabled {
  width: 344px;
  height: 55px;
  border-radius: 4px;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.12);
  margin: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.voucher-box {
  background: #fff;
  cursor: pointer;
}

.voucher-box-disabled {
  background: #f6f6f6;
  cursor: not-allowed;
}

.voucher-right,
.voucher-left {
  display: flex;
  align-items: center;
}

.voucher-title {
  color: #808080;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  margin-left: 20px;
}

.voucher-amount {
  color: #ff5e5e;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 12px;
  margin-right: 20px;
}

.deduction-bottom-box {
  display: flex;
  align-items: center;
}

.deduction-code-btn,
.deduction-code-loading {
  margin-left: 30px;
  margin-bottom: 1.34375em;
}

.deduction-code-msg {
  color: #3fe07f;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.34375em;
  margin-left: 30px;
}

.hint-desktop {
  display: block;
}

@media screen and (max-width: 599px) {

  .voucher-box,
  .voucher-box-disabled {
    width: auto;
  }

  .deduction {
    padding: 0;
    margin-top: 0;
  }

  .footer {
    height: auto;
    border-radius: 0;
  }

  .example-full-width {
    width: 100%;
  }

  .deduction-bottom-box {
    display: block;
  }

  .deduction-code-msg {
    margin-bottom: 0;
    margin-left: 0;
    margin-top: 15px;
    justify-content: flex-start;
  }

  .deduction-code-btn,
  .deduction-code-loading {
    margin-left: 0;
    margin-bottom: 0;
    width: 50%;
  }

  .hint-desktop {
    display: none;
  }

  .to-pay-footer {
    display: none;
  }
}
