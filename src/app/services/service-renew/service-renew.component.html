<div class="payment-methods">
  <mat-progress-bar *ngIf="loading" mode="indeterminate"></mat-progress-bar>
  <div class="services-content-pc">
    <div class="services-content-pc-li"
      [class]="currentBillingCycle.id === billingCycle.id ? 'activeClick' : 'noActive'"
      (click)="setBillingCycle(billingCycle)" *ngFor="let billingCycle of billingCycles">
      <img *ngIf="currentBillingCycle.id === billingCycle.id" src="assets/images/products/checked.png"
        class="activeImg" />
      <div class="content-pc-li-title">{{ billingCycle.description }}</div>
      <div class="content-pc-li-amount">{{ product?.pricing?.HKD[billingCycle.id] }}港币/{{ billingCycle.unit }}</div>
      <div class="content-pc-li-discount">
        <span *ngIf="product?.getMonthlyAmount(billingCycle.id) < product?.pricing?.HKD['monthly']"
          style="text-decoration: line-through; color: #808080; margin-right: 5px">
          {{ product?.pricing?.HKD["monthly"] }}港币/月
        </span>
        {{ product?.getMonthlyAmount(billingCycle.id) }}港币/月
      </div>
      <div [class]="product?.getSaveAmount(billingCycle.id) > 0 ? 'li-discount-off' : 'li-discount-on'">
        节省{{ product?.getSaveAmount(billingCycle.id) }}%
      </div>
    </div>
  </div>
  <p class="hint-desktop" style="color: #808080; font-size: 12px">无限流量，超出{{currentFLow}}后限制速度；超出后可购买流量补充包。</p>
  <!--  <div class="services-content-mobile" *ngIf="hasDeduct">由于您存在使用优惠券的未支付订单，当前仅可支付原订单，如需修改订单内容，请<span style="color: '#ff5e5e'; cursor: pointer" (click)="cancelInvoice()">点击此处取消原订单</span>后继续操作，取消后优惠会原路退还</div>-->
  <div class="services-content-mobile" *ngIf="currentBillingCycle">
    <div class="div">
      <div class="parent">
        <div class="div1">{{ currentBillingCycle.description }}</div>
        <div class="div2">
          <img class="icon-" alt="" src="assets/images/products/change-billing-cycle.svg">
          <div class="div3" (click)="changeBillingCycle()">更换套餐</div>
        </div>
      </div>
      <div class="group">
        <div class="div4">{{ product?.pricing?.HKD[currentBillingCycle.id] }}港币/{{
          currentBillingCycle.unit }}</div>
        <div class="frame-parent">
          <div class="container">
            <div class="div5">{{ product?.pricing?.HKD["monthly"] }}港币/月</div>
            <div class="div6">{{ product?.getMonthlyAmount(currentBillingCycle.id) }}港币/月</div>
          </div>
          <div class="rectangle-parent">
            <div class="frame-child">
            </div>
            <div class="div7">节省{{ product?.getSaveAmount(currentBillingCycle.id) }}%</div>
          </div>
        </div>
      </div>
    </div>
    <p style="color: #808080; font-size: 12px; padding-bottom: 10px">无限流量，超出{{currentFLow}}后限制速度；超出后可购买流量补充包。</p>
  </div>
  <app-payment-methods></app-payment-methods>
  <app-deduction #deductionComponent [service]="service" [product]="product"
    [currentBillingCycle]="currentBillingCycle"></app-deduction>
  <app-payment-details #paymentDetailsComponent [buyType]="BuyType.SERVICE" [loading]="loading"
    [currentBillingCycle]="currentBillingCycle" [deductionAmount]="deductionComponent.deductionAmount$ | async"
    [origialAmount]="deductionComponent.getOrigialAmount()" [deDutiontoString]="deductionComponent.deDutiontoString()"
    [amountAfterDeduction]="deductionComponent.getAmountAfterDeduction()" [submitted]="submitted"
    [openBillingCyclesChange]="openBillingCyclesChange" (buy)="buy()"
    (setBillingCycle)="setBillingCycle($event)"></app-payment-details>
</div>