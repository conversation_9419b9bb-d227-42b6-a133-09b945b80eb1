import { Injectable } from "@angular/core";
import { CoreEventService, CORE_EVENTS } from "@flashvpn-io/web-core";
import { NotificationService, AFTER_USER_LOGIN, AFTER_USER_LOGOUT } from "@flashvpn-io/web-core";

@Injectable({
  providedIn: "root",
})
export class EventAdapterService {
  constructor(private coreEventService: CoreEventService, private notificationService: NotificationService) {
    // this.initializeEventMapping();
  }

  // private initializeEventMapping() {
  //     this.coreEventService.register(CORE_EVENTS.USER_LOGIN)
  //         .subscribe(data => this.notificationService.post(AFTER_USER_LOGIN, data));

  //     this.coreEventService.register(CORE_EVENTS.USER_LOGOUT)
  //         .subscribe(() => this.notificationService.post(AFTER_USER_LOGOUT));

  // }
}
