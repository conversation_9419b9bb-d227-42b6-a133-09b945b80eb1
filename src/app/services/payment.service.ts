import { Injectable, Input } from "@angular/core";
import { BehaviorSubject, interval, Observable, Subject } from "rxjs";
import { APIManager } from "@flashvpn-io/web-core";
import { Router } from "@angular/router";
import { filter, flatMap, map, take } from "rxjs/operators";
import { Invoice } from "@flashvpn-io/web-core";
import { DialogsService } from "../utils/dialogs/dialogs.service";
import { AFTER_INVOICE_PAID, AFTER_TRANSFER_FUND, NotificationService } from "@flashvpn-io/web-core";
import { PayInfo } from "@flashvpn-io/web-core";
import { Rule } from "@flashvpn-io/web-core";
import { Service } from "@flashvpn-io/web-core";
import { Product } from "@flashvpn-io/web-core";
import { Reward } from "@flashvpn-io/web-core";
import { AppService } from "../app.service";

type NewType = string;

@Injectable({
  providedIn: "root",
})
export class PaymentService {
  constructor(
    private router: Router,
    private apiManager: APIManager,
    private appService: AppService,
    private dialogsService: DialogsService,
    public notification: NotificationService
  ) { }

  private subject = new Subject<[string, any]>();

  public payInfo: PayInfo;
  post(name: string, value?: any) {
    this.subject.next([name, value]);
  }
  register(name: string): Observable<any> {
    return this.subject.pipe(filter((i) => i[0] === name)).pipe(map((i) => i[1]));
  }

  async flowPay(
    payInfo: Rule,
    paymentMethod: NewType,
    paymentToken: string,
    deductionAmount: number,
    service: Service,
    voucher: Reward,
    discountCode: string
  ) {
    if (payInfo.amount) {
      const postRule = {
        type: "flow",
        amount: payInfo?.amount,
        ruleId: payInfo?.id,
        serviceId: service.id,
      };
      const newPayInfo = new PayInfo();
      newPayInfo.paymentMethod = paymentMethod;
      newPayInfo.paymentToken = paymentToken;
      newPayInfo.billingCycles = payInfo?.id;
      this.payInfo = { ...newPayInfo };
      this.apiManager.createFlowInvoice(postRule).subscribe(
        (value) => {
          if (value) {
            const invoiceId = (value as any).invoiceid;
            if (deductionAmount > 0) {
              const payInvoice = new Invoice(null, null, null, null, null, null, invoiceId, null, null);
              this.apiManager.deductionInvoice(payInvoice, deductionAmount, voucher?.id.toString(), discountCode, null, true).subscribe(
                (value) => {
                  this.notification.post(AFTER_TRANSFER_FUND);
                  this.payBalanceInvoice(invoiceId, paymentMethod, paymentToken);
                },
                (res) => {
                  this.appService.snackUp(res?.error?.message);
                  this.post(PAYMENT_FAILURE, { ...this.payInfo, buyType: 2 });
                }
              );
            } else {
              this.payBalanceInvoice(invoiceId, paymentMethod, paymentToken);
            }
          }
        },
        (res) => {
          this.appService.snackUp(res?.error?.message);
          this.post(PAYMENT_FAILURE, { ...this.payInfo, buyType: 2 });
        }
      );
    }
  }

  async payBalanceInvoice(invoiceId: string, paymentMethod: string, paymentToken: string) {
    this.apiManager.payBalanceInvoice(invoiceId, paymentMethod, paymentToken).subscribe(
      (data) => {
        if (data.redirect_url !== undefined && ["alipay", "wechat"].includes(paymentMethod)) {
          if (this.appService.isElectron() || this.appService.isExtension()) {
            window.open(data.redirect_url);
          } else {
            window.location = data.redirect_url;
          }
        }
        this.pullingInvoiceByType(invoiceId, 2);
        this.post(IN_PAYMENT, { ...this.payInfo, buyType: 2 });
      },
      (res) => {
        this.post(PAYMENT_FAILURE, { ...this.payInfo, buyType: 2 });
        if (res?.error?.error === "InsufficientPoints") {
          this.appService.snackUp("余额不足");
        } else {
          this.appService.snackUp(res?.error?.message);
        }
      }
    );
  }

  async servicePay(
    payInfo: Rule,
    paymentMethod: string,
    paymentToken: string,
    deductionAmount: number,
    service: Service,
    voucher: Reward,
    discountCode: string
  ) {
    let payInvoice;
    const newPayInfo = new PayInfo();
    newPayInfo.paymentMethod = paymentMethod;
    newPayInfo.paymentToken = paymentToken;
    newPayInfo.billingCycles = payInfo?.id;
    this.payInfo = newPayInfo;
    try {
      // 校验是否有服务

      if (service.billingcycle !== payInfo.id) {
        await this.apiManager.updateBillingCycle(service.id, { billingCycle: payInfo.id }).toPromise();
      }
      payInvoice = await this.apiManager.fetchLatestInvoice(service).toPromise();
      if (service.status === "Suspended") {
        payInvoice = await this.apiManager.regenerateInvoice(payInvoice).toPromise();
      }
      // payInvoice = payInvoice as Invoice;
      this.payInfo.invoiceId = payInvoice.id;
      if (deductionAmount > 0) {
        this.apiManager.deductionInvoice(payInvoice, deductionAmount, voucher?.id.toString(), discountCode, payInfo.id).subscribe(
          (value) => {
            this.notification.post(AFTER_TRANSFER_FUND);
            this.payInvoice(payInvoice, paymentMethod, paymentToken);
          },
          (res) => {
            this.post(PAYMENT_FAILURE, { ...this.payInfo, buyType: 1 });
            this.appService.snackUp(res?.error?.message);
          }
        );
      } else {
        await this.payInvoice(payInvoice, paymentMethod, paymentToken);
      }
    } catch (err) {
      this.appService.snackUp(err?.error?.message);
      this.post(PAYMENT_FAILURE, { ...this.payInfo, buyType: 1 });
    }
  }

  async payInvoice(payInvoice: Invoice, paymentMethod: string, paymentToken: string) {
    this.apiManager.payInvoice(payInvoice, paymentMethod, paymentToken).subscribe(
      (data) => {
        if (data.redirect_url !== undefined && ["alipay", "wechat"].includes(paymentMethod)) {
          if (this.appService.isElectron() || this.appService.isExtension()) {
            window.open(data.redirect_url);
          } else {
            window.location = data.redirect_url;
          }
        }
        this.post(IN_PAYMENT, { ...this.payInfo, buyType: 1 });
        this.pullingInvoiceByType(payInvoice?.id, 1);
      },
      (res) => {
        this.post(PAYMENT_FAILURE, { ...this.payInfo, buyType: 1 });
        if (res?.error?.error === "InsufficientPoints") {
          this.appService.snackUp("余额不足");
        } else {
          this.appService.snackUp(res?.error?.message);
        }
      }
    );
  }

  pullingInvoice(invoiceId) {
    const dis = interval(3000)
      .pipe(take(30))
      .pipe(flatMap(() => this.apiManager.fetchInvoiceDetail(invoiceId)))
      .pipe(filter((i) => i.status === "Paid"))
      .subscribe(
        (invoice) => {
          this.router.navigate(["payment-status"], { queryParams: { status: PAYMENT_SUCCESS } });
          this.postGtm(invoice);
          console.log("Invoice paid");
          this.notification.post(AFTER_INVOICE_PAID, {});
          dis.unsubscribe();
        },
        (res) => {
          this.router.navigate(["payment-status"], { queryParams: { status: PAYMENT_FAILURE } });
          this.appService.snackUp(res?.error?.message);
        },
        () => {
          this.router.navigate(["payment-status"], { queryParams: { status: PAYMENT_FAILURE } });
          this.appService.snackUp(this.appService.translate("FailedToPayInvoice"));
        }
      );
  }

  pullingInvoiceByType(invoiceId, buyType) {
    const dis = interval(3000)
      .pipe(take(30))
      .pipe(flatMap(() => this.apiManager.fetchInvoiceDetail(invoiceId)))
      .pipe(filter((i) => i.status === "Paid"))
      .subscribe(
        (invoice) => {
          this.post(PAYMENT_SUCCESS, { ...this.payInfo, buyType });
          this.postGtm(invoice);
          dis.unsubscribe();
        },
        (res) => {
          this.post(PAYMENT_FAILURE, { ...this.payInfo, buyType });
          this.appService.snackUp(res?.error?.message);
        },
        () => {
          this.post(PAYMENT_FAILURE, { ...this.payInfo, buyType });
          this.appService.snackUp(this.appService.translate("FailedToPayInvoice"));
        }
      );
  }

  postGtm(invoice: Invoice) {
    const dataLayer = (window as any).dataLayer;
    function gtag(...arg) {
      dataLayer.push(arguments);
    }
    gtag("event", "purchase", {
      transaction_id: invoice?.id,
      value: invoice?.total,
      currency: "HKD",
      items: [
        {
          item_id: invoice?.id,
          item_name: invoice?.description,
          currency: "HKD",
          price: invoice?.total,
        },
      ],
    });
  }
}

export const PAYMENT_SUBMIT = "PAYMENT_SUBMIT";

export const IN_PAYMENT = "IN_PAYMENT";

export const PAYMENT_SUCCESS = "PAYMENT_SUCCESS";

export const PAYMENT_FAILURE = "PAYMENT_FAILURE";

export const DESHBOARD_SEND_DATA = "DESHBOARD_SEND_DATA";

export const SELECT_VOUCHER_SEND_DATA = "SELECT_VOUCHER_SEND_DATA";

export const PAYMENT_METHODS_SEND_DATA = "PAYMENT_METHODS_SEND_DATA";

export const MOBLIE_PAYMETHOD = "MOBLIE_PAYMETHOD";

export const UPDATE_ORDER = "UPDATE_ORDER";

export const CANCEL_ORDER = "CANCEL_ORDER";
