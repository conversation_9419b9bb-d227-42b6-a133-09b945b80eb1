
<mat-progress-bar *ngIf="loading" mode="indeterminate"></mat-progress-bar>
<div *ngIf="vouchers&&vouchers.length>0">
  <mat-radio-group [ngModel]="currentVoucher" >
    <div class="select-voucher">
      <div
        class="select-voucher-card"
        *ngFor="let voucher of vouchers"
      >
        <div
          class="select-voucher-card-left"
        >
          <div class="award-box">
            <div class="award">{{voucher.award}}</div>
            <div class="award-ccy">港币</div>
          </div>
          <div id='bottomCover'></div>
          <div id='topCover'></div>
        </div>
        <div class="select-voucher-card-right">
          <div class="select-voucher-card-text">
            <span>{{voucher.title}}</span>
            <div class="select-voucher-card-note">
              <span>{{voucher.note}}</span>
            </div>
          </div>
          <div class="select-voucher-card-botton">
              <mat-radio-button [value]="voucher.id" (change)="changeVoucher($event.value)"></mat-radio-button>
          </div>
        </div>
      </div>
    </div>
  </mat-radio-group>
  <div class="botton-box">
    <button class="select-voucher-btn" mat-raised-button color="warn" id="select-voucher-btn" (click)="submit()">确认</button>
  </div>
</div>
<div *ngIf="!loading&&!(vouchers&&vouchers.length>0)">
  <div class="no-voucher">无可用优惠券</div>
</div>

