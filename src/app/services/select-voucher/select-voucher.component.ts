import { Component, OnInit } from "@angular/core";
import { AppService } from "src/app/app.service";
import { APIManager } from "@flashvpn-io/web-core";
import { NotificationService } from "@flashvpn-io/web-core";
import { DialogsService } from "../../utils/dialogs/dialogs.service";
import { Router } from "@angular/router";
import { Location } from "@angular/common";
import { PaymentService, SELECT_VOUCHER_SEND_DATA } from "../payment.service";
import { Reward } from "@flashvpn-io/web-core";
import { RewardService } from "@flashvpn-io/web-core";

@Component({
  selector: "app-select-voucher",
  templateUrl: "./select-voucher.component.html",
  styleUrls: ["./select-voucher.component.css"],
})
export class SelectVoucherComponent implements OnInit {
  public vouchers: Reward[] = [];

  public currentVoucher: number;

  public loading = false;

  constructor(
    private appService: AppService,
    private apiManager: APIManager,
    private rewardService: RewardService,
    private notificationService: NotificationService,
    private dialogsService: DialogsService,
    private location: Location,
    private paymentService: PaymentService,
    private router: Router
  ) {}

  ngOnInit(): void {}
  changeVoucher(value) {
    this.currentVoucher = value;
  }

  submit() {
    if (this.dialogsService.isMobile() || window.innerWidth <= 599) {
      // this.router.navigate(['payment-methods']);
      this.location.back();
    } else {
      this.dialogsService.closeDialog();
    }
    const currentVoucherInfo = this.rewardService.coupons$.value?.filter((v) => v.id === this.currentVoucher)?.pop();
    this.paymentService.post(SELECT_VOUCHER_SEND_DATA, currentVoucherInfo);
  }
}
