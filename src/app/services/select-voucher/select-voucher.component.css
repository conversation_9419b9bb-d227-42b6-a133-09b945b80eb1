.select-voucher{
  width: auto;
  display: flex;
  flex-wrap: wrap;
  flex-direction: initial;
  gap: 24px;
  padding: 24px;
}
.select-voucher-card {
  width: 343px;
  height: 84px;
  background: #FFFFFF;
  box-shadow: 0px 0px 10px rgba(255, 94, 94, 0.1);
  display: flex;
  align-items: center;
  border-radius: 8px;
  position: relative;
}
.select-voucher-card-right{
  display: flex;
  justify-content: space-between;
  align-items: center;
  float: right;
  width: 100%;
}
.select-voucher-card-text{
  display: flex;
  flex-direction: column;
  margin-left: 10px;
  gap: 10px;
  width: auto;
  color: #505050;
  font-size: 16px;
  font-weight: 500;
  line-height: 14px;
}
.select-voucher-card-botton{
  display: flex;
  justify-content: center;
  align-items: center;
  width: 70px;
}
@media screen and (max-width: 599px){
  .select-voucher{
    gap: 14px;
    padding: 0px;
  }
  .select-voucher-card{
    width: 100%;
  }
}
.select-voucher-card-left{
  width: 90px;
  height: 84px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right:1px solid transparent;
  background: linear-gradient(white,white) padding-box,
  repeating-linear-gradient(-45deg,#999999 0, #ccc 0.1em,white 0,white 0.3em);
  border-radius: 8px;
}

.select-voucher-card-left::before,.select-voucher-card-left::after{
  content: ''
}

.select-voucher-card-left::before, #bottomCover {
  position: absolute;
  right: 0;
  bottom: -8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #FFFFFF;
  z-index: 1;
  box-shadow: inset 0 0px 10px rgba(255, 94, 94, 0.1);
}

.select-voucher-card-left::after, #topCover {
  position: absolute;
  top: -8px;
  right: 0;
  bottom: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #FFFFFF;
  z-index: 1;
  box-shadow: inset 0 0px 10px rgba(255, 94, 94, 0.1);
}

.select-voucher-card-left::after, #topCover{
  left: inherit;
  right: -8px;
  box-shadow: inset 0 0px 10px rgba(255, 94, 94, 0.1);
}

.select-voucher-card-left::before, #bottomCover{
  left: inherit;
  right: -8px;
  box-shadow: inset 0 0px 10px rgba(255, 94, 94, 0.1);
}

#topCover{
  right: -15px;
  top: -20px;
}

#bottomCover{
  right: -15px;
  bottom: -20px;
}

#bottomCover, #topCover{
  box-shadow: none;
  width: 24px;
  height: 24px;
  filter: blur(3px);
  z-index: 2;
}
.received{
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 15px;
  color: #FF5E5E;
  padding: 0 16px;
}
.select-voucher-card-note{
  font-size: 12px;
  line-height: 14px;
}
.award-box{
  color: #FF5E5E;
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.award{
  text-align: center;
  font-size: 26px;
  font-weight: 500;
  line-height: 26px;
}
.award-ccy{
  text-align: center;
  font-size: 12px;
  line-height: 12px;
}
.botton-box{
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 30px 0;
}
.select-voucher-btn{
  width: 245px;
  height: 52px;
}
.no-voucher{
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #505050;
  font-size: 16px;
  font-weight: 500;
  line-height: 14px;
}
@media screen and (max-width: 599px){
  .select-voucher{
    margin-bottom: 100px;
  }
  .botton-box{
    position: fixed;
    bottom: 0;
    height: 100px;
    left: 0;
    right: 0;
    margin: 0;
    width: 100%;
    background-color: #FFFFFF;
    z-index: 9;
  }
  .select-voucher-btn{
    width: 90%;
  }
  .no-voucher{
    height: auto;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
  }
  .select-voucher-card-list{
    padding: 24px 7px;
  }
}
