import { Component, OnInit, ViewChild } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { APIManager } from "@flashvpn-io/web-core";
import { AppService } from "src/app/app.service";
import * as _ from "lodash";
import { UsersService } from "@flashvpn-io/web-core";
import { MatMenuTrigger } from "@angular/material/menu";
import { MatDialog } from "@angular/material/dialog";
import { WalletsService } from "@flashvpn-io/web-core";
import { DialogsService } from "../../utils/dialogs/dialogs.service";
import { IN_PAYMENT, PAYMENT_FAILURE, PAYMENT_SUBMIT, PAYMENT_SUCCESS, PaymentService } from "../payment.service";
import { LoadBottonComponent } from "../../utils/load-botton/load-botton.component";
import * as moment from "moment";
import { RewardService } from "@flashvpn-io/web-core";
import { BuyType, DeductionType, Invoice, InvoicePage, RouterBuyType } from "@flashvpn-io/web-core";
import { Service } from "@flashvpn-io/web-core";
import { BillingCycles, defaultBillingCycles } from "@flashvpn-io/web-core";
import { FlowDataRule } from "@flashvpn-io/web-core";
import { filter, tap } from "rxjs/operators";

@Component({
  selector: "app-dashboard",
  templateUrl: "./dashboard.component.html",
  styleUrls: ["./dashboard.component.css"],
})
export class DashboardComponent implements OnInit {
  @ViewChild("trigger", { read: MatMenuTrigger }) trigger?: MatMenuTrigger;
  @ViewChild("loadBottonComponent") loadBottonComponent: LoadBottonComponent;
  public service: Service;
  public services: Service[];
  public apps: string[];
  public displayedColumns: string[] = ["description", "datepaid", "paymentmethod", "total"];
  public submitted = false;
  public loading = false;
  public invoices = [];
  public helpsArticles;
  public paginator = {
    length: 0,
    pageSize: 3,
    pageIndex: 0,
  };
  public currentDeduction: string;
  public discountCode: string;
  public billingCycles: BillingCycles[] = defaultBillingCycles;

  public flowdataRule: FlowDataRule[];

  public systemCollection: string[] = ["Windows", "macOS", "Android", "iOS"];

  public expiredAt: string;

  constructor(
    public router: Router,
    public appService: AppService,
    public userService: UsersService,
    public walletService: WalletsService,
    public dialogsService: DialogsService,
    public dialog: MatDialog,
    private apiManager: APIManager,
    private paymentService: PaymentService,
    public rewardService: RewardService,
    public route: ActivatedRoute,
  ) {
    if (!(localStorage.getItem("token"))) {
      this.router.navigate(["services", "welcome"]);
      // this.router.navigate(["center"]);
    }
  }

  // 判断系统
  OnGetOS() {
    const agent = navigator.userAgent.toLowerCase();
    const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;
    const MAC = /macintosh|mac os x/i.test(navigator.userAgent) && !(window as any).MSStream;
    const android = /Android/.test(navigator.userAgent) && !(window as any).MSStream;

    if (agent.indexOf("win32") >= 0 || agent.indexOf("wow32") >= 0) {
      return "Windows"; // 此处根据需求调整
    }
    if (agent.indexOf("win64") >= 0 || agent.indexOf("wow64") >= 0) {
      return "Windows"; // 此处根据需求调整
    }
    if (iOS) {
      return "iOS";
    }
    if (MAC) {
      return "macOS";
    }
    if (android) {
      return "Android";
    }
    return "Windows";
  }

  async ngOnInit() {
    // 识别系统放到第一位
    const systemItem = this.OnGetOS();
    const ids = this.systemCollection.indexOf(systemItem);
    this.systemCollection.splice(ids, 1);
    this.systemCollection.unshift(systemItem);

    this.route.queryParamMap.subscribe(async (params) => {
      const serviceId = params.get("id");
      if (serviceId) {
        localStorage.setItem("currentServiceId", serviceId)
      }
    });

    this.paymentService.register(IN_PAYMENT).subscribe(async () => {
      this.dialog.closeAll();
      await this.router.navigate(["payment-status"], { queryParams: { ..._, status: IN_PAYMENT } });
    });
    this.paymentService.register(PAYMENT_SUCCESS).subscribe(async () => {
      this.dialog.closeAll();
      await this.router.navigate(["payment-status"], { queryParams: { ..._, status: PAYMENT_SUCCESS } });
    });
    this.paymentService.register(PAYMENT_FAILURE).subscribe(async () => {
      this.dialog.closeAll();
      await this.router.navigate(["payment-status"], { queryParams: { ..._, status: PAYMENT_FAILURE } });
    });
  }

  getHelpsUrl() {
    this.apiManager.getHelpsArticlesByShow().subscribe((value) => {
      this.helpsArticles = JSON.parse(JSON.stringify(value.data));
    });
  }

  goToHelps(type, code) {
    const articles = this.helpsArticles.attributes.results;
    const urlItems = articles.filter((item) => item.platform.toLowerCase() === type.toLowerCase());
    const urlItem = urlItems.find((item) => item?.title?.toLowerCase().indexOf(code.toLowerCase()) > -1);
    if (urlItem) {
      this.router.navigate([`/helps/article/${urlItem.id}`]);
    }
  }

  goToWelfare() {
    this.router.navigate(["welfare"]);
  }
}
