.serve-layout {
  margin: 0 auto;
  max-width: 1300px;
}

.serve {
  width: 100%;
  display: grid;
  align-items: flex-start;
  grid-auto-flow: row;
  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  gap: 10px;
  padding: 0 0 40px 0;
}

.serveItem {
  width: 100%;
  height: 100%;
  box-shadow: 0px 0px 10px 0px rgba(255, 94, 94, 0.1);
  position: relative;
}

.appServiceTile {
  width: 100%;
  height: 100%;
}

.serviceTitle {
  font-size: 16px;
  font-weight: 600;
  line-height: 16px;
  text-align: left;
  color: rgba(0, 0, 0, 1);
  padding: 0 0 20px 0;
}

.appVersionBox {
  padding-bottom: 40px;
}

.title {
  font-size: 24px;
  font-weight: 500;
  line-height: 24px;
}

.subTitle {
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  text-align: left;
  color: rgba(128, 128, 128, 1);
  margin-bottom: 10px;
}

.invite {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding-bottom: 24px;
}

.inviteBox {
  display: flex;
  grid-template-columns: 1fr 1fr;
  padding: 20px;
  box-sizing: border-box;
}

.inviteItemBox {
  width: 100%;
  display: grid;
  flex-direction: column;
  align-items: center;
  grid-template-rows: 2fr 1fr;
  gap: 10px;
}

.showDesktop {
  display: block;
}

.showMobile {
  display: none;
}

@media screen and (max-width: 599px) {
  .serve {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(auto-fill, minmax(200px, 1fr));
    gap: 30px;
    padding: 10px 0 30px 0;
  }

  .resetTime {
    height: 30px;
  }

  .serveItemBox {
    padding: 20px;
  }

  .progressBox {
    box-shadow: none;
  }

  .showDesktop {
    display: none;
  }

  .showMobile {
    display: block;
  }
}