<mat-progress-bar *ngIf="loading" mode="indeterminate"></mat-progress-bar>
<div fxLayout="row" fxLayoutAlign="start center" fxLayout.lt-sm="column" fxLayoutGap.lt-sm="5px" fxLayoutAlign.lt-sm="start start">
  <div class="register-buy">
    <div class="box">
      <div class="box-item">
        <div class="order">
          <div class="title"><div class="number">1</div>确认账单金额</div>
          <div class="order-item">
            <div class="order-title-1">{{product?.pricing?.HKD[cycles]}}港币/{{billingCycles[cycles]['unit']}}</div>
            <div class="order-title-2">
              <span *ngIf="product?.getMonthlyAmount(cycles)<product?.pricing?.HKD['monthly']" style="text-decoration: line-through;color: #808080;margin-right: 5px">{{product?.pricing?.HKD['monthly']}}港币/月</span>{{product?.getMonthlyAmount(cycles)}}港币/月
            </div>
            <div [class]="product?.getSaveAmount(cycles)>0?'order-Offer-1':'order-Offer-2'">节省{{product?.getSaveAmount(cycles)}}%</div>
          </div>
          <div class="order-item-mobile">
            <div class="order-title-1">{{product?.pricing?.HKD[cycles]}}港币/{{billingCycles[cycles]['unit']}}</div>
            <div class="order-save">
              <div class="order-title-2">
                <span *ngIf="product?.getMonthlyAmount(cycles)<product?.pricing?.HKD['monthly']" style="text-decoration: line-through;color: #808080;margin-right: 5px">{{product?.pricing?.HKD['monthly']}}港币/月</span>{{product?.getMonthlyAmount(cycles)}}港币/月
              </div>
              <div [class]="product?.getSaveAmount(cycles)>0?'order-Offer-1':'order-Offer-2'">节省{{product?.getSaveAmount(cycles)}}%</div>
            </div>
          </div>
        </div>
        <div class="register">
          <div class="title"><div class="number">2</div>填写邮箱及设置密码</div>
          <div id="tipsInfo" class="none_tips"><mat-icon>error</mat-icon>{{tipsInfo}}</div>
          <div id="signupForm" >
            <div>
              <mat-form-field class="form-input field-gap" floatLabel="never" appearance="fill" empty="false" autofilled="false">
                <svg MatPrefix style="margin-right: 20px;" width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M20 2C20 0.9 19.1 0 18 0H2C0.9 0 0 0.9 0 2V14C0 15.1 0.9 16 2 16H18C19.1 16 20 15.1 20 14V2ZM18 2L10 7L2 2H18ZM10 9L2 4V14H18V4L10 9Z" fill="black" fill-opacity="0.54"/>
                </svg>
                <input id="emailInput" matInput i18n-placeholder placeholder="Please enter your account number" (input)="sendNewValue()" (focus)='emailGetFocus(true)' (blur)="emailGetFocus(false)"  [(ngModel)]="email" name="email">
              </mat-form-field>
              <mat-nav-list class="email-list" *ngIf="emailListShow" >
                <a href="javascript:void(0)" mat-list-item *ngFor="let item of emailTips;" (mousedown)="setEmail($event,item)">
                  <span mat-line>{{email.indexOf('@')>-1?email.substring(0,email.indexOf('@')+1)+item:email+'@'+item}}</span>
                </a>
              </mat-nav-list>
            </div>
            <div>
              <mat-form-field class="form-input" floatLabel="never" appearance="fill" empty="false" autofilled="false">
                <svg MatPrefix style="margin-right: 20px;"  width="16" height="22" viewBox="0 0 16 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M13 7.5H14C15.1 7.5 16 8.4 16 9.5V19.5C16 20.6 15.1 21.5 14 21.5H2C0.9 21.5 0 20.6 0 19.5V9.5C0 8.4 0.9 7.5 2 7.5H3V5.5C3 2.74 5.24 0.5 8 0.5C10.76 0.5 13 2.74 13 5.5V7.5ZM8 2.5C6.34 2.5 5 3.84 5 5.5V7.5H11V5.5C11 3.84 9.66 2.5 8 2.5ZM2 19.5V9.5H14V19.5H2ZM10 14.5C10 15.6 9.1 16.5 8 16.5C6.9 16.5 6 15.6 6 14.5C6 13.4 6.9 12.5 8 12.5C9.1 12.5 10 13.4 10 14.5Z" fill="black" fill-opacity="0.54"/>
                </svg>
                <input id="passwordInput" (input)="passwordChange()" (blur)="passwordGetFocus()" matInput i18n-placeholder placeholder="Enter your password" [type]="hide ? 'password' : 'text'" [(ngModel)]="password" name="password">
                <a mat-icon-button matSuffix style="margin-left: 20px;display: flex;align-items: center;justify-content: center;" (click)="hide = !hide" [attr.aria-label]="'Hide password'" [attr.aria-pressed]="hide">
                  <mat-icon>{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
                </a>
              </mat-form-field>
            </div>
          </div>
        </div>
      </div>
      <div class="buy">
        <div class="title"><div class="number">3</div>选择支付方式</div>
        <div class="buy-box">
          <mat-radio-group
            aria-labelledby="methods-radio-group-label"
            class="methods-radio-group"
            [(ngModel)]="currentMethod"
          >
            <mat-radio-button class="methods-radio-button" (change)="methodOnChange($event)" *ngFor="let payMethod of payMethodList" [value]="payMethod.payType">
              <div class="methods-radio-option">
                <img [src]="payMethod.imgUrl" alt="" /><span style="margin-left: 10px">{{payMethod.payName}}</span>
              </div>
            </mat-radio-button>
          </mat-radio-group>
        </div>
        <div class="buy-box-mobile">
          <div class="select-pay-method" (click)="openSelectPayMethod()" >
            <div class="select-pay-method-box">
              <div class="select-pay-method-title" i18n>支付方式</div>
              <div class="select-pay-method-info">
                <img [src]="payMethod?.imgUrl" alt="" />
                <span style="margin-left: 3px">{{payMethod?.payName}}</span>
              </div>
            </div>
            <div class="select-pay-method-arrow">
              <svg width="9" height="13" viewBox="0 0 9 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 1L7 6.5L1 12" stroke="black" stroke-width="2"/>
              </svg>
            </div>
          </div>
        </div>
        <div class="card-box" *ngIf="currentMethod==='creditcard'">
          <div id="cardInfo" #cardInfo></div>
        </div>
        <div class="payment-box">
          <button *ngIf="!submitted" [disabled]="payDisabled()" class="payment-btn" mat-raised-button color="warn" (click)="buy()" >支付{{product?.pricing?.HKD[cycles]}}港币</button>
          <app-load-botton *ngIf="submitted"></app-load-botton>
        </div>
      </div>
    </div>
  </div>
</div>
