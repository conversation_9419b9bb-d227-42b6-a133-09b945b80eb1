import { Component, ElementRef, OnInit, ViewChild } from "@angular/core";
import { AppService } from "src/app/app.service";
import { APIManager } from "@flashvpn-io/web-core";
import { NotificationService } from "@flashvpn-io/web-core";
import { IN_PAYMENT, MOBLIE_PAYMETHOD, PAYMENT_FAILURE, PAYMENT_SUCCESS, PaymentService } from "../payment.service";
import { Location } from "@angular/common";
import { MatBottomSheet } from "@angular/material/bottom-sheet";
import { emailList } from "../../../constants/email-list.constants";
import { environment } from "../../../environments/environment";
import { ActivatedRoute, Router } from "@angular/router";
import { UsersService } from "@flashvpn-io/web-core";
import { MobilePaymethodComponent } from "../../component/mobile-paymethod.component";
import { Product } from "@flashvpn-io/web-core";
import { Signup } from "@flashvpn-io/web-core";
import { User } from "@flashvpn-io/web-core";
import { Rule } from "@flashvpn-io/web-core";

@Component({
  selector: "app-register-buy",
  templateUrl: "./register-buy.component.html",
  styleUrls: ["./register-buy.component.css"],
})
export class RegisterBuyComponent implements OnInit {
  public billingCycles = {
    monthly: { title: "按月支付", unit: "月" },
    quarterly: { title: "按季度支付", unit: "季度" },
    semiannually: { title: "按半年支付", unit: "半年" },
    annually: { title: "按年支付", unit: "年" },
  };

  public payMethodList = [
    {
      payType: "wechat",
      imgUrl: "assets/images/products/wechat.svg",
      payName: "微信支付",
    },
    {
      payType: "alipay",
      imgUrl: "assets/images/products/alipay.svg",
      payName: "支付宝",
    },
    // {
    //   payType: 'creditcard',
    //   imgUrl: 'assets/images/products/bank.svg',
    //   payName: '银行信用卡支付',
    // }
  ];

  public hide = true;

  public email: any;

  public password: any;

  public emailError = false;

  public passwordError = false;

  public emailErrorInfo: any;

  public passwordErrorInfo: any;

  public tipsInfo: any;

  public emailListShow = false;

  public emailTips: any;

  public currentMethod: string;

  public submitted = false;

  @ViewChild("cardInfo") cardInfo: ElementRef;

  public card: any;

  public elements: any;

  public loading = false;

  public product: Product;

  private products: Product[];

  public cycles: string;

  public payMethod;

  constructor(
    private appService: AppService,
    private apiManager: APIManager,
    private notificationService: NotificationService,
    private paymentService: PaymentService,
    private location: Location,
    public usersService: UsersService,
    public route: ActivatedRoute,
    public router: Router,
    private bottomSheet: MatBottomSheet
  ) {
    this.payMethod = this.payMethodList[0];
    this.currentMethod = this.payMethodList[0].payType;
  }

  ngOnInit(): void {
    this.route.queryParamMap.subscribe(async (params) => {
      this.cycles = params.get("type");
      this.getProducts();
    });
    this.paymentService.register(IN_PAYMENT).subscribe(async (_) => {
      await this.router.navigate(["payment-status"], { queryParams: { ..._, status: IN_PAYMENT } });
    });
    this.paymentService.register(PAYMENT_SUCCESS).subscribe(async (_) => {
      await this.router.navigate(["payment-status"], { queryParams: { ..._, status: PAYMENT_SUCCESS } });
    });
    this.paymentService.register(PAYMENT_FAILURE).subscribe(async (_) => {
      await this.router.navigate(["payment-status"], { queryParams: { ..._, status: PAYMENT_FAILURE } });
    });
    this.paymentService.register(MOBLIE_PAYMETHOD).subscribe(async (_) => {
      this.payMethod = { ..._ };
      this.currentMethod = _.payType;
    });
  }

  async getProducts() {
    this.loading = true;
    let products = await this.apiManager.fetchProducts().toPromise();
    if (products && products.length > 0) {
      products = products.map((p) => Product.fromData(p)).sort((a, b) => a.id - b.id);
      this.products = products;
      this.product = products[0];
    } else {
      this.appService.snackUp("未找到对应套餐，请联系客服");
    }
    this.loading = false;
  }

  onChange({ error }) {
    if (error) {
      this.appService.snackUp(error.message);
    }
    if (this.card?.detectChanges) {
      this.card?.detectChanges();
    }
    this.submitted = false;
  }

  checkEmail() {
    const errorInfo = {
      isError: false,
      errorTips: null,
    };
    const reg = /(^\s+)|(\s+$)|\s+|\++/g;
    const isReg = reg.test(this.email);
    if (!/foxit.io/.test(this.email) && isReg) {
      errorInfo.isError = true;
      errorInfo.errorTips = this.appService.translate("EmailIllegalCharacters");
      return errorInfo;
    }
    if (this.email && this.email.length > 40) {
      errorInfo.isError = true;
      errorInfo.errorTips = this.appService.translate("EmailLongError");
      return errorInfo;
    }
    return errorInfo;
  }

  setEmailError(error, tips) {
    if (error) {
      document.getElementById("emailInput").style.color = "#FF5E5E";
      this.emailErrorInfo = tips;
      this.tipsInfo = tips;
      document.getElementById("tipsInfo").className = "input_tips fade-in-fwd";
      document.getElementById("signupForm").className = "slide-bottom";
    } else if (this.emailError && !error) {
      if (this.passwordError) {
        this.emailErrorInfo = null;
        this.tipsInfo = this.passwordErrorInfo;
        document.getElementById("emailInput").style.color = "";
      } else {
        document.getElementById("tipsInfo").className = "input_tips fade-out-bck";
        document.getElementById("signupForm").className = "slide-top";
        document.getElementById("emailInput").style.color = "";
        this.emailErrorInfo = null;
        this.tipsInfo = null;
      }
    }
    this.emailError = error;
  }

  setPasswordError(error, tips) {
    if (error) {
      document.getElementById("passwordInput").style.color = "#FF5E5E";
      this.passwordErrorInfo = tips;
      this.tipsInfo = tips;
      document.getElementById("tipsInfo").className = "input_tips fade-in-fwd";
      document.getElementById("signupForm").className = "slide-bottom";
    } else if (this.passwordError && !error) {
      if (this.emailError) {
        this.passwordErrorInfo = null;
        this.tipsInfo = this.emailErrorInfo;
        document.getElementById("passwordInput").style.color = "";
      } else {
        document.getElementById("tipsInfo").className = "input_tips fade-out-bck";
        document.getElementById("signupForm").className = "slide-top";
        document.getElementById("passwordInput").style.color = "";
        this.passwordErrorInfo = null;
        this.tipsInfo = null;
      }
    }
    this.passwordError = error;
  }

  emailPrompt() {
    if (this.email && this.email.indexOf("@") > -1) {
      this.emailTips = emailList.filter((emailStr) => {
        return emailStr.indexOf(this.email.substring(this.email.indexOf("@") + 1, this.email.length)) > -1;
      });
    } else {
      this.emailTips = emailList;
    }
    this.emailListShow = this.email && this.emailTips && this.emailTips.length > 0 && !this.emailError ? true : false;
  }

  sendNewValue() {
    const errorInfo = this.checkEmail();
    this.setEmailError(errorInfo.isError, errorInfo.errorTips);
    this.emailPrompt();
  }

  checkPassword() {
    const errorInfo = {
      isError: false,
      errorTips: null,
    };
    const reg = /(^\s+)|(\s+$)|\s+|\++/g;
    const isReg = reg.test(this.password);
    const reg2 = /[\u4e00-\u9fa5]+/g;
    const isReg2 = reg2.test(this.password);
    if (isReg || isReg2) {
      errorInfo.isError = true;
      errorInfo.errorTips = this.appService.translate("PasswordIllegalCharacters");
      return errorInfo;
    }
    if (this.password && this.password.length > 20) {
      errorInfo.isError = true;
      errorInfo.errorTips = this.appService.translate("PasswordLongError");
      return errorInfo;
    }
    return errorInfo;
  }

  passwordChange() {
    const errorInfo = this.checkPassword();
    this.setPasswordError(errorInfo.isError, errorInfo.errorTips);
  }

  emailGetFocus(value) {
    if (value) {
      this.emailPrompt();
    } else {
      if (this.email && !this.emailError) {
        this.emailListShow = false;
        const reg = /^\w+((.\w+)|(-\w+))@[A-Za-z0-9]+((.|-)[A-Za-z0-9]+).[A-Za-z0-9]+$/;
        this.setEmailError(!reg.test(this.email), this.appService.translate("EmailFormatError"));
      }
    }
  }

  setEmail(event, item) {
    event.preventDefault();
    this.email = this.email.indexOf("@") > -1 ? this.email.substring(0, this.email.indexOf("@") + 1) + item : this.email + "@" + item;
    this.emailListShow = false;
  }

  passwordGetFocus() {
    const errorInfo = this.checkPassword();
    if (this.password && this.password.length < 8) {
      errorInfo.isError = true;
      errorInfo.errorTips = this.appService.translate("PasswordShortError");
    }
    this.setPasswordError(errorInfo.isError, errorInfo.errorTips);
  }

  methodOnChange(event) {
    this.currentMethod = event?.value;
  }

  async buy() {
    if (this.email && this.password && !this.emailError && !this.passwordError) {
      this.submitted = true;
      const paymentToken = "";
      if (this.currentMethod === "creditcard") {
        this.submitted = false;
        return;
      }
      const utmSource = this.route.snapshot.queryParams.utm_source || localStorage.getItem("utmSource") || undefined;
      const utmMedium = this.route.snapshot.queryParams.utm_medium || localStorage.getItem("utmMedium") || undefined;
      const utmCampaign = this.route.snapshot.queryParams.utm_campaign || localStorage.getItem("utmCampaign") || undefined;
      const utmTerm = this.route.snapshot.queryParams.utm_term || localStorage.getItem("utmTerm") || undefined;
      const utmContent = this.route.snapshot.queryParams.utm_content || localStorage.getItem("utmContent") || undefined;
      const registerInfo: Signup = {
        email: this.email,
        password: this.password,
        inviterCode: undefined,
        utmSource,
        utmMedium,
        utmCampaign,
        utmTerm,
        utmContent,
        sendEmail: false,
      };
      this.apiManager.createUser(registerInfo).subscribe(
        async (user) => {
          await this.usersService.loginByUser(user as User);
          await this.paymentService.servicePay({ id: this.cycles } as Rule, this.currentMethod, paymentToken, 0, null, null, null);
        },
        (err) => {
          this.appService.snackUp(err?.error?.message);
          this.submitted = false;
        }
      );
    }
  }

  payDisabled(): boolean {
    // tslint:disable-next-line:max-line-length
    return (
      !(!this.loading && this.email && this.password && this.password.length > 7 && !this.emailError && !this.passwordError) ||
      (this.currentMethod === "creditcard" && !this.card)
    );
  }

  openSelectPayMethod = () => {
    this.bottomSheet.open(MobilePaymethodComponent, { data: this.payMethodList });
  };
}
