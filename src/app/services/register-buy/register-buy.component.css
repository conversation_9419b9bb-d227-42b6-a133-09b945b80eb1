.register-buy{
  margin: 50px 100px 0px 100px;
  height: 100%;
  width: 100%;
}
.box{
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.box-item{
  display: flex;
  height: 300px;
}
.title{
  display: flex;
  align-items: center;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 14px;
  color: #000000;
  margin-bottom: 40px;
}
.number{
  background-color: #FF5E5E;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 14px;
  color: #FFFFFF;
  margin-right: 10px;
  width: 20px;
  height: 20px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}
@media screen and (max-width: 599px) {
  .register-buy{
    margin: 0;
  }
  .box-item{
    flex-direction: column;
    height: auto;
  }
}
/* order */
.order{
  width: 40%;
  height: 30%;
}
.order-item{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-evenly;
  padding: 20px;
  gap: 24px;
  width: 40%;
  height: 120px;
  background: #FFFFFF;
  box-shadow: 0px 4px 10px rgba(255, 94, 94, 0.2);
  border-radius: 2px;
}
.order-item-mobile{
  display: none;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-evenly;
  padding: 20px;
  gap: 24px;
  width: auto;
  height: 80px;
  background: #FFFFFF;
  box-shadow: 0px 4px 10px rgba(255, 94, 94, 0.2);
  border-radius: 2px;
}
.order-title-1{
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 14px;
  color: #FF5E5E;
}
.order-title-2{
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  color: #000000;
}
.box-Offer-1{
  background: rgba(255, 94, 94, 0.1);
  border-radius: 2px;
  padding: 5px 10px;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 14px;
  color: #FF5E5E;
}
.box-Offer-2{
  background: #F2F2F2;
  border-radius: 2px;
  padding: 5px 10px;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 14px;
  color: #808080;
}
.order-save{
  display: none;
  align-items: center;
}
@media screen and (max-width: 599px) {
  .order{
    width: 100%;
    height: auto;
    margin-top: 30px;
  }
  .order-item{
    display: none;
  }
  .order-save{
    display: flex;
  }
  .order-Offer-1{
    margin-left: 20px;
  }
  .order-Offer-2{
    margin-left: 20px;
  }
  .order-item-mobile{
    display: flex;
  }
}
/* register */
.register{
  width: 60%;
  height: 30%;
}
.form-box{
  /*display: flex;*/
  /*flex-direction: column;*/
}
:host ::ng-deep .mat-form-field-underline {
  display: none!important;
}
.form-input {
  width: 420px;
  height: 56px;
}
@media screen and (max-width: 599px){
  .form-input {
    width: 100%;
  }
}
:host ::ng-deep .mat-form-field-wrapper {
  padding-bottom: 0;
  height: 100%;
}
:host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
  padding: 0 12px;
  height: 100%;
  align-items: center;
}
:host ::ng-deep .mat-form-field-appearance-fill .mat-form-field-infix {
  padding: 0;
  margin: 0;
  border: 0;
  display: flex;
  align-items: center;
}
.email-list{
  position: absolute;
  width: 420px;
  font-size: 14px;
  background-color: #ffff;
  z-index: 9;
  border-radius: 8px;
  background: #ffff;
  box-shadow:  11px 11px 22px #9b9b9b,
  -11px -11px 22px #ffffff;
  margin-top: -15px;
}
@media screen and (max-width: 599px){
  .email-list {
    width: 340px;
  }
}
.input_tips{
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 10px;
  gap: 9px;
  width: 420px;
  height: 38px;
  background: rgba(58, 144, 246, 0.2);
  border: 1px solid #3A73F6;
  border-radius: 2px;
  flex: none;
  order: 0;
  flex-grow: 0;
  margin-bottom: 10px;
  color: #3A73F6;
}
@media screen and (max-width: 599px){
  .input_tips {
    width: 100%;
  }
}
.input_tips .mat-icon{
  width: 18px;
  height: 18px;
}
.input_tips .material-icons{
  font-size: 18px;
}
.email-list{
  position: absolute;
  width: 420px;
  font-size: 14px;
  z-index: 99 !important;
  border-radius: 8px;
  background: #FFFFFF;
  box-shadow:  11px 11px 22px #9b9b9b,
  -11px -11px 22px #ffffff;
  margin-top: -15px;
}
@media screen and (max-width: 599px){
  .email-list {
    width: 340px;
  }
}
.email-list .mat-list-item{
  height: 32px;
  font-size: 14px;
}
.fade-in-fwd {
  -webkit-animation: fade-in-fwd 0.3s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
  animation: fade-in-fwd 0.3s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
}
@-webkit-keyframes fade-in-fwd {
  0% {
    -webkit-transform: translateZ(-80px);
    transform: translateZ(-80px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    opacity: 1;
  }
}
@keyframes fade-in-fwd {
  0% {
    -webkit-transform: translateZ(-80px);
    transform: translateZ(-80px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    opacity: 1;
  }
}
.fade-out-bck {
  -webkit-animation: fade-out-bck 0.3s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
  animation: fade-out-bck 0.3s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}
@-webkit-keyframes fade-out-bck {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    opacity: 1;
  }
  100% {
    -webkit-transform: translateZ(-80px);
    transform: translateZ(-80px);
    opacity: 0;
  }
}
@keyframes fade-out-bck {
  0% {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    opacity: 1;
  }
  100% {
    -webkit-transform: translateZ(-80px);
    transform: translateZ(-80px);
    opacity: 0;
  }
}
.slide-bottom {
  -webkit-animation: slide-bottom 0.6s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
  animation: slide-bottom 0.6s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}
@-webkit-keyframes slide-bottom {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  100% {
    -webkit-transform: translateY(18px);
    transform: translateY(18px);
  }
}
@keyframes slide-bottom {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  100% {
    -webkit-transform: translateY(18px);
    transform: translateY(18px);
  }
}
.slide-top {
  -webkit-animation: slide-top 0.6s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
  animation: slide-top 0.6s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}
@-webkit-keyframes slide-top {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  100% {
    -webkit-transform: translateY(-48px);
    transform: translateY(-48px);
  }
}
@keyframes slide-top {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  100% {
    -webkit-transform: translateY(-48px);
    transform: translateY(-48px);
  }
}
.none_tips{
  display: none;
}
.field-gap{
  margin-bottom: 50px;
}
@media screen and (max-width: 599px) {
  .register{
    width: 100%;
    height: auto;
    margin-top: 50px;
  }
  .field-gap{
    margin-bottom: 30px;
  }
}
/* buy */
.buy{
  width: 100%;
  margin-top: 40px;
}
.methods-radio-group {
  display: flex;
  flex-direction: column;
  margin: 20px 0;
}
.methods-radio-button {
  margin: 10px;
}
.methods-radio-option{
  display: flex;
  align-items: center;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 16px;
  color: #000000;
}
.payment-box{
  margin-top: 40px;
}
.payment-btn{
  width: 200px;
  height: 45px
}
.card-box{
  width: 30%;
  padding: 15px;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
}
.buy-box{
  display: block;
}
.buy-box-mobile{
  display: none;
}
.select-pay-method{
  display: flex;
  align-items: center;
  padding: 8px 14px;
  gap: 8px;
  height: 67px;
  background: #FFFFFF;
  position: relative;
  margin-top: 20px;
  width: auto;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
}
.select-pay-method-box{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.select-pay-method-title{
  font-family: 'Libre Franklin';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #808080;
}
.select-pay-method-info{
  ont-family: 'Libre Franklin';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #000000;
  margin-top: 10px;
  display: flex;
  align-items: center;
}
.select-pay-method-arrow{
  position: absolute;
  right: 20px;
}
@media screen and (max-width: 599px) {
  .buy{
    margin-top: 50px;
  }
  .buy-box{
    display: none;
  }
  .buy-box-mobile{
    display: block;
  }
  .payment-box{
    width: 100%;
    margin-bottom: 40px;
    text-align: center;
  }
  .card-box{
    width: auto;
    margin-top: 30px;
  }
  .payment-btn{
    width: 100%;
  }
}
