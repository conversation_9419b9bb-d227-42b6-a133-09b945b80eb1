import { Component, OnInit, ViewChild } from "@angular/core";
import { AppService } from "src/app/app.service";
import { ActivatedRoute, Router } from "@angular/router";
import {
  APIManager,
  DeductionType,
  defaultBillingCycles,
  DynamicDialogService,
  OauthType,
  PaymentMethodService,
  PayType,
  Rule,
  ServiceService,
  Signup,
  User,
} from "@flashvpn-io/web-core";
import { DialogsService } from "../../utils/dialogs/dialogs.service";
import { Product } from "@flashvpn-io/web-core";
import { filter, tap } from "rxjs/operators";
import { UsersService } from "@flashvpn-io/web-core";
import { BillingCyclesComponent } from "../../component/billing-cycles.component";
import { BuyProductsComponent } from "../../component/buy-products.component";
import { OauthFormComponent } from "../../component/oauth-form.component";
import { MatSnackBar } from "@angular/material/snack-bar";
import { TipsComponent } from "../../component/tips.component";
import { AlreadyRegisteredComponent } from "../../component/already-registered.component";
import { combineLatest } from "rxjs";
import { Utils } from "src/app/utils/utils";
import { IN_PAYMENT } from "../payment.service";

@Component({
  selector: "app-welcome",
  templateUrl: "./welcome.component.html",
  styleUrls: ["./welcome.component.css"],
})
export class WelcomeComponent implements OnInit {
  protected readonly PayType = PayType;

  protected readonly OauthType = OauthType;

  public submitted = false;

  @ViewChild("billingCyclesComponent") billingCyclesComponent: BillingCyclesComponent;

  @ViewChild("buyProductsComponent") buyProductsComponent: BuyProductsComponent;

  @ViewChild("oauthFormComponent") oauthFormComponent: OauthFormComponent;

  constructor(
    private snackBar: MatSnackBar,
    public appService: AppService,
    private apiManager: APIManager,
    public router: Router,
    public route: ActivatedRoute,
    private paymentMethodService: PaymentMethodService,
    public usersService: UsersService,
    public serviceService: ServiceService,
    private customOverlayService: DynamicDialogService
  ) { }

  async ngOnInit() {
    if (await this.usersService.isLoggedIn()) {
      // await this.router.navigate(["services", "dashboard"]);
      await this.router.navigate(["center"]);
      return;
    }
  }

  async emailcheck() {
    Utils.openPayWindow();
    const email = this.oauthFormComponent.email;
    const password = this.oauthFormComponent.password;
    const emailError = this.oauthFormComponent.emailError;
    const passwordError = this.oauthFormComponent.passwordError;
    if (email && password && !emailError && !passwordError) {
      this.submitted = true;
      this.apiManager.emailcheck(email).subscribe(
        (isOk) => {
          if (!isOk) {
            this.onSubmit();
          } else {
            Utils.closePayWindow()
            this.submitted = false;
            const tips = this.customOverlayService.open(AlreadyRegisteredComponent);
            tips.onOk.subscribe(() => {
              this.customOverlayService.close();
              this.router.navigate(["users", "signin"]);
            });
          }
        },
        (res) => {
          Utils.closePayWindow()
          this.submitted = false;
          this.snackBar.open(this.appService.translate("EmailcheckError"), "Okay", {
            duration: 2000,
            verticalPosition: "top",
          });
        }
      );
    }
  }

  async onSubmit() {
    const email = this.oauthFormComponent.email;
    const password = this.oauthFormComponent.password;
    const emailError = this.oauthFormComponent.emailError;
    const passwordError = this.oauthFormComponent.passwordError;
    const currentMethod = this.paymentMethodService.currentMethod$.value;
    if (email && password && !emailError && !passwordError) {
      try {
        this.submitted = true;
        if (currentMethod.payType === "creditcard") {
          this.submitted = false;
          return;
        }
        const utmSource = this.route.snapshot.queryParams.utm_source || localStorage.getItem("utmSource") || undefined;
        const utmMedium = this.route.snapshot.queryParams.utm_medium || localStorage.getItem("utmMedium") || undefined;
        const utmCampaign = this.route.snapshot.queryParams.utm_campaign || localStorage.getItem("utmCampaign") || undefined;
        const utmTerm = this.route.snapshot.queryParams.utm_term || localStorage.getItem("utmTerm") || undefined;
        const utmContent = this.route.snapshot.queryParams.utm_content || localStorage.getItem("utmContent") || undefined;
        const registerInfo: Signup = {
          email: email,
          password: password,
          inviterCode: undefined,
          utmSource,
          utmMedium,
          utmCampaign,
          utmTerm,
          utmContent,
          sendEmail: false,
        };
        this.apiManager.createUser(registerInfo).subscribe(
          async (user) => {
            console.log(user, "user");
            !this.appService.isExtension() && this.setChatwootDefaultUserInfo(user as User);
            await this.usersService.loginByUser(user as User);
            const services = await this.apiManager.fetchServices().toPromise();
            const service = services[0];
            const currentMethod = this.paymentMethodService.currentMethod$.value;
            const currentProduct = this.buyProductsComponent.currentProduct$.value;
            const currentBillingCycle = this.billingCyclesComponent.currentBillingCycle$.value;
            if (service?.productId !== currentProduct?.id) {
              await this.apiManager
                .upgradeService(service?.id, {
                  productId: currentProduct?.id,
                  billingCycle: currentBillingCycle?.id,
                })
                .toPromise();
            } else if (service?.billingcycle !== currentBillingCycle?.id) {
              await this.apiManager.updateBillingCycle(service?.id, { billingCycle: currentBillingCycle?.id }).toPromise();
            }
            const invoice = await this.apiManager.fetchLatestInvoice(service).toPromise();
            const data = await this.apiManager.payInvoice(invoice, currentMethod.payType, "").toPromise();
            if (data.redirect_url !== undefined) {
              if (this.appService.isElectron() || this.appService.isExtension()) {
                window.open(data.redirect_url);
                Utils.closePayWindow();
              } else {
                // window.location = data.redirect_url;
                Utils.sendPayUrl(data.redirect_url);
                await this.router.navigate(["payment-status"], { queryParams: { status: IN_PAYMENT, id: invoice.id } });
              }
            }
            this.submitted = false;
          },
          (err) => {
            Utils.closePayWindow()
            this.appService.snackUp(err?.error?.message);
            this.submitted = false;
          }
        );
      } catch (e) {
        Utils.closePayWindow()
        this.appService.snackUp(e.error.message);
        this.submitted = false;
      }
    }
  }

  setChatwootDefaultUserInfo(user: User) {
    // @ts-ignore
    window.$chatwoot.setUser(user.id, {
      name: user.email, // Name of the user
      email: user.email, // Email of the user
      country_code: user.locale, // Two letters country code
    });
  }
}
