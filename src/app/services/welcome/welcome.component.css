.welcome-box {
  margin: 0 100px;
  height: 100%;
  width: 100%;
}
@media screen and (max-width: 599px) {
  .welcome-box {
    margin: 0;
  }
}
.title {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #000000;
  flex: none;
  order: 0;
  flex-grow: 0;
}
/* head */
.head {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.head-left {
  display: flex;
  align-items: center;
}
.head-left-title {
  margin-left: 30px;
}
@media screen and (max-width: 599px) {
  .head {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
  }
  .head-left {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
  }
  .head-right {
    width: auto;
    margin-top: 30px;
  }
  .head-left-title {
    margin-left: 20px;
  }
}
.head-title-1 {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 40px;
  color: #ff5e5e;
}
.head-title-2 {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 550;
  font-size: 14px;
  line-height: 30px;
  color: #000000;
}
.head-title-3 {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 20px;
  text-align: center;
  color: #000000;
  margin-top: 20px;
}
.head-title-3-mobile {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  color: #000000;
  margin-left: 10px;
}
@media screen and (max-width: 599px) {
  .head-right {
    display: none;
  }
  .head-right-mobile {
    display: flex;
  }
  .operation-btn {
    width: 100px;
  }
}
.item-box {
  margin-top: 50px;
}
.mat-btn {
  height: 45px;
  width: 150px;
}
@media screen and (max-width: 599px) {
  .mat-btn {
    width: 100%;
  }
}
