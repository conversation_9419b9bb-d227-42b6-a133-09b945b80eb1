<div class="welcome-box" style="margin: 0">
  <div class="head">
    <div class="head-left">
      <svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="56" height="56" rx="10" fill="#FF5E5E" />
        <path d="M13.3325 28.3567L28.9834 10.2017V28.3567H13.3325Z" fill="white" />
        <path d="M39 23.9745L23.349 42.1295V23.9745H39Z" fill="white" />
      </svg>
      <div class="head-left-title">
        <div class="head-title-1">Flash｜您唯一需要的加速器</div>
        <div class="head-title-2">极速体验 · 安全保障 · 多平台共享</div>
      </div>
    </div>
  </div>
  <div class="item-box">
    <app-billing-cycles #billingCyclesComponent [currentProduct]="buyProductsComponent.currentProduct$ | async"></app-billing-cycles>
  </div>
  <div class="item-box">
    <app-buy-products #buyProductsComponent [currentBillingCycle]="billingCyclesComponent.currentBillingCycle$ | async"></app-buy-products>
  </div>
  <div class="item-box">
    <app-digital-title [num]="3" title="选择支付方式"></app-digital-title>
    <div style="margin-top: 30px">
      <app-payment-methods [showTitle]="false" [excludePayType]="[PayType.Balance, PayType.CreditCard]" ></app-payment-methods>
    </div>
  </div>
  <div class="item-box">
    <app-digital-title [num]="4" title="填写邮箱及设置密码"></app-digital-title>
    <div style="margin-top: 30px">
      <app-oauth-form #oauthFormComponent [oAuthType]="OauthType.SIGNUP" (onSubmit)="emailcheck()"></app-oauth-form>
    </div>
  </div>
  <div class="item-box">
    <button
      *ngIf="!submitted"
      class="mat-btn"
      [disabled]="oauthFormComponent.isDisabled$ | async"
      mat-raised-button
      color="warn"
      id="welcome-submit"
      (click)="emailcheck()">
      确认下单
    </button>
    <app-load-botton *ngIf="submitted"></app-load-botton>
  </div>
</div>
