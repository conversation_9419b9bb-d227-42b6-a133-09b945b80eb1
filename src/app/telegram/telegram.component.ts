import { Component, OnInit } from "@angular/core";
import { AppService } from "../app.service";
import { APIManager } from "@flashvpn-io/web-core";
import { MatSnackBar } from "@angular/material/snack-bar";
import { UsersService } from "@flashvpn-io/web-core";
import { Router } from "@angular/router";
import { environment } from "../../environments/environment";

@Component({
  selector: "app-telegram",
  templateUrl: "./telegram.component.html",
  styleUrls: ["./telegram.component.scss"],
})
export class TelegramComponent implements OnInit {
  tgCmd = "";
  isLoggedIn = false;
  tgBot = environment.tgBot;

  constructor(
    private router: Router,
    private snackBar: MatSnackBar,
    private appService: AppService,
    private apiManager: APIManager,
    private usersService: UsersService
  ) {}

  async ngOnInit(): Promise<void> {
    this.isLoggedIn = await this.usersService.isLoggedIn();
  }

  goToSignIn = () => {
    this.router.navigate(["users", "signin"], { queryParams: { url: this.router.url } });
  };

  getBindTgCode() {
    this.apiManager.getTgBindCode().subscribe(
      (res) => {
        const tgCmd = `/login/${this.usersService.user.email}/${res.code}`;
        this.tgCmd = tgCmd;
      },
      (err) => {
        this.snackBar.open(err.error.message, "Okay", {
          duration: 2000,
          verticalPosition: "top",
        });
      }
    );
  }

  copy() {
    this.appService.copyMessage(this.tgCmd);
  }

  gotoBot() {}
}
