<div class="container" fxFill>
  <div class="title" i18n>Bind telegram for the first time to get 5 HKD balance</div>
  <div class="content">
    <div class="desc" i18n>To celebrate the launch of the TG robot, users who bind the TG robot for the first time will be given 5 Hong Kong dollars to the account, which can be used for renewal bills.</div>
    <div class="desc" i18n>In the tg robot, you can check traffic, renew services, and obtain subscription addresses.</div>
    <div class="desc" i18n>1. Get bind command</div>
    <div class="desc" *ngIf="!tgCmd">
      <button *ngIf="isLoggedIn" id="getBindTgCmd" mat-raised-button color="warn" (click)="getBindTgCode()" i18n>Get bind robot command</button>
      <button *ngIf="!isLoggedIn" id="goToSignPage" mat-raised-button color="warn" (click)="goToSignIn()" i18n>log in</button>
    </div>
    <div class="desc" *ngIf="tgCmd">
      <code class="strong">{{tgCmd}}</code>
    </div>
    <div class="desc" i18n>2. Copy bind command</div>
    <div class="desc"  *ngIf="tgCmd">
      <button id="tg-page-copy-button" mat-raised-button color="warn" (click)="copy()" i18n>Copy the Command</button>
    </div>
    <div class="desc" i18n>3. Go to the robot and enter the bind command</div>
    <div class="desc"><a href="{{tgBot}}" target="_blank">{{tgBot}}</a></div>
  </div>
</div>
