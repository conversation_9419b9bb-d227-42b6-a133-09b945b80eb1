<div fxFill id="trial-container">
  <div class="content-container">
    <div class="trial-features">
      <div class="title">
        仅支付
        <span class="one-yuan">{{ trialPrice }}元</span>
        ，即可体验{{ trialPeriod }}天：
      </div>
      <div class="features-container">
        <div class="feature-item">
          <svg class="feature-icon" width="21" height="27" viewBox="0 0 21 27" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd"
              d="M5.61894 0.718767C5.83642 0.304204 6.26639 0.0444336 6.7351 0.0444336H16.3178C16.7345 0.0444336 17.1242 0.250146 17.3589 0.593953C17.5936 0.93776 17.6429 1.37519 17.4905 1.76245L14.0605 10.4813H19.7399C20.2803 10.4813 20.7604 10.8253 20.9335 11.3364C21.1066 11.8474 20.9341 12.4118 20.5047 12.7393L3.39392 25.7862C2.95748 26.119 2.35544 26.1312 1.9058 25.8164C1.45617 25.5016 1.26252 24.9323 1.42714 24.4092L5.01845 12.9975H1.26C0.819082 12.9975 0.410243 12.7673 0.181985 12.3907C-0.0462724 12.014 -0.0607384 11.5457 0.143842 11.1557L5.61894 0.718767ZM7.4975 2.56056L3.34234 10.4813H6.7351C7.13627 10.4813 7.51347 10.6721 7.75085 10.995C7.98822 11.3179 8.05741 11.7344 7.93715 12.1165L5.02235 21.3786L16.014 12.9975H12.2119C11.7952 12.9975 11.4054 12.7918 11.1707 12.4479C10.936 12.1041 10.8868 11.6667 11.0391 11.2794L14.4691 2.56056H7.4975Z"
              fill="white" />
          </svg>
          <span class="text">超快网速</span>
        </div>
        <div class="feature-item">
          <svg class="feature-icon" width="26" height="27" viewBox="0 0 26 27" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd"
              d="M11.5054 7.08754C12.3075 5.90796 13.2381 4.82098 14.28 3.84657C14.6271 3.56792 15.2102 3.29935 16.0421 3.08229C16.8739 2.86523 17.8553 2.72466 18.8903 2.64487C20.4846 2.52195 22.131 2.54869 23.416 2.62845C23.4957 3.91343 23.5225 5.55989 23.3996 7.1544C23.3198 8.18948 23.1792 9.17106 22.9621 10.0032C22.7451 10.8351 22.4765 11.4187 22.1977 11.7663C21.2268 12.8044 20.1442 13.732 18.9696 14.532C18.9179 14.5596 18.8678 14.5909 18.8197 14.626C18.3335 14.9805 17.8263 15.3332 17.3066 15.6759L17.3015 15.6793C16.007 16.5418 14.7109 17.3377 13.6844 17.9377L10.9 15.1465L8.10668 12.3599C8.70663 11.3335 9.5025 10.0375 10.365 8.74308L10.3684 8.738C10.711 8.21827 11.0638 7.71112 11.4183 7.22493C11.4505 7.18069 11.4796 7.13478 11.5054 7.08754ZM5.72751 13.5406C5.69263 13.5115 5.65917 13.4804 5.62729 13.4473L4.0447 11.804C2.60552 10.2839 2.60555 7.90389 4.04473 6.38373L4.04772 6.38057C4.74796 5.64602 5.71756 5.22892 6.7324 5.22566L6.73644 5.22566H9.73695C10.579 4.05161 11.536 2.96373 12.5941 1.9783C12.6114 1.96218 12.6291 1.94655 12.6473 1.93143C13.4014 1.30464 14.4017 0.909874 15.4068 0.647627C16.4316 0.380211 17.5723 0.222829 18.6968 0.136133C20.9451 -0.0371972 23.2552 0.062906 24.7095 0.207805C25.3057 0.267208 25.7772 0.738783 25.8366 1.33496C25.9815 2.78919 26.0816 5.09932 25.9083 7.34779C25.8216 8.47243 25.6642 9.61325 25.3968 10.6383C25.1346 11.6436 24.7399 12.644 24.1133 13.3984C24.0981 13.4167 24.0824 13.4346 24.0661 13.4521C23.0807 14.5101 21.9929 15.467 20.8189 16.3091V19.3118C20.8157 20.3267 20.3986 21.2963 19.664 21.9965L19.6609 21.9995C18.1407 23.4387 15.7608 23.4387 14.2406 21.9995L14.2328 21.9921L12.5973 20.417C12.5649 20.3857 12.5343 20.353 12.5058 20.3189L9.12083 16.9258L9.11868 16.9236L5.72751 13.5406ZM21.1549 6.66874C21.6462 6.17742 21.6462 5.38085 21.1549 4.88953C20.6636 4.39822 19.867 4.39822 19.3757 4.88953L17.3007 6.96454C16.8094 7.45585 16.8094 8.25243 17.3007 8.74375C17.792 9.23506 18.5886 9.23506 19.0799 8.74375L21.1549 6.66874ZM6.0379 15.9465C6.5291 15.4551 6.52893 14.6585 6.03751 14.1673C5.54609 13.6761 4.74951 13.6763 4.25831 14.1677L0.368294 18.0594C-0.122914 18.5508 -0.122743 19.3474 0.368678 19.8386C0.860098 20.3298 1.65668 20.3296 2.14788 19.8382L6.0379 15.9465ZM8.95518 18.8668C9.44628 18.3753 9.44594 17.5787 8.95441 17.0876C8.46288 16.5965 7.66631 16.5968 7.1752 17.0883L5.23271 19.0325C4.74161 19.524 4.74196 20.3206 5.23348 20.8117C5.72501 21.3028 6.52159 21.3025 7.01269 20.811L8.95518 18.8668ZM11.8767 21.7861C12.3681 21.2949 12.3683 20.4983 11.8771 20.0069C11.3859 19.5155 10.5893 19.5153 10.0979 20.0065L6.20621 23.8965C5.71479 24.3878 5.71462 25.1843 6.20583 25.6758C6.69704 26.1672 7.49361 26.1673 7.98503 25.6761L11.8767 21.7861ZM6.73881 7.74183C6.41068 7.74334 6.09724 7.87811 5.87042 8.11521C5.35243 8.66402 5.3519 9.52137 5.86883 10.0708L6.28283 10.5007C6.78293 9.66369 7.37517 8.70965 8.01051 7.74183H6.73881ZM18.3028 18.0337C17.3349 18.6691 16.3809 19.2613 15.5439 19.7614L15.9738 20.1754C16.5232 20.6923 17.3805 20.6918 17.9294 20.1738C18.1665 19.947 18.3013 19.6336 18.3028 19.3054V18.0337Z"
              fill="white" />
          </svg>
          <span class="text">自由连接</span>
        </div>
        <div class="feature-item">
          <svg class="feature-icon" width="25" height="27" viewBox="0 0 25 27" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd"
              d="M9.8289 3.31613C8.34914 3.92496 6.20816 4.95074 3.28419 6.60191L3.25396 6.61844C2.81436 6.85104 2.53323 7.30096 2.5163 7.79731C2.54594 18.2475 8.77292 22.5329 12.1118 23.5227L12.1131 23.523C12.1362 23.5299 12.1608 23.5299 12.1839 23.523L12.1848 23.5228C15.5822 22.5146 22.0042 18.0935 21.8078 7.23683C21.804 7.1083 21.7331 6.99108 21.621 6.92789L21.6137 6.92376L21.6137 6.92374C18.4954 5.13888 16.2011 4.03245 14.6169 3.3765C13.8241 3.04828 13.2196 2.8372 12.7861 2.71035C12.3652 2.58723 12.175 2.56456 12.1468 2.5612C12.1436 2.56082 12.1425 2.56069 12.1434 2.56069C12.1437 2.56069 12.1432 2.56074 12.1418 2.56088C12.123 2.56279 11.9455 2.58073 11.5386 2.6985C11.133 2.81588 10.5693 3.0115 9.8289 3.31613ZM10.839 0.281441C11.3234 0.141256 11.7775 0.0444336 12.1434 0.0444336C12.5138 0.0444336 12.9858 0.147056 13.4926 0.29533C14.0346 0.453912 14.7268 0.69859 15.5795 1.05165C17.2855 1.75802 19.6763 2.91568 22.8605 4.7381C23.7435 5.23761 24.2992 6.16451 24.3234 7.17895L24.3236 7.18608C24.5446 19.2909 17.2696 24.6385 12.9006 25.935L12.5427 24.7289L12.9015 25.9348C12.9012 25.9349 12.9009 25.935 12.9006 25.935C12.4095 26.081 11.8865 26.0809 11.3955 25.9348C11.3959 25.9349 11.3963 25.935 11.3967 25.9351L11.7543 24.7289L11.3955 25.9348C7.09693 24.6599 0.0247782 19.4685 0 7.78375L0.000360553 7.75084C0.0341585 6.34494 0.821505 5.06641 2.06051 4.40319C2.05598 4.40574 2.05144 4.4083 2.04691 4.41086L2.66555 5.50639L2.07714 4.39433C2.07159 4.39727 2.06604 4.40022 2.06051 4.40319C5.04125 2.72053 7.27336 1.64667 8.87149 0.989139C9.67122 0.660099 10.3234 0.430681 10.839 0.281441Z"
              fill="white" />
            <path fill-rule="evenodd" clip-rule="evenodd"
              d="M18.0831 8.81133C18.5749 9.30217 18.5757 10.0988 18.0849 10.5906L11.3749 17.314C11.139 17.5504 10.8189 17.6832 10.485 17.6834C10.1511 17.6836 9.83082 17.551 9.59472 17.3149L6.23971 13.9599C5.74838 13.4686 5.74838 12.672 6.23971 12.1806C6.73104 11.6893 7.52764 11.6893 8.01897 12.1806L10.4835 14.6451L16.3038 8.81311C16.7947 8.32129 17.5913 8.3205 18.0831 8.81133Z"
              fill="white" />
          </svg>
          <span class="text">安全防护</span>
        </div>
      </div>
    </div>
    <div class="rectangle-parent">
      <img class="vector-icon" alt="" src="assets/images/trail-tips.svg">
      <div class="flash">本活动仅Flash新客户可用</div>
    </div>
    <div *ngIf="(usersService.user$ | async) === null" class="email-form">
      <div class="title">邮箱</div>
      <div class="tips">请输入邮箱，Flash将自动为您创建账号</div>
      <app-oauth-form #oauthFormComponent [oAuthType]="OauthType.SIGNUP" (onSubmit)="buy()"></app-oauth-form>
    </div>
    <div class="payment-methods">
      <app-payment-methods [excludePayType]="[this.payType.Balance, this.payType.CreditCard]"></app-payment-methods>
    </div>
  </div>
  <app-payment-details style="display: grid;" #paymentDetailsComponent [buyType]="BuyType.TRIAL"
    [currentBillingCycle]="{ description: trialPrice + '元试用体验', title: '', unit: '', id: '' }" [deductionAmount]="0"
    [origialAmount]="trialPrice" [deDutiontoString]="" [amountAfterDeduction]="trialPrice" [submitted]="loading"
    (buy)="buy()"></app-payment-details>
</div>