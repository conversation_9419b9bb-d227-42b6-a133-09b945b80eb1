@import "../app.component.css";

#trial-container {
  position: relative;
  flex-direction: column;
  padding-bottom: 40px;

  .content-container {
    margin-bottom: 250px;
  }

  .rectangle-parent {
    display: flex;
    align-items: center;
    margin-top: 24px;
    background-color: rgba(246, 228, 228, 1);
    width: 343px;
    height: 36px;
    padding-left: 10px;

    .vector-icon {
      margin-right: 10px;
    }

    .flash {
      color: #FF5E5E;
      font-family: <PERSON>bre Franklin;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
    }
  }

  .email-form {
    margin-top: 32px;

    .title {
      color: #000;
      font-family: PingFang SC;
      font-size: 20px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      /* 80% */
    }

    .tips {
      margin-top: 24px;
      margin-bottom: 24px;
    }
  }

  .trial-features {
    .title {
      color: #000;
      font-family: <PERSON><PERSON> Franklin;
      font-size: 20px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    .one-yuan {
      color: #FF5E5E;
      font-family: <PERSON><PERSON>;
      font-size: 20px;
      font-style: normal;
      font-weight: 900;
      line-height: normal;
    }

    .features-container {
      display: flex;
      flex-direction: row;

      .feature-item {
        width: 107px;
        height: 107px;
        border-radius: 4px;
        background: #FF5E5E;
        display: flex;
        align-items: center;
        flex-direction: column;
        margin-right: 11px;
        margin-top: 24px;

        .feature-icon {
          margin-top: 34px;
        }

        .text {
          margin-top: 12px;
          color: #FFF;
          font-family: Libre Franklin;
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }
    }

    .tips {
      display: flex;
      align-items: center;
      margin-top: 24px;
      width: 343px;
      height: 36px;
      flex-shrink: 0;
      border-radius: 2px;
      background: rgba(255, 94, 94, 0.10);

      .tips-icon {
        margin-left: 10.5px;
      }

      .text {
        margin-left: 10.5px;
        color: #FF5E5E;
        font-family: Libre Franklin;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 16px;
        /* 100% */
      }
    }
  }

  .payment-methods {
    width: 100%;
    margin-top: 0;
  }

}

@media screen and (max-width: 599px) {
  #trial-container {
    .payment-methods {
      margin-top: 20px;
    }

    .content-container {
      margin-bottom: 10px;
    }

    .rectangle-parent {
      width: auto
    }
  }
}