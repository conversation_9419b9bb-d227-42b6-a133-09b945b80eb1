import { Component, OnInit, ViewChild } from "@angular/core";
import { ToPayComponent } from "../services/to-pay/to-pay.component";
import { LoadBottonComponent } from "../utils/load-botton/load-botton.component";
import { AppService } from "../app.service";
import { emailList } from "../../constants/email-list.constants";
import { MatBottomSheet } from "@angular/material/bottom-sheet";
import { MobilePaymethodComponent } from "../component/mobile-paymethod.component";
import { APIManager, OauthType, PaymentMethodService, PayType, ServiceService, USER_EMAIL_VERIFIED } from "@flashvpn-io/web-core";
import { ActivatedRoute, Router } from "@angular/router";
import { AFTER_USER_LOGIN, AFTER_USER_LOGOUT, NotificationService } from "@flashvpn-io/web-core";
import { UsersService } from "@flashvpn-io/web-core";
import { IN_PAYMENT, MOBLIE_PAYMETHOD, PaymentService } from "../services/payment.service";
import { MatSnackBar } from "@angular/material/snack-bar";
import { BuyType } from "@flashvpn-io/web-core";
import { User } from "@flashvpn-io/web-core";
import { Signup } from "@flashvpn-io/web-core";
import { OauthFormComponent } from "../component/oauth-form.component";
import { DialogsService } from "../utils/dialogs/dialogs.service";
import { Utils } from "../utils/utils";

@Component({
  selector: "app-trial",
  templateUrl: "./trial.component.html",
  styleUrls: ["./trial.component.less"],
})
export class TrialComponent implements OnInit {
  @ViewChild("toPayComponent") toPayComponent: ToPayComponent;
  @ViewChild("oauthFormComponent") oauthFormComponent: OauthFormComponent;
  @ViewChild("loadBottonComponent") loadBottonComponent: LoadBottonComponent;

  protected readonly OauthType = OauthType;

  protected readonly payType = PayType;

  protected readonly BuyType = BuyType;

  public loading = false;
  public buyType = BuyType.SERVICE;
  public trialPrice = 4;
  public trialTraffic = 5;
  public trialPeriod = 3;
  public emailVerifying = false;

  constructor(
    private appService: AppService,
    private bottomSheet: MatBottomSheet,
    private apiManager: APIManager,
    public route: ActivatedRoute,
    private notification: NotificationService,
    public usersService: UsersService,
    public router: Router,
    private snackBar: MatSnackBar,
    private dialogsService: DialogsService,
    private paymentMethodService: PaymentMethodService,
    private serviceService: ServiceService
  ) { }

  async ngOnInit(): Promise<void> {
    this.serviceService.getTrialProductInfo().then(() => {
      this.trialPrice = +localStorage.getItem("trialPrice");
      this.trialTraffic = +localStorage.getItem("trialTraffic");
      this.trialPeriod = +localStorage.getItem("trialPeriod");
    });

    this.notification.register(USER_EMAIL_VERIFIED).subscribe(async () => {
      if (this.emailVerifying) {
        this.emailVerifying = false;
        await this.createTrialOrder();
      }
    });
  }

  async buy() {
    this.loading = true;
    try {
      if (!this.usersService?.user$?.value) {
        const utmSource = this.route.snapshot.queryParams.utm_source || localStorage.getItem("utmSource") || undefined;
        const utmMedium = this.route.snapshot.queryParams.utm_medium || localStorage.getItem("utmMedium") || undefined;
        const utmCampaign = this.route.snapshot.queryParams.utm_campaign || localStorage.getItem("utmCampaign") || undefined;
        const utmTerm = this.route.snapshot.queryParams.utm_term || localStorage.getItem("utmTerm") || undefined;
        const utmContent = this.route.snapshot.queryParams.utm_content || localStorage.getItem("utmContent") || undefined;
        const registerInfo: Signup = {
          email: this.oauthFormComponent.email,
          password: this.oauthFormComponent.password,
          inviterCode: undefined,
          utmSource,
          utmMedium,
          utmCampaign,
          utmTerm,
          utmContent,
          sendEmail: false,
          isTrial: true,
        };
        try {
          const createUserResult = await this.apiManager.createUser(registerInfo).toPromise();
          await this.usersService.loginByUser(createUserResult as User);
        } catch (e) {
          if (e.error.error == "EmailExisted") {
            // user's email already verified, can sign in now
            const createUserResult = await this.apiManager.login(registerInfo.email, registerInfo.password).toPromise();
            await this.usersService.loginByUser(createUserResult as User);
          } else {
            this.snackBar.open("邮箱或密码错误", "Okay", {
              duration: 2000,
              verticalPosition: "top",
            });
          }
        }
      } else if (this.usersService.user.emailVerified === 0) {
        this.apiManager.resendEmail().subscribe();
      }

      if (this.usersService.user.emailVerified === 0) {
        this.emailVerifying = true;
        this.dialogsService.openDialog("email-verification-dialog", { email: this.usersService.user.email });
      } else {
        Utils.openPayWindow();
        await this.createTrialOrder();
      }
    } catch (e) {
      if (e.error.error == "CredentialIsWrong") {
        this.snackBar.open(e.error.message, "Okay", {
          duration: 2000,
          verticalPosition: "top",
        });
      } else if (e.error.error == "AlreadyExistTrialOrder") {
        this.dialogsService.openDialog("trial-failure-dialog");
      } else {
        this.snackBar.open(e.error.message, "Okay", {
          duration: 2000,
          verticalPosition: "top",
        });
      }
    } finally {
      this.loading = false;
    }
  }

  async createTrialOrder() {
    this.loading = true;
    try {
      // 先创建订单获取invoice id
      const result = await this.apiManager.createTrialOrder("1", this.paymentMethodService.currentMethod$.value?.payType).toPromise();
      const invoiceId = result["invoiceId"];

      if (this.appService.isElectron() || this.appService.isExtension()) {
        window.open(result["redirect_url"]);
        Utils.closePayWindow();
      } else {
        Utils.sendPayUrl(result["redirect_url"]);
      }

      // 显示等待支付状态
      await this.router.navigate(["payment-status"], { queryParams: { status: IN_PAYMENT, id: invoiceId } });
    } catch (e) {
      Utils.closePayWindow()
      if (e.error.error == "AlreadyExistTrialOrder") {
        this.dialogsService.openDialog("trial-failure-dialog");
      } else {
        this.snackBar.open("出现错误，请稍后尝试", "Okay", {
          duration: 2000,
          verticalPosition: "top",
        });
      }
    }
    this.loading = false;
  }
}
