import { Injectable } from '@angular/core';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';

const icons = ['twitter', 'facebook', 'telegram', 'whatsapp', 'google', 'bars', 'android', 'ios', 'macos', 'windows', 'chrome', 'systemProxy', 'update', 'line', 'chrome-push', 'bind-tg', 'enable-fcm', 'questionnaire', 'edit-rule', 'delete-rule', 'search-rule'];
@Injectable({
  providedIn: 'root'
})
export class IconService {

  constructor(
    private iconRegistry: MatIconRegistry,
    private sanitizer: DomSanitizer,
  ) {

    for (const icon of icons) {
      this.iconRegistry.addSvgIcon(
        icon,
        this.sanitizer.bypassSecurityTrustResourceUrl(`assets/images/${icon}.svg`));

    }
  }
}
