import { Pipe, PipeTransform } from '@angular/core';
import * as Big from 'big.js';
/**
 *  Calculate discount price from promotion code
 *
 *  A promotion code must follow the formation '10-sd345gs'
 */

@Pipe({
  name: 'discount'
})
export class DiscountPipe implements PipeTransform {

  transform(value: Big, promoCode: string): number {
    if (typeof value === 'string') {
      value = new Big(value);
    }
    const parts = promoCode.match(/(\d{1,2})-.+/);
    if (!!parts) {
      if (value.e === 0) {
        return Number(new Big(parts[1]).toFixed(2));
      }
      return Number(value.add(new Big(100).minus(value).times(new Big(parts[1]).div(100))).toFixed(2));
    } else {
      return Number(value.toFixed(2));
    }
  }

}

@Pipe({
  name: 'validatePromoCode'
})
export class ValidatePromoCodePipe implements PipeTransform {

  transform(value: string): boolean {
    if (!!value) {
      const results = value.match(/\d{1,2}-.+/);
      return !!results;
    } else {
      return false;
    }
  }

}
