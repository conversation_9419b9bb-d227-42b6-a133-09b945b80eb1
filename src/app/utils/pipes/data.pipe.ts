import { Pipe, PipeTransform } from '@angular/core';
import * as Big from 'big.js';

@Pipe({
  name: 'data'
})
export class DataPipe implements PipeTransform {

  transform(value: number, ...args: unknown[]): string {
    if (value > 1073741824) {

      return `${new Big(value).div(1073741824).toFixed(2)} GB`;

    } else if (value > 1048576) {
      return `${new Big(value).div(1048576).toFixed(2)} MB`;

    } else if (value > 1024) {
      return `${new Big(value).div(1024).toFixed(2)} KB`;
    } else {

      return `${value} B`;
    }
  }

}
