export class Utils {
    private static paymentWindow: Window | null = null;

    /**
     * 打开支付窗口
     */
    public static openPayWindow(): void {
        if (Utils.paymentWindow && !Utils.paymentWindow.closed) {
            Utils.paymentWindow.close();
        }
        localStorage.removeItem(`invoice-paying`);
        Utils.paymentWindow = window.open('/wait-payment', '_blank');
    }

    /**
     * 关闭支付窗口
     */
    public static closePayWindow(): void {
        if (Utils.paymentWindow && !Utils.paymentWindow.closed) {
            Utils.paymentWindow.close();
            Utils.paymentWindow = null;
        }
        localStorage.removeItem(`invoice-paying`);
    }

    public static sendPayUrl(url: string): void {
        localStorage.setItem(`invoice-paying`, url);
    }

    /**
     * 检查支付窗口是否打开
     * @returns 窗口是否打开
     */
    public static isPayWindowOpen(): boolean {
        return Utils.paymentWindow !== null && !Utils.paymentWindow.closed;
    }
}
