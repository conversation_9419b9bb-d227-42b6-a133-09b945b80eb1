<style>
  /* btn */
  .btn {
    border-radius: 0;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    padding: 8px 20px;
    color: #fff;
    border: none;
    cursor: pointer;
  }

  .btn-red {
    background: #FF5E5E;
    margin-left: 5%;
  }

  .gray {
    background: #C4C4C4;
  }
</style>


<h1 mat-dialog-title i18n>Are you sure to update the billing cycle?</h1>
<div mat-dialog-content>
  <p><span i18n>You will update the billing cycle to </span><span>{{appService.translate(data.billingCycle)}}</span> ?</p>
  <p i18n>When you update the billing cycle, unpaid bills will be cancelled, and the billing cycle will take effect immediately.</p>
</div>
<div mat-dialog-actions>
  <button class="btn gray" (click)="onNoClick()" mat-button i18n>Cancel</button>
  <button class="btn btn-red" mat-button (click)="updateBillingCycle()" i18n>Confirm</button>
</div>
