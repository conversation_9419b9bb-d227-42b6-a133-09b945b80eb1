<style>
  .content{
    width: 100%;
    text-align: center;
    padding-bottom: 30px;
  }
  .title{
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: 500;
    font-size: 24px;
    line-height: 24px;
    color: #000000;
    margin: 30px 0;
  }
  .subTitle{
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 16px;
    color: #333333;
  }
</style>
<div fxLayout="row" fxLayoutAlign="flex-end center">
  <span class="material-icons" (click)="close()" style="cursor: pointer;">
    close
  </span>
</div>
<div class="content">
  <div class="title">
    邮件已发送
  </div>
  <svg width="104" height="104" viewBox="0 0 104 104" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M52 91C73.5391 91 91 73.5391 91 52C91 30.4609 73.5391 13 52 13C30.4609 13 13 30.4609 13 52C13 73.5391 30.4609 91 52 91ZM71.4999 41.3248L66.1728 36.4001L43.0778 57.7507L33.927 49.291L28.5999 54.2157L43.0778 67.6001L71.4999 41.3248Z" fill="#FF5E5E"/>
  </svg>
  <div class="subTitle">
    <p>邮件已发送至指定好友邮箱</p>
  </div>
</div>
