<style>
  .btn-black {
    background: #000;
    color: #fff;
  }

  .title {}

  .title span {
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 17px;
    margin-left: 10px;
  }

  .radio {
    margin-top: 18px;
  }

  .radio span {
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    margin-left: 10px;
    /* identical to box height */


    color: #000000;

  }
  .btn-yellow {
    background: #FFBD2F;
    color: #000000;
  }

</style>
<section fxLayout="column" fxLayoutAlign="center start">
  <div fxLayoutAlign="start center" class="title">
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M19.25 5.5C19.6297 5.5 19.9435 5.78215 19.9932 6.14823L20 6.25V14.75C20 16.483 18.6435 17.8992 16.9344 17.9949L16.75 18H4.061L4.78033 18.7197C5.0466 18.9859 5.0708 19.4026 4.85295 19.6962L4.78033 19.7803C4.51406 20.0466 4.0974 20.0708 3.80379 19.8529L3.71967 19.7803L1.71967 17.7803L1.65627 17.7083C1.65382 17.7051 1.6514 17.7019 1.649 17.6987L1.71967 17.7803C1.68262 17.7433 1.65025 17.7033 1.62257 17.6612C1.60941 17.641 1.59703 17.62 1.58567 17.5984C1.58084 17.5893 1.57648 17.5806 1.5723 17.5718C1.55956 17.545 1.54805 17.5165 1.5383 17.4873C1.53604 17.4803 1.53412 17.4742 1.53227 17.4682C1.51135 17.3997 1.5 17.3262 1.5 17.25C1.5 17.2116 1.50288 17.1739 1.50845 17.137C1.51025 17.1255 1.51231 17.1138 1.51464 17.1021C1.51959 17.0771 1.52576 17.0528 1.53309 17.029C1.53671 17.0174 1.54074 17.0053 1.54509 16.9933C1.55357 16.97 1.56288 16.948 1.57317 16.9265C1.57867 16.9149 1.58497 16.9025 1.59163 16.8903C1.60487 16.8663 1.61898 16.8436 1.63422 16.8217C1.63814 16.816 1.64254 16.8099 1.64705 16.8038C1.67217 16.7706 1.69507 16.7443 1.71967 16.7197L3.71967 14.7197C4.01256 14.4268 4.48744 14.4268 4.78033 14.7197C5.0466 14.9859 5.0708 15.4026 4.85295 15.6962L4.78033 15.7803L4.06 16.5H16.75C17.6682 16.5 18.4212 15.7929 18.4942 14.8935L18.5 14.75V6.25C18.5 5.83579 18.8358 5.5 19.25 5.5ZM16.1962 0.147052L16.2803 0.21967L18.2803 2.21967C18.3058 2.24512 18.3294 2.27239 18.351 2.30126L18.2803 2.21967C18.3174 2.25672 18.3498 2.29669 18.3774 2.33883C18.3906 2.35898 18.403 2.37997 18.4143 2.40158C18.4192 2.4107 18.4235 2.41943 18.4277 2.42823C18.4404 2.45502 18.4519 2.48348 18.4617 2.51274C18.464 2.51968 18.4659 2.52575 18.4677 2.53184C18.4886 2.60032 18.5 2.67384 18.5 2.75C18.5 2.78839 18.4971 2.82611 18.4916 2.86295C18.4901 2.8725 18.4884 2.88219 18.4865 2.89186C18.4814 2.91897 18.4748 2.94527 18.4669 2.97098C18.4633 2.98264 18.4593 2.99469 18.4549 3.00665C18.4464 3.02995 18.4371 3.05202 18.4268 3.07352C18.4213 3.08512 18.415 3.09746 18.4084 3.10965C18.3951 3.13367 18.381 3.15639 18.3658 3.17826C18.3619 3.18401 18.3575 3.19014 18.3529 3.19621C18.3292 3.22765 18.3078 3.25237 18.285 3.2756L18.2803 3.28033L16.2803 5.28033C15.9874 5.57322 15.5126 5.57322 15.2197 5.28033C14.9534 5.01406 14.9292 4.5974 15.1471 4.30379L15.2197 4.21967L15.938 3.5H3.25C2.33183 3.5 1.57881 4.20711 1.5058 5.10647L1.5 5.25V13.75C1.5 14.1642 1.16421 14.5 0.75 14.5C0.370304 14.5 0.0565091 14.2178 0.00684667 13.8518L0 13.75V5.25C0 3.51697 1.35645 2.10075 3.06558 2.00514L3.25 2H15.939L15.2197 1.28033C14.9534 1.01406 14.9292 0.5974 15.1471 0.303788L15.2197 0.21967C15.4859 -0.0465967 15.9026 -0.0708026 16.1962 0.147052ZM10 6C12.2091 6 14 7.79086 14 10C14 12.2091 12.2091 14 10 14C7.79086 14 6 12.2091 6 10C6 7.79086 7.79086 6 10 6ZM10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5Z"
        fill="#212121" />
    </svg>

    <span i18n>Redeem points for data*</span>
  </div>
  <div>
    <div class="radio">
      <div (click)="radioToggle(3)" fxLayoutAlign="start center">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"
          *ngIf="radioValue !== 3">
          <circle cx="8" cy="8" r="7.5" fill="white" stroke="black" />
        </svg>
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"
          *ngIf="radioValue === 3">
          <circle cx="8" cy="8" r="7.5" fill="black" stroke="black" />
        </svg>
        <span i18n>Redeem 3 Pts for 5G data</span>
      </div>
      <div (click)="radioToggle(5)" fxLayoutAlign="start center" style="margin-top:10px;">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"
          *ngIf="radioValue !== 5">
          <circle cx="8" cy="8" r="7.5" fill="white" stroke="black" />
        </svg>
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"
          *ngIf="radioValue === 5">
          <circle cx="8" cy="8" r="7.5" fill="black" stroke="black" />
        </svg>
        <span i18n>Redeem 5 Pts for 10G data</span>
      </div>
    </div>
  </div>
  <button [disabled]="!service" mat-raised-button class="btn-yellow" style="margin-top: 22px;" (click)="redeem()" i18n>Redeem</button>
  <h4 i18n>*We will deduct the same amount from your current used data</h4>
</section>
