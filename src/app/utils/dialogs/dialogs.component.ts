import { Component, Inject, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { APIManager } from "@flashvpn-io/web-core";
import { AFTER_TRANSFER_FUND, NotificationService } from "@flashvpn-io/web-core";
import { WalletsService } from "@flashvpn-io/web-core";
import { AppService } from "src/app/app.service";
import * as Big from "big.js";

@Component({
  selector: "app-dialogs",
  templateUrl: "./dialogs.component.html",
  styleUrls: ["./dialogs.component.css"],
})
export class DialogsComponent implements OnInit {
  constructor() {}

  ngOnInit(): void {}
}
