<style>
  .oauthLink{
    transform: scale(1.5);
  }
  :host ::ng-deep .mat-form-field-underline {
    display: none!important;
  }
  .form-input {
    width: 420px;
    height: 56px;
  }
  :host ::ng-deep .mat-form-field-infix {
    display: flex;
  }
  :host ::ng-deep .mat-form-field-flex{
    padding: 0.75em !important;
  }
  .screen {
    align-items: flex-start;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 15px 11px;
    position: relative;
  }

  .screen .frame {
    align-items: flex-start;
    display: inline-flex;
    flex: 0 0 auto;
    flex-direction: column;
    gap: 16px;
    position: relative;
    width: 100%;
  }

  .screen .div {
    color: #000000;
    font-family: var(---font-family);
    font-size: var(---font-size);
    font-style: var(---font-style);
    font-weight: var(---font-weight);
    letter-spacing: var(---letter-spacing);
    line-height: var(---line-height);
    margin-top: -1px;
    position: relative;
    width: 327px;
  }

  .screen .flash-VPN {
    color: #7f7f7f;
    font-family: var(---font-family);
    font-size: var(---font-size);
    font-style: var(---font-style);
    font-weight: var(---font-weight);
    letter-spacing: var(---letter-spacing);
    line-height: var(---line-height);
    margin-top: -1px;
    position: relative;
    width: 100%;
  }

  .screen .view {
    align-items: center;
    background-color: var(--variable-collection);
    border-radius: 2px;
    display: flex;
    flex: 0 0 auto;
    justify-content: left;
    position: relative;
    width: 100%;
  }

  .screen .frame-2 {
    align-items: center;
    display: flex;
    justify-content: space-between;
    padding: 5px 0px;
    position: relative;
    width: 303px;
  }

  .screen .frame-wrapper {
    height: 14px;
    position: relative;
    width: 174px;
  }

  .screen .div-wrapper {
    height: 14px;
    position: relative;
  }

  .screen .text-wrapper-2 {
    color: var(--variable-collection-2);
    font-family: var(---font-family);
    font-size: var(---font-size);
    font-style: var(---font-style);
    font-weight: var(---font-weight);
    height: 14px;
    left: 0;
    letter-spacing: var(---letter-spacing);
    line-height: var(---line-height);
    position: absolute;
    top: -1px;
    width: 242px;
  }

  .screen .text-wrapper-3 {
    color: #ff5d5d;
    font-family: var(---font-family);
    font-size: var(---font-size);
    font-style: var(---font-style);
    font-weight: var(---font-weight);
    letter-spacing: var(---letter-spacing);
    line-height: var(---line-height);
    margin-top: -1px;
    position: relative;
    white-space: nowrap;
    width: fit-content;
    cursor: pointer;
  }

  .screen .component-109 {
    flex: 0 0 auto !important;
  }

  .screen .view-2 {
    height: 16px;
    position: relative;
    width: 129px;
  }
</style>
<div class="screen">
  <div class="frame">
    <div class="div">验证邮箱</div>
  </div>
  <div class="frame">
    <div class="flash-VPN">我们已经向您的电子邮件发送了确认码，请在此处输入以验证邮箱。</div>
  </div>
  <div class="frame">
    <div class="view">
        <mat-form-field class="form-input" floatLabel="never" appearance="fill" empty="false" autofilled="false">
          <input matInput placeholder="请输入验证码" (keyup.enter)="verify()" [(ngModel)]="code" required >
          <div *ngIf="sent" class="text-wrapper-3">重新发送({{countDown}}s)</div>
          <div *ngIf="!sent" class="text-wrapper-3" (click)="resend()" >重新发送</div>
        </mat-form-field>

    </div>
  </div>
  <div class="frame">
    <button [disabled]="submitted" (click)="verify()"  mat-raised-button class="btn-black" style="margin-top: 22px;" color="warn">验证</button>
    <mat-progress-bar *ngIf="submitted" mode="indeterminate" ></mat-progress-bar>
  </div>
</div>
