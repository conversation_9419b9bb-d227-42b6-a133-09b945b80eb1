import { Component, Inject, Injectable, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import * as qrcode from 'qrcode';
import { AppService } from '../../app.service';

@Component({
  selector: 'app-qr-dialog',
  template: `
    <div class="qr-dialog">
      <h2 mat-dialog-title class="dialog-title">{{data.title}} 二维码</h2>
      <mat-dialog-content class="dialog-content">
        <img [src]="qrCodeUrl" alt="QR Code">
        <p class="url-text">{{data.url}}</p>
      </mat-dialog-content>
      <mat-dialog-actions class="dialog-actions">
        <button mat-button class="copy-button" (click)="copyUrl()">
          <span>复制链接</span>
        </button>
        <button mat-button class="close-button" mat-dialog-close>关闭</button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [`
    .qr-dialog {
      padding: 24px;
      min-width: 320px;
    }

    .dialog-title {
      margin: 0;
      font-family: "Noto Sans SC-Bold", Helvetica;
      font-weight: 700;
      font-size: 18px;
      color: var(--neutral-800);
      text-align: center;
      margin-bottom: 20px;
    }

    .dialog-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0;
    }

    img {
      width: 256px;
      height: 256px;
      margin-bottom: 16px;
    }

    .url-text {
      word-break: break-all;
      font-family: var(--ch-body-14-400-font-family);
      font-size: var(--ch-body-14-400-font-size);
      color: var(--neutral-700);
      text-align: center;
      padding: 0 16px;
    }

    .dialog-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
      padding: 24px 0 0;
      margin: 0;
    }

    .copy-button, .close-button {
      min-width: 100px;
      padding: 8px 16px;
      border-radius: 4px;
      font-family: var(--ch-body-14-500-font-family);
      font-size: var(--ch-body-14-500-font-size);
      font-weight: var(--ch-body-14-500-font-weight);
      background-color: var(--primary-red-500);
      transition: all 0.3s ease;
      cursor: pointer;
      color: black;
    }

    .copy-button {
      background-color: var(--primary-red-500);
      color: black;
      border: none;
    }

    .copy-button:hover {
      background-color: rgba(255, 93, 93, 0.9);
      color: white;
    }

    .copy-button:active {
      transform: scale(0.98);
    }

    .close-button {
      background-color: white;
      border: 1px solid var(--neutral-300);
      color: var(--neutral-700);
    }

    .close-button:hover {
      background-color: rgba(0, 0, 0, 0.04);
      border-color: var(--neutral-400);
    }

    .close-button:active {
      transform: scale(0.98);
    }

    ::ng-deep .mat-dialog-container {
      padding: 0;
      border-radius: 8px;
      overflow: hidden;
    }

    ::ng-deep .mat-button {
      line-height: unset;
      padding: 0;
    }
  `]
})
export class QrDialogComponent {
  qrCodeUrl: string = '';

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: { url: string, title: string },
    private appService: AppService
  ) {
    this.generateQRCode();
  }

  async generateQRCode() {
    try {
      this.qrCodeUrl = await qrcode.toDataURL(this.data.url);
    } catch (err) {
      console.error('Error generating QR code:', err);
    }
  }

  copyUrl() {
    navigator.clipboard.writeText(this.data.url);
    this.appService.snackUp('复制成功');
  }
}

@Injectable({
  providedIn: 'root'
})
export class QrDialogService {
  constructor(private dialog: MatDialog) { }

  showQRCode(url: string, title: string) {
    this.dialog.open(QrDialogComponent, {
      data: { url, title },
      width: '368px'
    });
  }
} 