<style>
  .btn-black {
    background: #000;
    color: #fff;
  }

  .title {}

  .title span {
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 17px;
    margin-left: 10px;
  }

  .code {
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: 500;
    font-size: 24px;
    line-height: 29px;
    margin-top: 15px;

    color: #000000;

  }

</style>
<section fxLayout="column" fxLayoutAlign="center start">
  <div fxLayoutAlign="start center" class="title">
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9 11.17V5H11V11.17L13.59 8.59L15 10L10 15L5 10L6.41 8.59L9 11.17Z" fill="black" />
      <path fill-rule="evenodd" clip-rule="evenodd"
        d="M10 0C15.5228 -2.41411e-07 20 4.47715 20 10C20 15.5228 15.5228 20 10 20C4.47715 20 2.41411e-07 15.5228 0 10C-2.41411e-07 4.47715 4.47715 2.41411e-07 10 0ZM10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10C2 5.58172 5.58172 2 10 2Z"
        fill="black" />
    </svg>

    <span i18n>Share your address to receive</span>
  </div>
  <div class="code">
   {{wallet?.address}}
  </div>
  <button [disabled]="!wallet" (click)="copy()" mat-raised-button class="btn-black" style="margin-top: 13px;" i18n>Copy</button>
</section>
