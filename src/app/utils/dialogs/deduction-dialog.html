<style>
  .payments {
    color: #000;
    font-size: 14px;
    line-height: 17px;
  }
  .red-bg {
    color: #FF5E5E;
  }

  .btn {
    border-radius: 0;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    padding: 8px 20px;
    color: #fff;
    border: none;
    cursor: pointer;
  }

  .btn-red {
    background: #FF5E5E;
  }

  .example-full-width {
    width: 100%;
  }

  .gap {
    margin-top: 10px;
  }

  .balance-box {
    font-size: 22px;
    font-weight: normal;
  }
</style>
<div class="payments">
  <div fxLayout="row" fxLayoutAlign="space-between center">
    <h2 class="module-title" i18n>Deduction</h2>
    <span class="material-icons" (click)="dialogRef.close()" style="cursor: pointer;">
      close
    </span>
  </div>
  <mat-progress-bar *ngIf="isLoading" mode="indeterminate"></mat-progress-bar>
  <div *ngIf="!isLoading" fxLayout="row" fxLayoutAlign="space-around start" class="payments-content">
    <div fxLayout="column" fxLayoutAlign="start" class="dashed-right">
      <div class="product-item" fxLayout="column" fxLayoutAlign="start">
        <div class="price">
            <span class="balance-box" i18n>Your balance is :</span>
            <span class="balance-box red-bg"> {{wallet.balance}} </span>
            <span class="balance-box">HKD</span>
        </div>
        <div class="gap" i18n>Please enter the balance you want to deduct, please note that whether you pay the order or not, the balance will be deducted after deduction and will not be refunded.</div>
        <div class="gap" i18n>If you want to pay entirely with your balance, please use the balance payment method.</div>
        <div class="gap">
          <mat-form-field class="example-full-width  promoCode">
            <input id="deductionAmount" type="number" matInput i18n-placeholder placeholder="Deductible amount" [(ngModel)]="deductionAmount" required name="deductionAmount">
          </mat-form-field>
        </div>
      </div>
      <button class="btn btn-red" mat-button i18n (click)="confirm()">Deduction</button>
    </div>
  </div>
</div>
