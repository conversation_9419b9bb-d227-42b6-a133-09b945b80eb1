<style>
  .payments {
    color: #000;
    font-size: 12px;
    line-height: 15px;
  }

  ul {
    margin: 0;
    padding: 0;
  }

  li {
    list-style: none;
  }

  .product-item {
    position: relative;
    width: 300px;
    height: 363px;
    padding: 20px;
  }

  .product-item .save {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 94, 94, 0.3);
    padding: 5px 10px;
    font-weight: 500;
    font-size: 12px;
    line-height: 15px;
    color: #FF5E5E;

  }

  .product-item h6 {
    margin: 0;
    padding: 0;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    margin-bottom: 20px;
  }

  .price {
    margin-bottom: 21px;
  }

  .price-num {
    font-weight: 500;
    font-size: 24px;
    line-height: 28px;
  }

  .total-price {
    margin-bottom: 18px;
  }

  .total-price-divide {
    margin: 0 8px;
  }

  .list-icon-item li::before {
    content: '-';
    display: inline-block;
    margin-right: 10px;
  }

  .red {
    color: #FF5E5E;
  }

  .dashed-right {
    position: relative;
  }

  .stepNumber {
    width: 20px;
    height: 20px;
    color: white;
    border-radius: 10px;
    background: #FF5E5E;
  }

  .divide {
    width: 1px;
    height: 400px;
    border-right: 1px dashed #dadada;
  }

  .form-input {
    display: block;
    width: 250px;
  }

  @media screen and (max-width: 599px) {
    .payments-content {
      flex-direction: column!important;
    }
    .divide {
      display: none;
    }
    .product-item {
      height: auto;
      width: 300px;
    }
    .argument {
      margin-bottom: 20px;
    }
    :host ::ng-deep .paymentBox {
      width: 300px;
      min-width: 300px !important;
    }
  }
</style>
<div class="payments">
  <div fxLayout="row" fxLayoutAlign="space-between center">
    <h2 class="module-title" i18n>Payments</h2>
    <span class="material-icons" (click)="dialogRef.close()" style="cursor: pointer;">
      close
    </span>
  </div>
  <div fxLayout="row" fxLayoutAlign="space-around start" class="payments-content">
    <div fxLayout="column" fxLayoutAlign="start" class="dashed-right">
      <div class="product-item" fxLayout="column" fxLayoutAlign="start">
        <div *ngIf="promoCode | validatePromoCode" class="save">Save {{product.calculateSaved(billingCycle).saved | discount: promoCode}}%</div>
        <div *ngIf="!promoCode || !(promoCode | validatePromoCode)" class="save">Save {{product.calculateSaved(billingCycle).saved}}%</div>
        <h6>{{product.name}}</h6>
        <div class="price">
          <span class="h2Size">{{product.calculateSaved(billingCycle).price.toFixed(2)}}
            <span class="h2Size" i18n>HKD</span></span>
          <span>&nbsp;/&nbsp; {{appService.translate(billingCycle)}}</span>
        </div>
        <div class="total-price">
          <span>{{product.calculateSaved('annually').price.toFixed(2)}}<span i18n>HKD</span> / Yearly </span>
          <span class="total-price-divide">|</span>
          <span class="red" style="text-decoration: line-through">{{product.calculateSaved(billingCycle).pricePaidByMonth.toFixed(2)}}<span i18n>HKD</span> </span>
        </div>
        <div [innerHTML]="product.description"></div>
        <div>
          <mat-form-field class="example-full-width  promoCode">
            <input id="promoCodeInput" matInput i18n-placeholder placeholder="Have a Promo Code?"  required  [(ngModel)]="promoCode" name="email">
          </mat-form-field>
          <div *ngIf="promoCode && !(promoCode | validatePromoCode)" i18n>This promotion code is wrong, please contact CS.</div>
        </div>
      </div>
      <div class="argument" style="margin-top: auto;width: 240px;"><span i18n>By continuing the payment , you agree to our</span><a
        href="static/tos.html" target="_blank" i18n>Terms of Service</a> <span i18n> and </span> <a
        href="static/privacy.html" target="_blank" i18n>Privacy Policy</a>.</div>
    </div>
    <div class="divide"></div>
    <div>
      <div *ngIf="!usersService.user" id="productStep1">
        <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5px">
          <div fxLayout="row" fxLayoutAlign="center center" class="stepNumber">
            <div>1</div>
          </div>
          <h3 i18n>Fill out your email to create an account</h3>
        </div>
        <div>
          <form #form="ngForm">
            <mat-form-field class="form-input">
              <input matInput i18n-placeholder="" placeholder="Input your email" required [(ngModel)]="email"
                     name="email">
            </mat-form-field>

            <mat-form-field class="form-input">
              <input matInput i18n-placeholder placeholder="Enter your password" [type]="hide ? 'password' : 'text'"
                     required [(ngModel)]="password" name="password">
              <a mat-icon-button matSuffix (click)="hide = !hide" [attr.aria-label]="'Hide password'"
                 [attr.aria-pressed]="hide">
                <mat-icon>{{hide ? 'visibility_off' : 'visibility'}}</mat-icon>
              </a>
            </mat-form-field>
          </form>

        </div>
      </div>
      <div id="productStep2">
        <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5px">
          <div fxLayout="row" fxLayoutAlign="center center" class="stepNumber">
            <div>{{usersService.user ? 1 : 2}}</div>
          </div>
          <h3 i18n>Pick your preferred payment method</h3>
        </div>
        <div>
          <app-payment #paymentComponent [service]="" [product]="product" [invoice]="invoice"
                       [preSubmit]="placeOrder.bind(this)" [promoCode]="promoCode"></app-payment>
        </div>
      </div>
    </div>

  </div>
</div>
