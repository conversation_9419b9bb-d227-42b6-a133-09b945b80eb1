import { ApplicationRef, Component, Inject, Injectable, OnInit, ViewChild } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from "@angular/material/dialog";
import { AppService } from "src/app/app.service";
import { APIManager, USER_EMAIL_VERIFIED } from "@flashvpn-io/web-core";
import { AFTER_INVOICE_DEDUCT, AFTER_ORDER_CREATED, AFTER_TRANSFER_FUND, AFTER_USER_LOGOUT, AFTER_INVOICE_CANCEL, NotificationService } from "@flashvpn-io/web-core";
import { WalletsService } from "@flashvpn-io/web-core";
import * as Big from "big.js";
import { Router } from "@angular/router";
import { DomSanitizer } from "@angular/platform-browser";
import { HttpClient } from "@angular/common/http";
import { PaymentComponent } from "src/app/billings/payment/payment.component";
import { environment } from "../../../environments/environment";
import { ServiceRenewComponent } from "../../services/service-renew/service-renew.component";
import { SelectVoucherComponent } from "../../services/select-voucher/select-voucher.component";
import { Service } from "@flashvpn-io/web-core";
import { Wallet } from "@flashvpn-io/web-core";
import { Product } from "@flashvpn-io/web-core";
import { Invoice } from "@flashvpn-io/web-core";
import { User } from "@flashvpn-io/web-core";
import { Rule } from "@flashvpn-io/web-core";
import { UsersService } from "@flashvpn-io/web-core";
import { MatSnackBar } from "@angular/material/snack-bar";
import { Utils } from "../utils";

@Injectable({
  providedIn: "root",
})
export class DialogsService {
  public dialogRef;

  constructor(public dialog: MatDialog) { }

  isMobile() {
    const flag = navigator.userAgent.match(
      /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
    );
    return flag;
  }

  closeDialog() {
    this.dialog.closeAll();
  }

  /**
   * manage all dialogs
   * @param name name of dialog
   * @param data optional data
   */
  openDialog(name, data = null): void {
    let dialogRef = null;
    switch (name) {
      case "redeem":
        dialogRef = this.dialog.open(RedeemDialog, {
          width: "80%",
          data,
        });
        break;
      case "send":
        dialogRef = this.dialog.open(SendDialog, {
          width: "80%",
          data,
        });
        break;
      case "copy":
        dialogRef = this.dialog.open(ReceiveDialog, {
          width: "80%",
          data,
        });
        break;
      case "update":
        dialogRef = this.dialog.open(UpdateDialog, {
          width: "80%",
          data,
        });
        break;
      case "payments":
        if (!this.isMobile() && window.innerWidth > 599) {
          dialogRef = this.dialog.open(PaymentsDialog, {
            width: "60vw",
            data,
          });
        } else {
          dialogRef = this.dialog.open(PaymentsDialog, {
            width: "90vw",
            height: "90vh",
            maxWidth: "100vw",
            data,
          });
        }
        break;
      case "windows-export":
      case "macos-export":
      case "android-export":
        dialogRef = this.dialog.open(WindowsExportDialog, {
          width: "80%",
          data,
        });
        break;
      case "update-confirm-dialog":
        dialogRef = this.dialog.open(UpdateConfirmDialog, {
          width: window.innerWidth > 599 ? "50%" : "80%",
          data,
        });
        break;
      case "upgrade-confirm-dialog":
        dialogRef = this.dialog.open(UpgradeConfirmDialog, {
          width: window.innerWidth > 599 ? "50%" : "80%",
          data,
        });
        break;
      case "deduction-dialog":
        if (!this.isMobile() && window.innerWidth > 599) {
          dialogRef = this.dialog.open(DeductionDialog, {
            width: "50vw",
            data,
          });
        } else {
          dialogRef = this.dialog.open(DeductionDialog, {
            width: "80vw",
            maxWidth: "100vw",
            data,
          });
        }
        break;
      case "payment-method-dialog":
        dialogRef = this.dialog.open(PaymentMenthodsDialog, {
          width: "60%",
          data,
        });
        break;
      case "signout-check-dialog":
        dialogRef = this.dialog.open(SignoutCheckDialog, {
          width: window.innerWidth > 599 ? "20%" : "80%",
          data,
        });
        break;
      case "send-success-dialog":
        dialogRef = this.dialog.open(SendSuccessDialog, {
          width: window.innerWidth > 599 ? "50%" : "80%",
          data,
        });
        break;
      case "select-voucher-dialog":
        dialogRef = this.dialog.open(SelectVoucherDialog, {
          width: window.innerWidth > 599 ? "50%" : "80%",
          minWidth: "806px",
          data,
        });
        break;
      case "invoice-check-dialog":
        dialogRef = this.dialog.open(InvoiceCheckDialog, {
          width: window.innerWidth > 599 ? "50%" : "80%",
          data,
        });
        break;
      case "trial-invoice-check-dialog":
        dialogRef = this.dialog.open(TrialInvoiceCheckDialog, {
          width: window.innerWidth > 599 ? "50%" : "80%",
          data,
        });
        break;
      case "email-verification-dialog":
        dialogRef = this.dialog.open(EmailVerificationDialog, {
          width: window.innerWidth > 599 ? "492px" : "80%",
          data,
        });
        break;
      case "trial-failure-dialog":
        dialogRef = this.dialog.open(TrialFailureDialog, {
          width: window.innerWidth > 599 ? "492px" : "80%",
          data,
        });
        break;
      default:
        break;
    }
    this.dialogRef = dialogRef;
  }
}

@Component({
  selector: "app-redeem-dialog",
  templateUrl: "./redeem-dialog.html",
})
export class RedeemDialog implements OnInit {
  radioValue = 3;
  public service: Service = null;
  private submitted = false;

  constructor(
    public dialogRef: MatDialogRef<RedeemDialog>,
    public appService: AppService,
    public apiManager: APIManager,
    public walletsService: WalletsService,
    @Inject(MAT_DIALOG_DATA) public data
  ) { }

  onNoClick(): void {
    this.dialogRef.close();
  }

  radioToggle(value): void {
    this.radioValue = value;
  }

  async ngOnInit() { }

  redeem() {
    if (this.walletsService.wallet$.value?.balance < this.radioValue) {
      this.appService.snackUp(this.appService.translate(`InsufficientFunds`));
      return;
    }
    this.submitted = true;
    this.apiManager.redeemData(this.service, this.radioValue).subscribe(
      (value) => {
        this.dialogRef.close();
        this.appService.snackUp($localize`:@@redeem-finished:You successfully redeemed data, go to service and check`);
      },
      (error) => this.appService.snackUp(error?.error?.message),
      () => (this.submitted = false)
    );
  }
}

@Component({
  selector: "send-dialog",
  templateUrl: "./send-dialog.html",
})
export class SendDialog {
  public submitted = false;
  address?: string;
  amount?: string;

  constructor(
    public dialogRef: MatDialogRef<SendDialog>,
    @Inject(MAT_DIALOG_DATA) public data,
    public appService: AppService,
    private notificationService: NotificationService,
    public apiManager: APIManager
  ) { }

  onNoClick(): void {
    this.dialogRef.close();
  }

  send() {
    this.submitted = true;
    this.apiManager.transferFunds(this.address, this.amount).subscribe(
      (value) => {
        this.appService.snackUp($localize`:@@sent-funds-successfully:You successfully sent flash points to your friend, go to transaction to check`);
        this.notificationService.post(AFTER_TRANSFER_FUND, value);
        this.dialogRef.close();
      },
      (res) => {
        this.appService.snackUp(res?.error?.message);
        this.submitted = false;
      },
      () => (this.submitted = false)
    );
  }

  validateAmount(value: string) {
    try {
      const filterd = new Big(value);
      if (filterd.lte(0)) {
        return null;
      } else {
        return filterd.round();
      }
    } catch (e) {
      return null;
    }
  }
}

@Component({
  selector: "receive-dialog",
  templateUrl: "./receive-dialog.html",
})
export class ReceiveDialog {
  public wallet?: Wallet = null;

  constructor(public dialogRef: MatDialogRef<ReceiveDialog>, public appService: AppService, @Inject(MAT_DIALOG_DATA) public data) {
    this.wallet = data;
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  async copy() {
    this.appService.copyMessage(this.wallet.address);
    this.appService.snackUp($localize`:@@walletAddressCopied:Wallet address is copied, share it with your friend and receive points`);
    this.dialogRef.close();
  }
}

@Component({
  selector: "update-dialog",
  templateUrl: "./update-dialog.html",
})
export class UpdateDialog {
  public platform = "";
  private release = null;

  constructor(public dialogRef: MatDialogRef<ReceiveDialog>, public appService: AppService, @Inject(MAT_DIALOG_DATA) public data) {
    const { platform, release } = data;
    this.platform = platform;
    this.release = release;
  }

  ignore() {
    localStorage.setItem("ignoredVersion", this.release.version);
    this.dialogRef.close();
  }
}

@Component({
  selector: "payments-dialog",
  templateUrl: "./payments-dialog.html",
})
export class PaymentsDialog {
  public serverUrl;
  product: Product;
  billingCycle = "monthly";
  email: any;
  password: any;
  fakeService: Service = new Service("--", null, "--", this.billingCycle, null, null, {}, null);
  invoice?: Invoice;
  promoCode?: string;
  hide: any;

  @ViewChild("paymentComponent") paymentComponent: PaymentComponent;

  constructor(
    public apiManager: APIManager,
    public dialogRef: MatDialogRef<ReceiveDialog>,
    public appService: AppService,
    public http: HttpClient,
    public usersService: UsersService,
    public appRef: ApplicationRef,
    public notification: NotificationService,
    public router: Router,
    @Inject(MAT_DIALOG_DATA) public data
  ) {
    const { product, billingCycle, promoCode } = data;
    this.product = product;
    this.billingCycle = billingCycle;
    this.promoCode = promoCode;
    this.fakeService.billingcycle = this.billingCycle;
    this.fakeService.name = this.product.name;
    const serverUrl = localStorage.getItem("serverUrl");
    if (serverUrl) {
      this.serverUrl = serverUrl;
    } else {
      this.serverUrl = environment.apiUrl !== undefined ? environment.apiUrl : "";
    }
  }

  async createUser(): Promise<boolean> {
    if (this.usersService.user) {
      return true;
    }
    try {
      if (this.email === undefined || this.password === undefined) {
        this.appService.snackUp("邮箱或者密码为空");
        return false;
      }
      if (this.email && this.email.length > 20) {
        this.appService.snackUp("电子邮件地址超过20个字符");
        return false;
      }
      const reg1 = /^\w+((.\w+)|(-\w+))@[A-Za-z0-9]+((.|-)[A-Za-z0-9]+).[A-Za-z0-9]+$/;
      const isReg1 = reg1.test(this.email);
      if (!isReg1) {
        this.appService.snackUp("电子邮件格式错误");
        return false;
      }
      const reg2 = /(^\s+)|(\s+$)|\s+|\++/g;
      const isReg2 = reg2.test(this.email);
      if (isReg2) {
        this.appService.snackUp("电子邮件名称包含非法字符");
        return false;
      }
      const reg3 = /(^\s+)|(\s+$)|\s+|\++/g;
      const isReg3 = reg3.test(this.password);
      const reg4 = /[\u4e00-\u9fa5]+/g;
      const isReg4 = reg4.test(this.password);
      if (isReg3 || isReg4) {
        this.appService.snackUp("密码包含非法字符");
        return false;
      }
      if (this.password && this.password.length > 20) {
        this.appService.snackUp("密码长度超过20个字符");
        return false;
      }
      if (this.password && this.password.length < 8) {
        this.appService.snackUp("密码长度小于8个字符");
        return false;
      }
      const user = await this.http
        .post(`${this.serverUrl}/api/v1/users`, {
          email: this.email,
          password: this.password,
        })
        .toPromise();
      await this.usersService.loginByUser(user as User);
      return true;
    } catch (e) {
      this.appService.snackUp(e?.error?.message);
      return false;
    }
  }

  async placeOrder(): Promise<boolean> {
    if (!this.product) {
      this.appService.snackUp("产品未选择");
      return false;
    }
    if (this.usersService.user === undefined) {
      if (!(await this.createUser())) {
        return false;
      }
    }

    try {
      this.invoice = await this.apiManager.createOrder(this.product, this.billingCycle, this.promoCode).toPromise();
      /**
       * Thi is important!!!!
       * Call this to force change detection, so the invoice component can have our invoice
       */
      this.appRef.tick();

      // let service component refresh
      this.notification.post(AFTER_ORDER_CREATED);
      await this.router.navigate(["invoices"], { queryParams: { id: this.invoice.id, pulling: true } });
      this.dialogRef.close();
      return true;
    } catch (e) {
      this.appService.snackUp(e?.error?.message);
      return false;
    }
  }
}

@Component({
  selector: "app-windows-export-dialog",
  templateUrl: "./windows-export-dialog.html",
})
export class WindowsExportDialog {
  public service: Service;

  constructor(
    public dialogRef: MatDialogRef<ReceiveDialog>,
    public appService: AppService,
    public userService: UsersService,
    public router: Router,
    public sanitizer: DomSanitizer,
    @Inject(MAT_DIALOG_DATA) public data
  ) {
    this.service = data;
  }
}

export interface ConfirmDTO {
  billingCycle: string;
  service: Service;
}

@Component({
  selector: "update-confirm-dialog",
  templateUrl: "./update-confirm-dialog.html",
})
export class UpdateConfirmDialog {
  constructor(
    public dialogRef: MatDialogRef<UpdateConfirmDialog>,
    @Inject(MAT_DIALOG_DATA)
    public data: ConfirmDTO,
    public appService: AppService
  ) { }

  onNoClick(): void {
    this.dialogRef.close();
  }

  updateBillingCycle(): void {
    this.appService.snackUp(this.appService.translate(`Processing`));
    this.dialogRef.close(this.data.billingCycle);
  }
}

@Component({
  selector: "upgrade-confirm-dialog",
  templateUrl: "./upgrade-confirm-dialog.html",
})
export class UpgradeConfirmDialog {
  constructor(
    public dialogRef: MatDialogRef<UpgradeConfirmDialog>,
    @Inject(MAT_DIALOG_DATA)
    public data: any,
    public appService: AppService
  ) { }

  onNoClick(): void {
    this.dialogRef.close();
  }

  matchData() {
    return this.data.product.description.match(/\d{2,3}G/g).pop();
  }

  upgrade(): void {
    this.appService.snackUp(this.appService.translate(`Processing`));
    this.dialogRef.close(this.data);
  }
}

@Component({
  selector: "deduction-dialog",
  templateUrl: "./deduction-dialog.html",
})
export class DeductionDialog implements OnInit {
  public wallet: Wallet;
  public isLoading = true;
  public deductionAmount;
  public invoice: Invoice;

  constructor(
    public dialogRef: MatDialogRef<DeductionDialog>,
    public appService: AppService,
    public apiManager: APIManager,
    @Inject(MAT_DIALOG_DATA)
    public data,
    public notification: NotificationService
  ) {
    this.invoice = data.invoice;
  }

  ngOnInit() {
    this.apiManager.fetchWallet().subscribe((value) => {
      this.isLoading = false;
      this.wallet = value;
    });
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  confirm() {
    if (!this.deductionAmount) {
      this.appService.snackUp($localize`:@@DeductionAmountIsEmpty:Deduction amount is Empty.`);
      return;
    }
    if (this.deductionAmount < 1) {
      this.appService.snackUp($localize`:@@DeductionAmountCannotBeNegative:Deduction amount can not be negative.`);
      return;
    }
    if (this.deductionAmount > this.wallet.balance) {
      this.appService.snackUp($localize`:@@DeductionAmountExceedsBalance:Deduction amount exceeds Balance.`);
      return;
    }
    if (this.deductionAmount > this.invoice.total) {
      this.appService.snackUp($localize`:@@DeductionAmountExceedsTotal:Deduction amount exceeds Total.`);
      return;
    }
    this.apiManager.deductionInvoice(this.invoice, this.deductionAmount).subscribe(
      (value) => {
        this.notification.post(AFTER_TRANSFER_FUND);
        this.notification.post(AFTER_INVOICE_DEDUCT);
        this.dialogRef.close();
      },
      (res) => {
        this.appService.snackUp(res?.error?.message);
      }
    );
  }
}

@Component({
  selector: "payment-menthods-dialog",
  templateUrl: "./payment-methods-dialog.html",
})
export class PaymentMenthodsDialog implements OnInit {
  @ViewChild("paymentMethodsComponent") paymentMethodsComponent: ServiceRenewComponent;

  public title: string;

  public rechargeList: Rule[];

  public payInfo: Rule;

  public buyType: number;

  public eDate: string;

  constructor(@Inject(MAT_DIALOG_DATA) public data, public dialogRef: MatDialogRef<PaymentMenthodsDialog>) {
    this.title = this.data?.title;
    this.rechargeList = this.data?.rechargeList;
    this.payInfo = this.data?.rechargeList ? this.data?.rechargeList[0] : null;
    this.buyType = this.data?.buyType ? this.data?.buyType : 1;
    this.eDate = this.data?.eDate;
  }

  ngOnInit() { }

  close(): void {
    this.dialogRef.close();
  }
}

@Component({
  selector: "signout-check-dialog",
  templateUrl: "./signout-check-dialog.html",
})
export class SignoutCheckDialog implements OnInit {
  constructor(
    @Inject(MAT_DIALOG_DATA) public data,
    public dialogRef: MatDialogRef<PaymentMenthodsDialog>,
    public usersService: UsersService,
    private notification: NotificationService,
    public appService: AppService,
    public router: Router
  ) {
    console.log(data);
  }

  ngOnInit() { }

  close(): void {
    this.dialogRef.close();
  }

  async logout() {
    await this.usersService.logout();
    await this.router.navigate(["users/signin"]);
    this.notification.post(AFTER_USER_LOGOUT);
    this.dialogRef.close();
  }
}

@Component({
  selector: "send-success-dialog",
  templateUrl: "./send-success-dialog.html",
})
export class SendSuccessDialog implements OnInit {
  constructor(@Inject(MAT_DIALOG_DATA) public data, public dialogRef: MatDialogRef<PaymentMenthodsDialog>) { }

  ngOnInit() { }

  close(): void {
    this.dialogRef.close();
  }
}

@Component({
  selector: "select-voucher-dialog",
  templateUrl: "./select-voucher-dialog.html",
})
export class SelectVoucherDialog implements OnInit {
  @ViewChild("selectVoucherComponent") selectVoucherComponent: SelectVoucherComponent;

  constructor(@Inject(MAT_DIALOG_DATA) public data, public dialogRef: MatDialogRef<SelectVoucherDialog>) { }

  ngOnInit() { }

  close(): void {
    this.dialogRef.close();
  }
}

@Component({
  selector: "invoice-check-dialog",
  templateUrl: "./invoice-check-dialog.html",
})
export class InvoiceCheckDialog implements OnInit {
  loading = false;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data,
    public dialogRef: MatDialogRef<PaymentMenthodsDialog>,
    private router: Router,
    private notificationService: NotificationService,
    public apiManager: APIManager,
    private appService: AppService
  ) { }

  ngOnInit() { }

  gotoPay() {
    this.router.navigate(["invoices"], {
      queryParams: {
        id: this.data.invoiceId,
        buyType: this.data.buyType,
      },
    });
    this.dialogRef.close();
  }

  async gotoCancel() {
    this.loading = true;
    await this.apiManager.cancelInvoice(this.data.invoiceId).toPromise();
    this.notificationService.post(AFTER_INVOICE_CANCEL);
    this.appService.snackUp("取消成功");
    this.dialogRef.close();
  }
}

@Component({
  selector: "trial-invoice-check-dialog",
  templateUrl: "./trial-invoice-check-dialog.html",
})
export class TrialInvoiceCheckDialog implements OnInit {
  loading = false;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data,
    public dialogRef: MatDialogRef<PaymentMenthodsDialog>,
    private router: Router,
    private notificationService: NotificationService,
    public apiManager: APIManager,
    private appService: AppService
  ) { }

  ngOnInit() { }

  gotoPay() {
    this.router.navigate(["invoices"], {
      queryParams: {
        id: this.data.invoiceId,
        buyType: this.data.buyType,
        currentDeduction: "",
        discountCode: "",
      },
    });
    this.dialogRef.close();
  }

  async gotoCancel() {
    this.loading = true;
    await this.apiManager.cancelInvoice(this.data.invoiceId).toPromise();
    this.notificationService.post(AFTER_INVOICE_CANCEL);
    this.appService.snackUp("取消成功");
    this.dialogRef.close();
  }
}

@Component({
  selector: "email-verification-dialog",
  templateUrl: "./email-verification-dialog.html",
})
export class EmailVerificationDialog implements OnInit {
  loading = false;
  countDown = 60;
  code = "";
  sent = true;
  submitted = false;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data,
    public dialogRef: MatDialogRef<EmailVerificationDialog>,
    private router: Router,
    public apiManager: APIManager,
    private appService: AppService,
    private usersService: UsersService,
    private snackBar: MatSnackBar,
    private notificationService: NotificationService
  ) { }

  ngOnInit() {
    const timer = setInterval(() => {
      if (this.countDown > 0) {
        this.countDown--;
      } else {
        clearInterval(timer);
        this.sent = false;
      }
    }, 1000);
  }

  verify() {
    Utils.openPayWindow();
    this.submitted = true;
    this.apiManager.verifyEmail(this.code, this.data.email).subscribe(
      async (data) => {
        this.submitted = false;
        await this.usersService.loginByUser(data as User);
        this.snackBar.open("邮箱验证成功", "Okay", {
          duration: 2000,
          verticalPosition: "top",
        });
        this.notificationService.post(USER_EMAIL_VERIFIED);
        this.dialogRef.close();
      },
      (error) => {
        this.submitted = false;
        this.snackBar.open("邮箱验证失败，请稍后重试", "Okay", {
          duration: 2000,
          verticalPosition: "top",
        });
        Utils.closePayWindow();
      }
    );
  }

  async resend() {
    if (this.sent) {
      return;
    }
    this.sent = true;
    this.apiManager.resendEmail().subscribe(
      (_) => {
        this.snackBar.open("我们已经重新发送了确认邮件 ", "Okay", {
          duration: 2000,
          verticalPosition: "top",
        });
      },
      (res) => {
        this.snackBar.open(res?.error?.message, "Okay", {
          duration: 2000,
          verticalPosition: "top",
        });
        this.dialogRef.close();
      },
      () => { }
    );

    this.countDown = 60;
    const timer = setInterval(() => {
      if (this.countDown > 0) {
        this.countDown--;
      } else {
        clearInterval(timer);
        this.sent = false;
      }
    }, 1000);
  }
}

@Component({
  selector: "trial-failure-dialog",
  templateUrl: "./trial-failure-dialog.html",
})
export class TrialFailureDialog implements OnInit {
  loading = false;
  code = "";
  submitted = true;

  constructor(@Inject(MAT_DIALOG_DATA) public data, public dialogRef: MatDialogRef<TrialFailureDialog>, private router: Router) { }

  ngOnInit() { }

  toMyService() {
    // this.router.navigate(["services"]);
    this.router.navigate(["center"]);
    this.dialogRef.close();
  }
}
