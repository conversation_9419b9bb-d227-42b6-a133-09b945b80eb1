<style>
  .title {}

  .title span {
    font-family: 'Libre Franklin';
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 17px;
    margin-left: 10px;
  }

</style>
<section fxLayout="column" fxLayoutAlign="center start">
  <div fxLayoutAlign="start center" class="title">
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M11 8.83L11 15L9 15L9 8.83L6.41 11.41L5 10L10 5L15 10L13.59 11.41L11 8.83Z" fill="black" />
      <path fill-rule="evenodd" clip-rule="evenodd"
        d="M10 20C4.47715 20 0 15.5228 0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20ZM10 18C5.58172 18 2 14.4183 2 10C2 5.58172 5.58172 2 10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18Z"
        fill="black" />
    </svg>
    <span i18n>Send your points to friends</span>
  </div>
  <br/>
  <div>
    <mat-form-field>
      <label>
        <input i18n-placeholder="address" placeholder="address" required  [(ngModel)]="address"  matInput>
      </label>
    </mat-form-field>
    <mat-form-field>
      <label>
        <input (keyup.enter)="send()" #amountInput i18n-placeholder="amount"  placeholder="amount" [(ngModel)]="amount" (keyup)="amountInput.value = validateAmount(amountInput.value)" required  matInput>
      </label>
    </mat-form-field>
  </div>
  <button [disabled]="!address || !amount" (click)="send()"  mat-raised-button class="btn-black" style="margin-top: 22px;" i18n>Send</button>
  <mat-progress-bar *ngIf="submitted" mode="indeterminate"></mat-progress-bar>
</section>
