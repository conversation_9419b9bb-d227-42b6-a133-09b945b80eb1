import { Injectable } from '@angular/core';
import confetti from 'canvas-confetti';

@Injectable({
  providedIn: 'root'
})
export class ConfettiService {

  constructor() { }

  public canon(count?: number, spread?: number): void{
    confetti({
      particleCount: count || 200,
      spread: spread || 70,
      origin: { y: 0.6 }
    });
  }

  public fire(){
    const fireCanon = (particleRatio, opts) => {
      confetti.create()
      confetti(Object.assign({}, {origin: { y: 0.7 }}, opts, {
        particleCount: Math.floor(200 * particleRatio)
      }));
    };
    fireCanon(0.25, {
      spread: 26,
      startVelocity: 55,
    });
    fireCanon(0.2, {
      spread: 60,
    });
    fireCanon(0.35, {
      spread: 100,
      decay: 0.91,
      scalar: 0.8
    });
    fireCanon(0.1, {
      spread: 120,
      startVelocity: 25,
      decay: 0.92,
      scalar: 1.2
    });
    fireCanon(0.1, {
      spread: 120,
      startVelocity: 45,
    });
  }

  public randomCanon(): void {
    confetti({
      angle: this.randomInRange(55, 125),
      spread: this.randomInRange(50, 70),
      particleCount: this.randomInRange(50, 100),
      origin: { y: 0.6 }
    });
  }

  private randomInRange(min, max) {
    return Math.random() * (max - min) + min;
  }
}
