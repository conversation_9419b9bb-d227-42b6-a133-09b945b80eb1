.win-dialog-box {
    position: relative;
    width: 500px;
    font-size: 14px;
    height: 70vh;
}


/* 消息列表 */

.inbox-message-content .message-checked {
    display: flex;
}

.inbox-message-content .checkbox-msg {
    cursor: pointer;
    border-radius: 50%;
}

.inbox-message-content .content-title {
    text-align: center;
    font-size: 20px;
    font-weight: 600;
}

.inbox-message-content .filter {
    display: flex;
    font-size: 14px;
    color: #ff5e5e;
    justify-content: space-between;
    padding: 20px 4px 12px 4px;
}

.inbox-message-content .filter-left {
    display: flex;
    align-items: center;
}

.inbox-message-content .filter-left {
    cursor: pointer;
}

.inbox-message-content .filter-right {
    cursor: pointer;
}

.inbox-message-content .ul-wrapper {
    width: 100%;
    position: absolute;
    top: 75px;
    padding: 0px 4px 15px 4px;
    left: 0;
    z-index: 10;
    height: calc(70vh - 110px);
    overflow-y: scroll;
    box-sizing: border-box;
    padding-bottom: 56px;
    /* 为分页控件留出空间 */
}

.inbox-message-content .message-item {
    padding: 6px 20px 10px 20px;
    color: #A0AEC0;
    box-shadow: 0px 2px 10px rgba(255, 94, 94, 0.1);
    margin-top: 14px;
    cursor: pointer;
    border-left: 3px solid #ff5e5e;
}

.inbox-message-content .message-item-content {
    width: 100%;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    box-orient: vertical;
    -webkit-line-clamp: 2;
    /* 这里是超出几行省略 */
    line-clamp: 2;
    /* 这里是超出几行省略 */
    overflow: hidden;
}

.inbox-message-content .message-item p {
    line-height: normal !important;
    padding: 0 !important;
    margin: 0 !important;
    color: #000 !important;
    font-size: 14px !important;
    padding-top: 6px !important;
}

.inbox-message-content .message-item .message-item-title {
    font-weight: 700;
    padding-bottom: 4px;
}

.inbox-message-content .message-item.checkedMessage {
    border-left: 3px solid #A0AEC0;
}

.inbox-message-content .checkedMessage p {
    color: #A0AEC0 !important;
}

.dialog-button {
    justify-content: end;
    text-align: right;
    padding-top: calc(70vh - 114px);
}

/deep/.mat-dialog-container {
    overflow: hidden;
}


/* 消息详情 */

/* 分页控件样式 */
.mat-paginator {
    /* position: absolute; */
    bottom: -100px;
    left: 0;
    right: 0;
    background: transparent;
}