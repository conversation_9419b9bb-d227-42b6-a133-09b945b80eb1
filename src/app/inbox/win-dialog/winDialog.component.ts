import { Component, OnInit } from "@angular/core";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { DetailDialogComponent } from "../win-dialog-detail/detailDialog.component";
import { FcmService } from "@flashvpn-io/web-core";

@Component({
  selector: "app-winDialog",
  templateUrl: "./winDialog.component.html",
  styleUrls: ["./winDialog.component.css"],
})
export class WinDialogComponent implements OnInit {
  public loading = false;

  constructor(
    public dialog: MatDialog,
    public dialogRef: MatDialogRef<WinDialogComponent>,
    public dialogRefDetail: MatDialogRef<DetailDialogComponent>,
    public fcmService: FcmService
  ) { }

  close() {
    this.dialogRef.close();
  }

  async handleReadAll() {
    this.loading = true;
    try {
      await this.fcmService.handleReadAll();
    } finally {
      this.loading = false;
    }
  }

  async methodOnChange(e) {
    this.loading = true;
    try {
      await this.fcmService.methodOnChange(e);
    } finally {
      this.loading = false;
    }
  }

  async handleSwitch(info) {
    await this.fcmService.handleSwitch(info);
    this.dialog.open(DetailDialogComponent, {
      data: info,
    });
  }

  ngOnInit() {
    this.loading = true;
    try {
      // 获取所有消息，不再使用分页
      this.fcmService.getFcm().finally(() => {
        this.loading = false;
      });
    } catch (error) {
      this.loading = false;
      console.error('Failed to load messages:', error);
    }
  }
}
