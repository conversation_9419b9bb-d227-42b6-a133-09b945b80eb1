<div class="win-dialog-box">
    <div mat-dialog-content>
        <!-- 消息中心 -->
        <div class="inbox-message-content">
            <div class="content-title">消息中心</div>
            <div class="filter">
                <div class="filter-left">
                    <div class="message-checked">
                        <mat-checkbox class="example-margin" [checked]="fcmService.checked"
                            (change)="methodOnChange($event.checked)">
                            仅显示未读
                        </mat-checkbox>
                    </div>
                </div>
                <div class="filter-right" (click)="handleReadAll()">全部已读</div>
            </div>
            <div style="width:100%;position:absolute;text-align: center;" *ngIf="loading">
                <mat-spinner style="margin:40px auto;" [strokeWidth]="3" [diameter]="60"></mat-spinner>
            </div>
            <div class="ul-wrapper">
                <div [ngClass]="{'message-item':true,'checkedMessage':message.readUserIds.includes(fcmService.currentUserId)}"
                    *ngFor="let message of fcmService.messageList" (click)="handleSwitch(message)">
                    <p class="message-item-title">{{message.title}}</p>
                    <div>{{message.createdAt}}</div>
                    <p class="message-item-content" [innerHTML]="message.content"></p>
                </div>
                <p *ngIf="fcmService.messageList.length==0&&!loading"
                    style="padding:30px 0;text-align: center;width:100%;color:#A0AEC0;">
                    暂无数据
                </p>
                <!-- 分页功能已移除，API现在一次性返回所有消息 -->
            </div>
        </div>
        <!-- 消息详情 -->
    </div>
    <div mat-dialog-actions class="dialog-button">
        <button mat-raised-button style="margin-right: 20px;" mat-button (click)="close()">关闭</button>
        <!-- <button mat-raised-button color="warn" (click)="logout()">确认</button> -->
    </div>
</div>