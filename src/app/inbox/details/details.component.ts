

import {Component, OnInit, Pipe, PipeTransform} from '@angular/core';
import { Location } from '@angular/common';
import {HttpClient} from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-details',
  templateUrl: './details.component.html',
  styleUrls: ['./details.component.css']
})
export class DetailsComponent implements OnInit {
  public info: any;
  constructor(
    private location: Location,
    private route: ActivatedRoute,
  ) { }

  goBack() {
    this.location.back();
  }
  ngOnInit() {
    this.route.queryParams.subscribe(queryParams => {
      this.info = JSON.parse(queryParams.info);
    });
  }

}
