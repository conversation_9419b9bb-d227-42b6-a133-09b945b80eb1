<div id="inbox-message">
    <div class="header-nav" (click)="goBack()" fxLayoutGap="10px" fxLayoutAlign="start center">
        <svg width="8" height="15" viewBox="0 0 8 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M8 2.01325L6.8 0.833252L1.19333 6.35992L0 7.53325L1.2 8.71992L6.73333 14.1666L7.92 12.9933L2.39333 7.54659L8 2.01325Z"
                fill="black" />
        </svg>

    </div>
    <div class="inbox-message-content">
        <div class="content-title">消息中心</div>
        <div class="filter">
            <div class="filter-left">
                <mat-checkbox class="message-checked" [checked]="fcmService.checked"
                    (change)="methodOnChange($event.checked)">仅显示未读</mat-checkbox>
            </div>
            <div class="filter-right" (click)="handleReadAll()">全部已读</div>
        </div>
        <div style="width:100%;text-align: center;" *ngIf="loading">
            <mat-spinner style="margin:40px auto;" [strokeWidth]="3" [diameter]="60"></mat-spinner>
        </div>
        <div class="ul-wrapper">
            <div [ngClass]="{'message-item':true,'checkedMessage':message.readUserIds.includes(fcmService.currentUserId)}"
                *ngFor="let message of fcmService.messageList" (click)="handleSwitch(message)">
                <p class="message-item-title">{{message.title}}</p>
                <div>{{message.createdAt}}</div>
                <p class="message-item-content" [innerHTML]="message.content"></p>
            </div>
            <p *ngIf="fcmService.messageList.length==0&&!loading"
                style="padding:30px 0;text-align: center;width:100%;color:#A0AEC0;">暂无数据</p>

            <!-- 分页功能已移除，API现在一次性返回所有消息 -->
        </div>

    </div>
</div>