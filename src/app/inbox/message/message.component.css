#inbox-message {
    position: relative;
    width: 100%;
    height: 100%;
}

#inbox-message .header-nav {
    position: absolute;
    padding: 15px;
    left: -10px;
    top: 0px;
    cursor: pointer;
}

#inbox-message .header-nav svg {
    width: 14px;
    height: 24px;
}

.content-title {
    font-size: 20px;
    width: 100%;
    text-align: center;
    padding-top: 30px;
}

.message-checked /deep/ .mat-checkbox-frame {
    border-radius: 50% !important;
}

.message-checked /deep/ .mat-checkbox-background {
    border-radius: 50% !important;
}

#inbox-message .filter {
    display: flex;
    font-size: 14px;
    color: #ff5e5e;
    justify-content: space-between;
    padding: 26px 4px 12px 4px;
}

.filter-left {
    display: flex;
    align-items: center;
}

.filter-left {
    cursor: pointer;
}

.filter-right {
    cursor: pointer;
}

.ul-wrapper {
    position: fixed;
    width: 100%;
    top: 110px;
    padding: 0px 24px 10px 24px;
    left: 0;
    z-index: 10;
    height: calc(100vh - 120px);
    overflow-y: scroll;
    box-sizing: border-box;
}

.message-item {
    padding: 6px 20px 10px 20px;
    color: #A0AEC0;
    box-shadow: 0px 2px 10px rgba(255, 94, 94, 0.1);
    margin-top: 14px;
    cursor: pointer;
    border-left: 3px solid #ff5e5e;
}

.message-item-content {
    width: 100%;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    box-orient: vertical;
    -webkit-line-clamp: 2;
    /* 这里是超出几行省略 */
    line-clamp: 2;
    /* 这里是超出几行省略 */
    overflow: hidden;
}

.message-item p {
    line-height: normal;
    padding: 0;
    margin: 0;
    color: #000;
    font-size: 14px;
    padding-top: 6px;
}

.message-item .message-item-title {
    font-weight: 600;
    padding-bottom: 4px;
}

.message-item.checkedMessage {
    border-left: 3px solid #A0AEC0;
}

.checkedMessage p {
    color: #A0AEC0;
}
