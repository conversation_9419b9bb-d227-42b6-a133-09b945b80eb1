import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { FcmService } from "@flashvpn-io/web-core";

@Component({
  selector: "app-message",
  templateUrl: "./message.component.html",
  styleUrls: ["./message.component.css"],
})
export class MessageComponent implements OnInit {
  public loading = false;

  constructor(
    private router: Router,
    public fcmService: FcmService
  ) { }

  goBack() {
    this.router.navigate(["/inbox"]);
  }

  async handleReadAll() {
    this.loading = true;
    try {
      await this.fcmService.handleReadAll();
    } finally {
      this.loading = false;
    }
  }

  async methodOnChange(e) {
    this.loading = true;
    try {
      await this.fcmService.methodOnChange(e);
    } finally {
      this.loading = false;
    }
  }

  async handleSwitch(info) {
    await this.fcmService.handleSwitch(info);
    this.router.navigate(["/inbox/detail"], {
      queryParams: { info: JSON.stringify(info) },
    });
  }

  ngOnInit() {
    this.loading = true;
    try {
      // 获取所有消息，不再使用分页
      this.fcmService.getFcm().finally(() => {
        this.loading = false;
      });
    } catch (error) {
      this.loading = false;
      console.error('Failed to load messages:', error);
    }
  }
}
