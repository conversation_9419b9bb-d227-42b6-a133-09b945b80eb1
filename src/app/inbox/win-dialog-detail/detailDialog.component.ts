
import {Component, OnInit, Pipe, PipeTransform, Inject} from '@angular/core';
import { Location } from '@angular/common';
import {HttpClient} from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import {MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-detailDialog',
  templateUrl: './detailDialog.component.html',
  styleUrls: ['./detailDialog.component.css']
})
export class DetailDialogComponent implements OnInit {
  public checked = true;
  public showMessageList = true;
  constructor(
    private location: Location,
    private route: ActivatedRoute,
    public dialog: MatDialog,
    public dialogRef: MatDialogRef<DetailDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data
  ) {
  }

  goBack() {
    this.dialogRef.close();
  }
  close() {
    this.dialogRef.close();
  }
  ngOnInit() {
    this.route.queryParams.subscribe(queryParams => {
      console.log('queryParams==>', queryParams);
    });
  }

}
