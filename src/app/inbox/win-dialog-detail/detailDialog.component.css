.win-dialog-box {
    position: relative;
    width: 500px;
    font-size: 14px;
    height: 70vh;
}

.win-dialog-box .header-nav {
    position: absolute;
    left: 0px;
    top: 0px;
    cursor: pointer;
}

.win-dialog-box .header-nav svg {
    width: 14px;
    height: 24px;
}


/* 消息列表 */

.inbox-message-content .message-checked {
    display: flex;
}

.inbox-message-content .checkbox-msg {
    border-radius: 50%;
}

.inbox-message-content .content-title {
    text-align: center;
    font-size: 20px;
    font-weight: 600;
}

.inbox-message-content .filter {
    display: flex;
    font-size: 14px;
    color: #ff5e5e;
    justify-content: space-between;
    padding: 20px 4px 12px 4px;
}

.inbox-message-content .filter-left {
    display: flex;
    align-items: center;
}

.inbox-message-content .filter-left {
    cursor: pointer;
}

.inbox-message-content .filter-right {
    cursor: pointer;
}

.inbox-message-content .message-item {
    padding: 6px 20px 10px 20px;
    color: #A0AEC0;
    box-shadow: 0px 2px 10px rgba(255, 94, 94, 0.1);
    margin-top: 14px;
    cursor: pointer;
    border-left: 3px solid #ff5e5e;
}

.inbox-message-content .message-item p {
    line-height: normal;
    padding: 0;
    margin: 0;
    color: #000;
    font-size: 14px;
    padding-top: 6px;
}

.inbox-message-content .message-item .message-item-title {
    font-weight: 700;
    padding-bottom: 4px;
}

.inbox-message-content .message-item.checkedMessage {
    border-left: 3px solid #A0AEC0;
}

.inbox-message-content .checkedMessage p {
    color: #A0AEC0;
}

.dialog-button {
    justify-content: end;
    text-align: right;
    padding-top: calc(70vh - 58px);
}


/* 消息详情 */

.inbox-message-detail {
    width: 100%;
    position: absolute;
    top: 75px;
    padding: 0px 4px 15px 4px;
    left: 0;
    z-index: 10;
    height: calc(70vh - 125px);
    overflow-y: scroll;
}

.inbox-message-detail {
    color: #A0AEC0;
}

.inbox-message-detail p {
    line-height: normal;
    padding: 0;
    margin: 0;
    color: #000;
    font-size: 14px;
    padding-top: 6px;
    line-height: 20px;
}

.inbox-message-detail .line {
    height: 1px;
    width: 100%;
    background: #555;
    margin: 15px 0;
}

.inbox-message-detail .message-item-title {
    font-weight: 600;
    padding-bottom: 4px;
}
