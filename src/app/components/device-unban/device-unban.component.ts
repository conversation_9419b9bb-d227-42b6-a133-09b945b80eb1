import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { DeviceService } from '@flashvpn-io/web-core';
import { interval, Subscription } from 'rxjs';
import { finalize, take } from 'rxjs/operators';

@Component({
    selector: 'app-device-unban',
    templateUrl: './device-unban.component.html',
    styleUrls: ['./device-unban.component.scss']
})
export class DeviceUnbanComponent implements OnInit, OnDestroy {
    deviceId: number;
    email: string = '<EMAIL>'; // This should be retrieved from user service
    verificationCode = new FormControl('', [Validators.required]);
    isLoading = false;
    countdown = 60;
    countdownActive = false;
    private subscriptions = new Subscription();

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private deviceService: DeviceService
    ) { }

    ngOnInit(): void {
        this.deviceId = +this.route.snapshot.paramMap.get('id');
        this.requestVerificationCode();
    }

    ngOnDestroy(): void {
        this.subscriptions.unsubscribe();
    }

    // Public navigation method
    navigateToServices(): void {
        this.router.navigate(['/myservices']);
    }

    requestVerificationCode(): void {
        this.isLoading = true;
        this.subscriptions.add(
            this.deviceService.requestVerificationCode().pipe(
                finalize(() => {
                    this.isLoading = false;
                    this.startCountdown();
                })
            ).subscribe(
                () => {
                    // Success handling if needed
                },
                (error) => {
                    console.error('Error requesting verification code:', error);
                }
            )
        );
    }

    startCountdown(): void {
        this.countdown = 60;
        this.countdownActive = true;

        const timer$ = interval(1000).pipe(
            take(60)
        );

        this.subscriptions.add(
            timer$.subscribe(
                (count) => {
                    this.countdown = 60 - (count + 1);
                    if (this.countdown === 0) {
                        this.countdownActive = false;
                    }
                }
            )
        );
    }

    resendCode(): void {
        if (!this.countdownActive) {
            this.requestVerificationCode();
        }
    }

    unbanDevice(): void {
        if (this.verificationCode.invalid) {
            this.verificationCode.markAsTouched();
            return;
        }

        this.isLoading = true;
        this.subscriptions.add(
            this.deviceService.unbanDevice(this.deviceId, this.verificationCode.value).pipe(
                finalize(() => this.isLoading = false)
            ).subscribe(
                (device) => {
                    // Navigate back to device list or show success message
                    this.router.navigate(['/myservices']);
                },
                (error) => {
                    console.error('Error unbanning device:', error);
                    // Handle error
                }
            )
        );
    }
} 