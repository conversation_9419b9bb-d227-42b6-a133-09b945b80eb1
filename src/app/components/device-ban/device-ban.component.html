<div class="device-ban-container">
    <!-- PC navigation header (for desktop) -->
    <div class="navigation-header">
        <div class="page-title">禁用设备</div>
        <div class="back-link" (click)="navigateToServices()">
            <div class="back-icon">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11.25 4.5L6.75 9L11.25 13.5" stroke="black" stroke-width="1.5" stroke-linecap="round"
                        stroke-linejoin="round" />
                </svg>
            </div>
            <span>返回上一页</span>
        </div>
    </div>

    <!-- Main content -->
    <div class="content">
        <div class="verification-info">
            <p>我们已经向您的电子邮件发送了验证码，请在此处输入正确的验证码。</p>
            <p>当前账号: {{ email }}</p>
        </div>

        <!-- Verification code input -->
        <div class="verification-input">
            <div class="input-container">
                <input [formControl]="verificationCode" type="text" placeholder="请输入验证码">
                <span class="resend" [class.disabled]="countdownActive" (click)="resendCode()">
                    重新发送{{ countdownActive ? '(' + countdown + ')' : '' }}
                </span>
            </div>
        </div>

        <!-- Ban button -->
        <button class="ban-button" [disabled]="isLoading || verificationCode.invalid" (click)="banDevice()">
            验证并禁用设备
        </button>
    </div>
</div>