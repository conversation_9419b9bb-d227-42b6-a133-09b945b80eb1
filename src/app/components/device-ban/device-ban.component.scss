.device-ban-container {
  max-width: 1140px;
  margin: 0 auto;
  padding: 0 16px;
}

.navigation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 0;
  
  .page-title {
    font-family: 'PingFang SC', sans-serif;
    font-weight: 600;
    font-size: 16px;
    color: #000000;
  }
  
  .back-link {
    display: flex;
    align-items: center;
    cursor: pointer;
    border-bottom: 1px solid #E6E6E6;
    padding-bottom: 4px;
    
    .back-icon {
      margin-right: 8px;
    }
    
    span {
      font-family: 'PingFang SC', sans-serif;
      font-size: 14px;
      color: #14152C;
    }
  }
}

.content {
  max-width: 1044px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.verification-info {
  p {
    font-family: 'Libre Franklin', sans-serif;
    font-size: 14px;
    line-height: 1.2857;
    color: #808080;
    margin: 0 0 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.verification-input {
  .input-container {
    background-color: #F6F6F6;
    border-radius: 2px;
    display: flex;
    align-items: center;
    padding: 10px;
    max-width: 326px;
    height: 56px;
    
    input {
      flex: 1;
      border: none;
      background: transparent;
      font-family: 'Roboto', sans-serif;
      font-size: 16px;
      line-height: 1.5;
      color: rgba(0, 0, 0, 0.87);
      &:focus {
        outline: none;
      }
    }
    
    .resend {
      font-family: 'PingFang HK', sans-serif;
      font-size: 14px;
      line-height: 1.4;
      color: #FF5E5E;
      cursor: pointer;
      white-space: nowrap;
      
      &.disabled {
        opacity: 0.7;
        cursor: default;
      }
    }
  }
}

.ban-button {
  background-color: #FF5E5E;
  color: #FFFFFF;
  border: none;
  border-radius: 2px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 20px;
  line-height: 1.4;
  padding: 10px 24px;
  max-width: 326px;
  height: 48px;
  cursor: pointer;
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

// Mobile styles
@media (max-width: 768px) {
  .device-ban-container {
    max-width: 100%;
  }
  
  .navigation-header {
    padding: 24px 0;
    justify-content: flex-start;
    gap: 16px;
    
    .page-title {
      order: 2;
    }
    
    .back-link {
      order: 1;
      border-bottom: none;
    }
  }
  
  .content {
    width: 100%;
  }
  
  .verification-input .input-container {
    width: 100%;
    max-width: 100%;
  }
  
  .ban-button {
    width: 100%;
    max-width: 100%;
  }
} 