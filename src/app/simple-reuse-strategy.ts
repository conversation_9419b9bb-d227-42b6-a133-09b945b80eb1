import { ActivatedRouteSnapshot, DetachedRouteHandle, Route, RouteReuseStrategy } from '@angular/router';
import * as _ from 'lodash';
import { Directive } from '@angular/core';
import {GlobalSubscriptionService} from './global-subscription.service';

@Directive()
export class SimpleReuseStrategy implements RouteReuseStrategy {
  private cacheRouters: any = new Map<string, DetachedRouteHandle>();

  constructor(
    private $globalSub: GlobalSubscriptionService
  ) {

  }
  // 相同路由是否复用(路由进入触发)
  shouldReuseRoute(future: ActivatedRouteSnapshot, curr: ActivatedRouteSnapshot): boolean {
    // console.log('shouldReuseRoute', this.getFullRouteURL(future), this.getFullRouteURL(curr), future, curr, future.routeConfig === curr.routeConfig && JSON.stringify(future.params) === JSON.stringify(curr.params));
    return future.routeConfig === curr.routeConfig && JSON.stringify(future.params) === JSON.stringify(curr.params);
  }

  // 是否允许复用路由
  shouldDetach(route: ActivatedRouteSnapshot): boolean {
    // console.log('shouldDetach', this.getFullRouteURL(route), route);
    return Boolean(route.data.keepalive);
  }

  // 存入路由(路由离开出发)
  store(route: ActivatedRouteSnapshot, handle: DetachedRouteHandle): void {
    // console.log('store', this.getFullRouteURL(route), route, handle);
    const url = this.getFullRouteURL(route);
    this.cacheRouters.set(url, handle);
  }

  // 是否允许还原路由
  shouldAttach(route: ActivatedRouteSnapshot): boolean {
    const url = this.getFullRouteURL(route);
    const routeIsReuse = _.cloneDeep(this.$globalSub.$routeIsReuse.value);
    setTimeout(() => {
      this.$globalSub.$routeIsReuse.next(null);
    });
    return this.cacheRouters.has(url) && this.cacheRouters.has(url) && url !== routeIsReuse?.field && !routeIsReuse?.value;
  }

  // 获取存储路由
  retrieve(route: ActivatedRouteSnapshot): DetachedRouteHandle | any {
    const url = this.getFullRouteURL(route);
    // console.log('retrieve', this.getFullRouteURL(route), route, this.cacheRouters.get(url));
    const routeIsReuse = _.cloneDeep(this.$globalSub.$routeIsReuse.value);
    setTimeout(() => {
      this.$globalSub.$routeIsReuse.next(null);
    });
    if (Boolean(route.data.keepalive) && this.cacheRouters.has(url) && url !== routeIsReuse?.field && !routeIsReuse?.value) {
      return this.cacheRouters.get(url);
    } else {
      return false;
    }
  }

  // 获取完整路由路径
  private getFullRouteURL(route: ActivatedRouteSnapshot): string {
    const { pathFromRoot } = route;
    let fullRouteUrlPath: string[] = [];
    pathFromRoot.forEach((item: ActivatedRouteSnapshot) => {
      fullRouteUrlPath = fullRouteUrlPath.concat(this.getRouteUrlPath(item));
    });
    return `/${fullRouteUrlPath.join('/')}`;
  }
  private getRouteUrlPath(route: ActivatedRouteSnapshot) {
    return route.url.map(urlSegment => urlSegment.path);
  }
}
