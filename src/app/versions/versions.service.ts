import { Injectable } from '@angular/core';
import { Observable, ReplaySubject } from "rxjs";
import { APIManager } from "@flashvpn-io/web-core";

@Injectable({
  providedIn: 'root'
})
export class VersionsService {

  private releases = {};
  private subjects = {};
  private subjectsSecond = {};
  constructor(
    private apiManager: APIManager
  ) { }

  latestRelease(platform: string): Observable<AppRelease> {
    let subject: ReplaySubject<AppRelease>;
    if (!!this.subjects[platform]) {
      subject = this.subjects[platform];
    } else {
      subject = new ReplaySubject<AppRelease>(1);
      this.subjects[platform] = subject;
    }

    this.apiManager.getAppLinkByAppCenter(platform)
      .subscribe(value => {
        subject.next(value as AppRelease);
      }, error => console.log(`failed to get release for ${platform}`));

    return subject;
  }

  latestReleaseSecond(platform: string): Observable<AppRelease> {
    let subject: ReplaySubject<AppRelease>;
    if (!!this.subjectsSecond[platform]) {
      subject = this.subjectsSecond[platform];
    } else {
      subject = new ReplaySubject<AppRelease>(1);
      this.subjectsSecond[platform] = subject;
    }

    this.apiManager.getAppLinkByGitHub(platform)
        .subscribe(value => {
          subject.next(value as AppRelease);
        }, error => console.log(`failed to get release for ${platform}`));

    return subject;
  }


}

export interface AppRelease {

  id: string;
  version: string;
  short_version: string;
  app_display_name: string;
  uploaded_at: string;
  download_url: string;
  release_notes: string;
}
