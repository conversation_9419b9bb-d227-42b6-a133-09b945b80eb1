import { Component, OnInit } from '@angular/core';
import {ActivatedRoute, ParamMap, Router} from '@angular/router';
import { AppService } from 'src/app/app.service';

@Component({
  selector: 'app-guide',
  templateUrl: './guide.component.html',
  styleUrls: ['./guide.component.css']
})
export class GuideComponent implements OnInit {
  public url: string;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    public appService: AppService
  ) { }

  ngOnInit() {
    this.route.queryParamMap.subscribe(
      async (params) => {
        this.url =  params.get('url');
      }
    );
  }
}
