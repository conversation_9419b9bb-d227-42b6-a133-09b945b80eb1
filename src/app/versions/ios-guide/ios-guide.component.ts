import { Component, OnInit } from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {environment} from '../../../environments/environment';
import { AppService } from 'src/app/app.service';

@Component({
  selector: 'app-ios-guide',
  templateUrl: './ios-guide.component.html',
  styleUrls: ['./ios-guide.component.css']
})
export class IosGuideComponent implements OnInit {

  public url: string;
  public clashUrl: string;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    public appService: AppService
  ) { }

  ngOnInit() {
    this.route.queryParamMap.subscribe(
      async (params) => {
        this.url =  `${params.get('url')}?agent=none`;
        this.clashUrl = `${params.get('url')}?agent=clash-fallback`;
      }
    );
  }

}
