<div>
  <h2 i18n>Welcome back</h2>
  <h4 i18n>We detected that you are redirected from our own app</h4>
  <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5px" *ngFor="let app of apps" mat-menu-item>
    <mat-icon  [svgIcon]="app.toLowerCase()"></mat-icon>
    <a *ngIf="app !== 'chrome'" class="action-button" [href]="this.sanitizer.bypassSecurityTrustUrl('flashvpn://services?token=' + this.userService.user?.token + '&' + 'email=' + userService.user?.email)" i18n>Open in {{app}}</a>
    <a *ngIf="app === 'chrome'" target="_blank" class="action-button" (click)="this.appService.loginToChrome()" i18n>Open in {{app}}</a>
  </div>
  <app-divider></app-divider>
  <a class="action-button" routerLink="/services" i18n>Go to services</a>
</div>

