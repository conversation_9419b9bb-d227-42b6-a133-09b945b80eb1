import { Component, OnInit } from "@angular/core";
import { UsersService } from "@flashvpn-io/web-core";
import { DomSanitizer } from "@angular/platform-browser";
import { AppService } from "../../app.service";

@Component({
  selector: "app-redirect",
  templateUrl: "./redirect.component.html",
  styleUrls: ["./redirect.component.css"],
})
export class RedirectComponent implements OnInit {
  public apps: string[];
  public isFromChrome = false;

  constructor(public userService: UsersService, public sanitizer: DomSanitizer, public appService: AppService) {}

  async ngOnInit() {
    this.apps = await this.appService.isFromApp();
  }
}
