import { Component, OnInit } from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';

@Component({
  selector: 'app-mac-guide',
  templateUrl: './mac-guide.component.html',
  styleUrls: ['./mac-guide.component.css']
})
export class MacGuideComponent implements OnInit {
  public url: string;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
  ) { }

  ngOnInit() {
    this.route.queryParamMap.subscribe(
      async (params) => {
        this.url =  params.get('url');
      }
    );
  }

}
