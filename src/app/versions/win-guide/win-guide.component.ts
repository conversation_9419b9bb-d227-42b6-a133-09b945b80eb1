import { Component, OnInit } from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';

@Component({
  selector: 'app-win-guide',
  templateUrl: './win-guide.component.html',
  styleUrls: ['./win-guide.component.css']
})
export class WinGuideComponent implements OnInit {
  public url: string;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
  ) { }

  ngOnInit() {
    this.route.queryParamMap.subscribe(
      async (params) => {
        this.url =  params.get('url');
      }
    );
  }


}
