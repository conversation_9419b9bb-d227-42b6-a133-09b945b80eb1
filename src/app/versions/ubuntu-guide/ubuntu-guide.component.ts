import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-ubuntu-guide',
  templateUrl: './ubuntu-guide.component.html',
  styleUrls: ['./ubuntu-guide.component.css']
})
export class UbuntuGuideComponent implements OnInit {
  public url: string;

  constructor(
    private route: ActivatedRoute,
  ) { }

  ngOnInit(): void {
    this.route.queryParamMap.subscribe(
      async (params) => {
        this.url =  params.get('url');
      }
    );
  }

}
