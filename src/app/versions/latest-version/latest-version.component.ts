import { Component, OnInit } from '@angular/core';
import { SwiperConfigInterface } from 'ngx-swiper-wrapper';

@Component({
  selector: 'app-latest-version',
  templateUrl: './latest-version.component.html',
  styleUrls: ['./latest-version.component.css']
})
export class LatestVersionComponent implements OnInit {

  // public platforms = ['android', 'chrome', 'macos', 'windows'];
  public platforms = ['android', 'macos', 'windows', 'chrome', 'macos-arm'];
  public config: SwiperConfigInterface;
  constructor() {

    this.config = {
      direction: 'horizontal', // 开启鼠标的抓手状态
      // grabCursor: true,// 被选中的滑块居中，默认居左
      // centeredSlides: true,
      loop: true,
      slidesPerView: 'auto', //
      loopedSlides: 8,
      autoplay: true,
      speed: 1000, // 切换效果为 coverflow//
      effect: 'coverflow', // coverflow 配置
      pagination: {
        el: '.swiper-pagination',
        clickable: true, // 开启分页按钮点击分页
      },
    };
   }
  ngOnInit() {
  }

  hrefLink(url) {
    window.open(url, '_blank');
  } 

}
