<div class="apps-download">
  <div class="topArea">
    <div class="topArea-left">
      <div class="left-header">
        <img class="left-header-img" src="assets/images/apps/flash.svg" alt="" />
        <div class="header-content">
          <div class="header-content-top">Flash｜您唯一需要的加速器</div>
          <div class="header-content-bottom">极速体验 · 安全保障 · 多平台共享</div>
        </div>
      </div>
      <div class="download-client">
        <div class="title-h2">下载Flash 客户端</div>
        <p class="title-p">Flash VPN 为您提供覆盖不同设备的客户端，请您安装客户端，即可一键连接</p>
        <!-- 移动端轮播图 -->
        <!-- <div class="swiper-mobile">
          <div class="swiper-container" [swiper]='config'>
            <div class="swiper-wrapper">
              <div class="swiper-slide">
                <img style="width:100%" src="assets/images/apps/swiperMb1.png" alt="" />
              </div>
              <div class="swiper-slide">
                <img style="width:100%" src="assets/images/apps/swiperMb2.png" alt="" />
              </div>
            </div>
            <div class="swiper-pagination pagination"></div>
          </div>
        </div> -->
        <app-app-version></app-app-version>
      </div>
    </div>
    <!-- pc轮播图 -->
    <div class="topArea-right">
      <!-- <div class="swiper-container" [swiper]='config'>
        <div class="swiper-wrapper">
          <div class="swiper-slide">
            <img src="assets/images/apps/swiperPc1.png" alt="" />
          </div>
          <div class="swiper-slide">
            <img src="assets/images/apps/swiperPc2.png" alt="" />
          </div>
        </div>
        <div class="swiper-pagination pagination"></div>
      </div> -->
    </div>
  </div>
</div>
<style type="text/css">
  .topArea {
    display: flex;
    justify-content: space-between;
  }

  .left-header {
    display: flex;
    align-items: center;
  }

  .header-content {
    padding-left: 10px;
  }

  .header-content-top {
    font-size: 20px;
    color: #FF5E5E;
    padding-bottom: 8px;
  }

  .header-content-bottom {
    font-size: 14px;
    color: #000;
    font-weight: 600;
  }

  .left-header-img {
    height: 56px;
    width: 56px;
  }

  .topArea-left {
    flex: 1;
  }

  .download-client {
    padding-top: 32px;
  }

  .download-client .tabs-img {
    width: 24px;
    height: 24px;
  }

  .download-client .tabs-span {
    padding-left: 6px;
  }

  .download-client /deep/ .mat-tab-label {
    opacity: 1 !important;
    min-width: 140px;
  }

  .tabs-content .content-btn {
    display: block;
    height: 38px;
    padding: 0 20px;
    border-radius: 3px;
    background: #FF5E5E;
    color: #fff;
    font-size: 14px;
    max-width: 100px;
    line-height: 38px;
    text-align: center;
    box-sizing: border-box;
    cursor: pointer;
    margin: 6px 0;
  }

  .tabs-content .goole-btn {
    display: flex;
    align-items: center;
    height: 38px;
    padding: 0 10px;
    border-radius: 3px;
    border: 1px solid #FF5E5E;
    color: #FF5E5E;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
  }

  .tabs-content .goole-btn span {
    padding-left: 5px;
  }

  .title-h2 {
    font-size: 22px;
    font-weight: 500;
  }

  .title-p {
    color: #505050;
    font-size: 14px;
    line-height: 20px;
  }

  .checked-list {
    margin-top: 14px;
  }

  .checked-list-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    padding-bottom: 6px;
  }

  .checked-list-item span {
    padding: 0 10px 0 8px;
  }

  .checked-list-item .discount {
    height: 14px;
    padding: 0 5px;
    font-size: 12px;
    line-height: 14px;
    background: #FF5E5E;
    color: #fff;
    border-radius: 5px 0 5px 0;
  }

  .topArea-right {
    width: 225px;
    margin-left: 100px;
  }

  .topArea-right .swiper-container {
    padding-bottom: 15px;
  }

  .topArea-right .pagination {
    position: absolute;
    left: 0;
    text-align: center;
    bottom: 0px;
    width: 100%;
    z-index: 999;
  }

  .swiper-mobile .pagination {
    position: absolute;
    left: 0;
    text-align: center;
    bottom: 20px;
    width: 100%;
    z-index: 999;
  }

  .pagination /deep/ .swiper-pagination-bullet {
    display: inline-block;
    width: 8px;
    height: 8px;
    opacity: 1;
    border-radius: 10px;
    background: #C4C4C4;
    margin: 0 5px;
    cursor: pointer;
    transition: width 0.3s ease-in-out;
  }

  .pagination /deep/ .swiper-pagination-bullet-active {
    background: #FF5E5E;
  }

  .download-box {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  @media screen and (max-width: 650px) {
    .topArea-right {
      display: none;
    }

    .download-box {
      flex-direction: column;
      align-items: flex-start;
      gap: 6px;
    }
  }

  @media screen and (min-width: 650px) {
    .swiper-mobile {
      display: none;
    }
  }
</style>