import { Component, OnInit } from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {environment} from '../../../environments/environment';
import { AppService } from 'src/app/app.service';

@Component({
  selector: 'app-clash-guide',
  templateUrl: './clash-guide.component.html',
  styleUrls: ['./clash-guide.component.css']
})
export class ClashGuideComponent implements OnInit {

  public url: string;
  public env: any = environment;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    public appService: AppService
  ) { }

  ngOnInit() {
    this.route.queryParamMap.subscribe(
      async (params) => {
        this.url =  `${params.get('url')}?agent=clash-fallback`;
      }
    );
  }
}
