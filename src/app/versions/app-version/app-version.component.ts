/*
 * @Author: jiangnan
 * @Email: <EMAIL>
 * @Date: 2023-02-27 08:35:57
 * @LastEditors: jiangnan
 * @LastEditTime: 2023-03-02 18:02:41
 * @Describle: 描述
 */
import { Component, Input, OnInit } from '@angular/core';
import { HttpBackend, HttpClient } from '@angular/common/http';
import { APIManager, ServiceService } from '@flashvpn-io/web-core';
import { VersionsService } from '../versions.service';
import { SwiperConfigInterface, } from 'ngx-swiper-wrapper'; // 3D 切换效果参数设置
import { ActivatedRoute, Router } from '@angular/router';
import * as moment from 'moment';
import { platform } from 'os';

@Component({
  selector: 'app-app-version',
  templateUrl: './app-version.component.html',
  styleUrls: ['./app-version.component.css'],
})
export class AppVersionComponent implements OnInit {
  private http: HttpClient;
  public platforms: string[];
  @Input() version: string;
  submitted = false;
  public allVersionArr: any;
  public allVersionArrSecond: any;
  public activeDescrible: string;
  public firstTabs: string;
  public deliveryIPs: string[];
  public deliverySubDomains: string[];
  public deliveryDownloadDomains: string[];

  public thirdPartyAppDownloadUrls: any[] = [{
    app: 'clash',
    platforms: [{
      platform: 'ubuntu',
      name: 'Debian',
      url: '/apps/clash-verge-linux-i386.deb'
    }]
  }, {
    app: 'clash-x32',
    platforms: [{
      platform: 'windows',
      name: 'Windows 32-bit',
      url: '/apps/clash-verge-windows-x86-setup.exe'
    }]
  }, {
    app: 'clash-x64',
    platforms: [{
      platform: 'windows',
      name: 'Windows 64-bit',
      url: '/apps/clash-verge-windows-x64-setup.exe'
    }]
  }, {
    app: 'clash-intel',
    platforms: [{
      platform: 'macos',
      name: 'macOS intel 芯片',
      url: '/apps/clash-verge-macos-x64.dmg'
    }]
  }, {
    app: 'clash-m',
    platforms: [{
      platform: 'macos',
      name: 'macOS M 芯片',
      url: '/apps/clash-verge-macos-aarch64.dmg'
    }]
  }, {
    app: 'terraka',
    platforms: [{
      platform: 'android',
      name: 'Android',
      url: 'https://terraka.app/apps'
    }, {
      platform: 'ios',
      name: 'iOS',
      url: 'https://terraka.app/apps'
    }, {
      platform: 'macos',
      name: 'macOS',
      url: 'https://terraka.app/apps'
    }, {
      platform: 'windows',
      name: 'Windows',
      url: 'https://terraka.app/apps'
    }, {
      platform: 'ubuntu',
      name: 'Ubuntu',
      url: 'https://terraka.app/apps'
    }]
  }, {
    app: 'ClashMetaForAndroid',
    platforms: [{
      platform: 'android',
      name: 'Android',
      url: '/apps/ClashMetaForAndroid.apk'
    }]
  }, {
    app: 'edge',
    platforms: [{
      platform: 'edge',
      name: 'Edge',
      url: 'https://microsoftedge.microsoft.com/addons/detail/mffhbbckgfkfginpkjodhikofglppolf'
    }]
  }, {
    app: 'chrome',
    platforms: [{
      platform: 'chrome',
      name: 'Chrome',
      url: 'https://chrome.google.com/webstore/detail/flashvpn/licpgijigioadjpgapodafkgacejebjp'
    }]
  }, {
    app: 'shadowrocket',
    platforms: [{
      platform: 'ios',
      name: 'iOS',
      url: 'https://apps.apple.com/app/shadowrocket/id932747118'
    }]
  }, {
    app: 'stash',
    platforms: [{
      platform: 'ios',
      name: 'iOS',
      url: 'https://apps.apple.com/us/app/stash/id1596063349'
    }]
  }, {
    app: 'quantumult x',
    platforms: [{
      platform: 'ios',
      name: 'iOS',
      url: 'https://apps.apple.com/us/app/quantumult-x/id1443988620'
    }]
  }];
  public subscriptionPath: string;

  selectedIndex = 0;
  constructor(
    private handler: HttpBackend,
    private apiManager: APIManager,
    private versionsService: VersionsService,
    private route: ActivatedRoute,
    private router: Router,
    private serviceService: ServiceService
  ) {
    this.http = new HttpClient(handler);
    this.platforms = ['Android', 'macOS', 'Windows', 'Chrome', 'Edge', 'iOS', 'Ubuntu'];
  }

  rTime(date) {
    const momentDate = moment(date);
    return momentDate.format('YYYY-MM-DD HH:mm:ss');
  }

  redirectToHelpCenter() {
    this.router.navigate(['helps/article/1']);
  }

  hrefLink(url) {
    window.open(url, '_blank');
  }

  OnGetOS() {
    const agent = navigator.userAgent.toLowerCase();
    console.log();
    const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;
    const MAC =
      /macintosh|mac os x/i.test(navigator.userAgent) && !(window as any).MSStream;
    const android = /Android/.test(navigator.userAgent) && !(window as any).MSStream;

    if (agent.indexOf('win32') >= 0 || agent.indexOf('wow32') >= 0) {
      return 'Windows'; // 此处根据需求调整
    }
    if (agent.indexOf('win64') >= 0 || agent.indexOf('wow64') >= 0) {
      return 'Windows'; // 此处根据需求调整
    }
    if (iOS) {
      return 'iOS';
    }
    if (MAC) {
      return 'macOS';
    }
    if (android) {
      return 'Android';
    }
    if (agent.indexOf('ubuntu') >= 0 || agent.indexOf('linux')) {
      return 'Ubuntu';
    }
    return 'unknow';
  }

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      const toPlatform = params.from ?? this.OnGetOS();
      let toSelect = this.platforms.indexOf(toPlatform);
      if (toSelect === -1) {
        toSelect = 0;
      }
      this.selectedIndex = toSelect;
    });

    // 文案获取
    this.submitted = true;
    this.allVersionArr = {};
    this.allVersionArrSecond = {};
    this.platforms.forEach((platform) => {

      platform = platform.toLowerCase();

      this.versionsService.latestRelease(platform).subscribe(
        (value) => {
          const resValue = value as any;
          resValue.uploaded_at_time = this.rTime(resValue.uploaded_at);
          // const keyName =
          //   resValue.app_name && resValue.app_name.replace(/FlashVPN-/g, '');
          this.allVersionArr[platform] = resValue;
          if (platform === 'ios') {
            this.allVersionArr[platform].download_url = 'https://testflight.apple.com/join/359Y3pYZ';
          }
          this.submitted = false;
        },
        (error) => { },
        () => (this.submitted = false)
      );
      this.versionsService.latestReleaseSecond(platform.toLowerCase()).subscribe(
        (value) => {
          // resValue.uploaded_at_time = this.rTime(resValue.uploaded_at);
          // const keyName =
          //     resValue.app_name && resValue.app_name.replace(/FlashVPN-/g, '');
          this.allVersionArrSecond[platform] = value as any;
          this.submitted = false;
        },
        (error) => { },
        () => (this.submitted = false)
      );
    });

    // 活动折扣等
    this.apiManager.walletsRule().subscribe(
      (value) => {
        const arr = [];
        const saveObj = {};
        if (Array.isArray(value)) {
          value.forEach((el, index) => {
            saveObj[index] = el.description;
            arr.push(el.gift);
          });
          const max = Math.max(...arr);
          const objIndex = arr.indexOf(max);
          if (max === 0) { // 代表没有赠送
            this.activeDescrible = '';
          } else {
            this.activeDescrible = saveObj[objIndex] || '';
          }

        }
      },
      (error) => console.log(`failed to get release for ${error}`)
    );

    this.apiManager.getDeliveryDomains().subscribe((value) => {
      this.deliverySubDomains = value.api?.length > 0 ? value.api.slice(0, 3) : [window.location.hostname];
      this.deliveryDownloadDomains = value.app;
    });

    this.apiManager.getDeliveryIPs().subscribe((value) => {
      this.deliveryIPs = value;
    });

    this.serviceService.currentService$.subscribe((service) => {
      if (service) {
        this.subscriptionPath = service['subscriptionUrl'];
      }
    });
  }
}
