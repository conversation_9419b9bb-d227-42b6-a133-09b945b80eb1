@import '~swiper/swiper-bundle.min.css';
@import url("https://fonts.googleapis.com/css?family=Noto+Sans+SC:var(--ch-body-16-500-font-weight),400,var(--ch-body-14-500-font-weight),var(--ch-body-14-400-font-weight),500,700");


.frame {
    --uicolorprimary: rgba(103, 80, 163, 1);
    --flowkitpurple: rgba(123, 97, 255, 1);
    --flowkitcharcoal: rgba(34, 34, 34, 1);
    --flowkitred: rgba(252, 85, 85, 1);
    --flowkitgreen: rgba(41, 204, 106, 1);
    --flowkitblue: rgba(0, 153, 255, 1);
    --flowkitwhite: rgba(255, 255, 255, 1);
    --neutral-800: rgba(44, 44, 44, 1);
    --neutral900-black: rgba(11, 11, 11, 1);
    --neutral-700: rgba(88, 88, 88, 1);
    --primary-red-500: rgba(255, 93, 93, 1);
    ---font-family: "PingFang SC", Helvetica;
    ---font-weight: 400;
    ---font-size: 14px;
    ---letter-spacing: 0px;
    ---line-height: 14px;
    ---font-style: normal;
    --caption-font-family: "PingFang SC", Helvetica;
    --caption-font-weight: 400;
    --caption-font-size: 12px;
    --caption-letter-spacing: 0px;
    --caption-line-height: 100%;
    --caption-font-style: normal;
    --ch-body-14-400-font-family: "Noto Sans SC", Helvetica;
    --ch-body-14-400-font-weight: 400;
    --ch-body-14-400-font-size: 14px;
    --ch-body-14-400-letter-spacing: 0px;
    --ch-body-14-400-line-height: 22px;
    --ch-body-14-400-font-style: normal;
    --ch-body-14-500-font-family: "Noto Sans SC", Helvetica;
    --ch-body-14-500-font-weight: 500;
    --ch-body-14-500-font-size: 14px;
    --ch-body-14-500-letter-spacing: 0px;
    --ch-body-14-500-line-height: 22px;
    --ch-body-14-500-font-style: normal;
    --ch-body-16-500-font-family: "Noto Sans SC", Helvetica;
    --ch-body-16-500-font-weight: 500;
    --ch-body-16-500-font-size: 16px;
    --ch-body-16-500-letter-spacing: 0.16px;
    --ch-body-16-500-line-height: 24px;
    --ch-body-16-500-font-style: normal;
    --card: 0px 0px 10px 0px rgba(255, 94, 94, 0.1);
    --variable-collection-flash: rgba(255, 94, 94, 1);
    --variable-collection-1: rgba(128, 128, 128, 1);
    --variable-collection: rgba(20, 21, 44, 1);
    --variable-collection-2: rgba(153, 153, 153, 1);
}

* {
    -webkit-font-smoothing: antialiased;
    box-sizing: border-box;
}

html,
body {
    margin: 0px;
    height: 100%;
}

/* a blue color as a generic focus style */
button:focus-visible {
    outline: 2px solid #4a90e2 !important;
    outline: -webkit-focus-ring-color auto 5px !important;
}

a {
    text-decoration: none;
}

@font-face {
    font-family: "PingFang SC-Semibold";
    src: url("https://anima-uploads.s3.amazonaws.com/projects/64216d7d4623c1aa040fa514/fonts/pingfangsc-semibold.otf") format("opentype");
}

@font-face {
    font-family: "PingFang SC-Medium";
    src: url("https://anima-uploads.s3.amazonaws.com/projects/64095f281bef4b15eceb2dc2/fonts/pingfang-sc-medium.otf") format("opentype");
}

.frame {
    display: inline-flex;
    height: 407px;
    align-items: flex-start;
    gap: 24px;
    padding: 20px;
    position: relative;
}

.frame .div {
    display: inline-flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 24px;
    position: relative;
    flex: 0 0 auto;
}

.frame .div-2 {
    display: inline-flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: 8px;
    position: relative;
    flex: 0 0 auto;
}

.frame .text-wrapper {
    position: relative;
    width: fit-content;
    margin-top: -1.00px;
    font-family: "Noto Sans SC-Bold", Helvetica;
    font-weight: 700;
    color: var(--neutral-800);
    font-size: 18px;
    letter-spacing: 0;
    line-height: normal;
}

.frame .rectangle {
    position: relative;
    width: 18px;
    height: 3px;
    background-color: var(--primary-red-500);
    border-radius: 999px;
}

.frame .frame-wrapper {
    display: flex;
    flex-direction: column;
    width: 472px;
    height: fit-content;
    align-items: flex-start;
    gap: 32px;
    padding: 0px 0px 32px;
    position: relative;
}

.frame .div-3 {
    display: flex;
    flex-direction: column;
    height: 328px;
    align-items: flex-start;
    position: relative;
    align-self: stretch;
    width: 100%;
    margin-bottom: -14.00px;
    border-radius: 12px;
    border: 1px solid;
    border-color: #0000001a;
}

.frame .div-4 {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px 16px 12px;
    position: relative;
    align-self: stretch;
    width: 100%;
    flex: 0 0 auto;
}

.frame .div-wrapper {
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;
    flex: 1;
    flex-grow: 1;
}

.frame .div-5 {
    display: inline-flex;
    align-items: center;
    gap: 16px;
    position: relative;
    flex: 0 0 auto;
}

.frame .start-icon {
    display: inline-flex;
    align-items: flex-start;
    position: relative;
    flex: 0 0 auto;
}

.frame .img {
    position: relative;
    width: 32px;
    height: 32px;
}

.frame .text-wrapper-2 {
    position: relative;
    width: fit-content;
    font-family: "PingFang SC-Semibold", Helvetica;
    font-weight: 400;
    color: var(--neutral-800);
    font-size: 20px;
    letter-spacing: 0;
    line-height: 14px;
    white-space: nowrap;
}

.frame .div-6 {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    position: relative;
    flex: 0 0 auto;
}

.frame .button {
    all: unset;
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    position: relative;
    flex: 0 0 auto;
    border-radius: 6px;
}

.frame .label {
    position: relative;
    width: fit-content;
    margin-top: -1.00px;
    font-family: "Noto Sans SC-Medium", Helvetica;
    font-weight: 500;
    color: var(--neutral900-black);
    font-size: 14px;
    text-align: center;
    letter-spacing: 0.14px;
    line-height: 24px;
    text-decoration: underline;
    white-space: nowrap;
}

.frame .div-wrapper-2 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 16px 16px 10px;
    position: relative;
    align-self: stretch;
    width: 100%;
    flex: 0 0 auto;
}

.frame .p {
    position: relative;
    flex: 1;
    margin-top: -1.00px;
    font-family: var(--ch-body-14-400-font-family);
    font-weight: var(--ch-body-14-400-font-weight);
    color: var(--neutral-700);
    font-size: var(--ch-body-14-400-font-size);
    letter-spacing: var(--ch-body-14-400-letter-spacing);
    line-height: var(--ch-body-14-400-line-height);
    font-style: var(--ch-body-14-400-font-style);
}

.frame .div-7 {
    display: inline-flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: 5px;
    padding: 5px 16px;
    position: relative;
    flex: 0 0 auto;
}

.frame .text-wrapper-3 {
    width: fit-content;
    font-family: var(--ch-body-14-500-font-family);
    font-weight: var(--ch-body-14-500-font-weight);
    color: var(--neutral-800);
    letter-spacing: var(--ch-body-14-500-letter-spacing);
    line-height: var(--ch-body-14-500-line-height);
    white-space: nowrap;
    position: relative;
    font-size: var(--ch-body-14-500-font-size);
    font-style: var(--ch-body-14-500-font-style);
}

.frame .text-wrapper-4 {
    position: relative;
    width: fit-content;
    margin-top: -1.00px;
    font-family: var(--ch-body-16-500-font-family);
    font-weight: var(--ch-body-16-500-font-weight);
    color: var(--neutral-800);
    font-size: var(--ch-body-16-500-font-size);
    letter-spacing: var(--ch-body-16-500-letter-spacing);
    line-height: var(--ch-body-16-500-line-height);
    white-space: nowrap;
    font-style: var(--ch-body-16-500-font-style);
}

.frame .text-wrapper-5 {
    width: fit-content;
    margin-top: -1.00px;
    font-family: var(--ch-body-14-500-font-family);
    font-weight: var(--ch-body-14-500-font-weight);
    color: var(--neutral-800);
    letter-spacing: var(--ch-body-14-500-letter-spacing);
    line-height: var(--ch-body-14-500-line-height);
    white-space: nowrap;
    position: relative;
    font-size: var(--ch-body-14-500-font-size);
    font-style: var(--ch-body-14-500-font-style);
}

.frame .text-wrapper-6 {
    position: relative;
    width: 169px;
    margin-top: -1.00px;
    font-family: var(--ch-body-14-500-font-family);
    font-weight: var(--ch-body-14-500-font-weight);
    color: var(--neutral-800);
    font-size: var(--ch-body-14-500-font-size);
    letter-spacing: var(--ch-body-14-500-letter-spacing);
    line-height: var(--ch-body-14-500-line-height);
    font-style: var(--ch-body-14-500-font-style);
}

.frame .frame-wrapper-2 {
    display: flex;
    height: 48px;
    align-items: center;
    gap: 8px;
    padding: 5px 16px;
    position: relative;
    align-self: stretch;
    width: 100%;
}

.frame .div-8 {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    position: relative;
    flex: 0 0 auto;
}

.frame .div-wrapper-3 {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 24px;
    position: relative;
    flex: 0 0 auto;
    border-radius: 2px;
    background: linear-gradient(180deg,
            rgb(255, 245.33, 158.31) 0%,
            rgb(255, 225.12, 125.34) 2.6%,
            rgb(255, 193.23, 73.31) 60.42%,
            rgb(255, 190.14, 23.37) 100%);
}

.frame .text-wrapper-7 {
    cursor: pointer;
    position: relative;
    width: fit-content;
    margin-top: -1.00px;
    font-family: "PingFang SC-Medium", Helvetica;
    font-weight: 500;
    color: #000000;
    font-size: 14px;
    letter-spacing: 0;
    line-height: normal;
}

.frame .div-9 {
    display: inline-flex;
    align-items: flex-start;
    gap: 24px;
    position: relative;
    flex: 0 0 auto;
}

.frame .img-2 {
    position: relative;
    width: 42px;
    height: 42px;
    margin-top: -2.00px;
    margin-bottom: -3.00px;
    object-fit: cover;
}

.frame .image {
    position: relative;
    width: 42px;
    height: 42px;
    margin-top: -2.00px;
    margin-bottom: -3.00px;
    margin-right: -4.00px;
    object-fit: cover;
}

.frame .div-10 {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    position: relative;
    align-self: stretch;
    width: 100%;
    flex: 0 0 auto;
    margin-bottom: -10.00px;
    border-radius: 12px;
    border: 1px solid;
    border-color: #0000001a;
}

.frame .element {
    position: relative;
    width: 56px;
    height: 56px;
    margin-top: -2.00px;
    margin-bottom: -6.00px;
    margin-left: -4.00px;
    object-fit: cover;
}

.frame .div-11 {
    display: flex;
    height: 48px;
    align-items: center;
    gap: 8px;
    padding: 16px;
    position: relative;
    align-self: stretch;
    width: 100%;
}

.frame .div-wrapper-4 {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 4px 16px;
    position: relative;
    flex: 0 0 auto;
    margin-top: -8.00px;
    margin-bottom: -8.00px;
    border-radius: 999px;
    border: 1px solid;
    border-color: var(--variable-collection-flash);
}

.frame .text-wrapper-8 {
    width: fit-content;
    margin-top: -1.00px;
    font-family: "Noto Sans SC-Medium", Helvetica;
    font-weight: 500;
    color: var(--variable-collection-flash);
    letter-spacing: 0.14px;
    line-height: 24px;
    white-space: nowrap;
    position: relative;
    font-size: 14px;
}

.frame .div-12 {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 17px;
    padding: 8px 16px 16px;
    position: relative;
    align-self: stretch;
    width: 100%;
    flex: 0 0 auto;
}

.frame .frame-wrapper-3 {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: 12px;
    position: relative;
    align-self: stretch;
    width: 100%;
    flex: 0 0 auto;
}

.frame .div-13 {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    position: relative;
    align-self: stretch;
    width: 100%;
    flex: 0 0 auto;
    border-radius: 4px;
    border: 1px solid;
    border-color: #221c121a;
}

.frame .text-wrapper-9 {
    flex: 1;
    font-family: "Noto Sans SC-Regular", Helvetica;
    font-weight: 400;
    color: #000000;
    letter-spacing: 0;
    line-height: 20px;
    position: relative;
    font-size: 14px;
}

.frame .div-14 {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    position: relative;
    flex: 0 0 auto;
}

.frame .label-2 {
    color: var(--primary-red-500);
    font-size: var(--ch-body-14-500-font-size);
    position: relative;
    width: fit-content;
    margin-top: -1.00px;
    font-family: var(--ch-body-14-500-font-family);
    font-weight: var(--ch-body-14-500-font-weight);
    text-align: center;
    letter-spacing: var(--ch-body-14-500-letter-spacing);
    line-height: var(--ch-body-14-500-line-height);
    white-space: nowrap;
    font-style: var(--ch-body-14-500-font-style);
}

.frame .line {
    position: relative;
    width: 1px;
    height: 20px;
    object-fit: cover;
}

.frame .scan {
    position: relative;
    width: 16px;
    height: 16px;
}

.frame .frame-wrapper-4 {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: 12px;
    position: relative;
    align-self: stretch;
    width: 100%;
    flex: 0 0 auto;
    background-color: #d9d9d980;
}

.frame .label-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 0px 8px;
    position: relative;
    flex: 0 0 auto;
    background-color: var(--variable-collection);
    border-radius: 4px;
}

.frame .label-3 {
    color: var(--variable-collection-1);
    font-size: 12px;
    position: relative;
    width: fit-content;
    margin-top: -1.00px;
    font-family: "Noto Sans SC-Medium", Helvetica;
    font-weight: 500;
    text-align: center;
    letter-spacing: 0;
    line-height: 22px;
    white-space: nowrap;
}

.frame .label-4 {
    color: #7f7f7f;
    font-size: var(--ch-body-14-500-font-size);
    position: relative;
    width: fit-content;
    margin-top: -1.00px;
    font-family: var(--ch-body-14-500-font-family);
    font-weight: var(--ch-body-14-500-font-weight);
    text-align: center;
    letter-spacing: var(--ch-body-14-500-letter-spacing);
    line-height: var(--ch-body-14-500-line-height);
    white-space: nowrap;
    font-style: var(--ch-body-14-500-font-style);
}