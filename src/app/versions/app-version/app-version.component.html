<div class="tabsWrapper">
  <mat-tab-group [(selectedIndex)]="selectedIndex">
    <mat-tab *ngFor="let platform of platforms">

      <ng-template mat-tab-label>
        <img class="tabs-img" src="assets/images/apps/{{platform.toLowerCase()}}.svg" alt="" />
        <span class="tabs-span">{{platform}}</span>
      </ng-template>
      <div class="tabs-content" *ngIf="platform=='Ubuntu'">
        <app-versions-info [allVersionArr]="allVersionArr['ubuntu']" describe="支持 Ubuntu" [platform]="platform"
          [deliveryIPs]="deliveryIPs" [thirdPartyAppList]="['terraka','clash']"
          [thirdPartyAppDownloadUrls]="thirdPartyAppDownloadUrls" [subscriptionPath]="subscriptionPath"
          [deliverySubDomains]="deliverySubDomains"
          [deliveryDownloadDomains]="deliveryDownloadDomains"></app-versions-info>
      </div>
      <div class="tabs-content" *ngIf="platform=='Windows'">
        <app-versions-info [allVersionArr]="allVersionArr['windows']"
          describe="适用于 Windows 7 Service Pack 1、Windows 8.1、Windows 10（版本 1607 以及更高版本）、Windows 11"
          [platform]="platform" [deliveryIPs]="deliveryIPs" [thirdPartyAppList]="['clash-x32','clash-x64', 'terraka']"
          [thirdPartyAppDownloadUrls]="thirdPartyAppDownloadUrls" [subscriptionPath]="subscriptionPath"
          [deliverySubDomains]="deliverySubDomains"
          [deliveryDownloadDomains]="deliveryDownloadDomains"></app-versions-info>
      </div>
      <div class="tabs-content" *ngIf="platform=='macOS'">
        <app-versions-info [allVersionArr]="allVersionArr['macos']"
          describe="支持 macOS 10.15 Catalina、macOS 11 Big Sur、macOS 12 Monterey 和 macOS 13 Ventura。"
          [platform]="platform" [deliveryIPs]="deliveryIPs"
          [thirdPartyAppList]="['clash-intel', 'clash-m', 'terraka', 'stash']"
          [thirdPartyAppDownloadUrls]="thirdPartyAppDownloadUrls" [subscriptionPath]="subscriptionPath"
          [deliverySubDomains]="deliverySubDomains"
          [deliveryDownloadDomains]="deliveryDownloadDomains"></app-versions-info>
      </div>

      <div class="tabs-content" *ngIf="platform=='Android'">
        <app-versions-info [allVersionArr]="allVersionArr['android']"
          describe="适用于 Android 6.0 或更高版本的设备您可以直接下载或通过 Google 应用商店获取" [platform]="platform" [deliveryIPs]="deliveryIPs"
          [thirdPartyAppList]="['terraka']" [thirdPartyAppDownloadUrls]="thirdPartyAppDownloadUrls"
          [subscriptionPath]="subscriptionPath" [deliverySubDomains]="deliverySubDomains"
          [deliveryDownloadDomains]="deliveryDownloadDomains"></app-versions-info>
      </div>

      <div class="tabs-content" *ngIf="platform=='iOS'">
        <app-versions-info [allVersionArr]="allVersionArr['ios']"
          describe="iOS版本当前内测中，您暂时只能通过 Testflight 免费进行下载！您可以免费加入我们的用户内测计划，进行使用！" [platform]="platform"
          [deliveryIPs]="deliveryIPs" [thirdPartyAppList]="['Terraka', 'Stash', 'Shadowrocket', 'Quantumult X']"
          [thirdPartyAppDownloadUrls]="thirdPartyAppDownloadUrls" [subscriptionPath]="subscriptionPath"
          [deliverySubDomains]="deliverySubDomains"
          [deliveryDownloadDomains]="deliveryDownloadDomains"></app-versions-info>
      </div>

      <div class="tabs-content" *ngIf="platform=='Chrome'">
        <app-versions-info [allVersionArr]="allVersionArr['chrome']" describe="您可以直接下载或通过 Chrome 应用商店获取"
          storeUrl="https://chrome.google.com/webstore/detail/flashvpn/licpgijigioadjpgapodafkgacejebjp"
          [platform]="platform" [deliveryIPs]="deliveryIPs" [thirdPartyAppDownloadUrls]="thirdPartyAppDownloadUrls"
          [subscriptionPath]="subscriptionPath" [deliverySubDomains]="deliverySubDomains"
          [deliveryDownloadDomains]="deliveryDownloadDomains"></app-versions-info>
      </div>

      <div class="tabs-content" *ngIf="platform=='Edge'">
        <app-versions-info [allVersionArr]="allVersionArr['edge']" describe="您可以直接下载或通过 Edge 应用商店获取"
          storeUrl="https://microsoftedge.microsoft.com/addons/detail/mffhbbckgfkfginpkjodhikofglppolf"
          [platform]="platform" [deliveryIPs]="deliveryIPs" [thirdPartyAppDownloadUrls]="thirdPartyAppDownloadUrls"
          [subscriptionPath]="subscriptionPath" [deliverySubDomains]="deliverySubDomains"
          [deliveryDownloadDomains]="deliveryDownloadDomains"></app-versions-info>
      </div>

      <!-- <div class="checked-list">
        <div class="checked-list-item">
          <img src="assets/images/apps/vector.svg" alt="" />
          <span>一键连接，便捷操作</span>
        </div>
        <div class="checked-list-item">
          <img src="assets/images/apps/vector.svg" alt="" />
          <span>智能推荐选择节点，超快网速</span>
        </div>
        <div class="checked-list-item">
          <img src="assets/images/apps/vector.svg" alt="" />
          <span>全程安全防护，保障隐私安全</span>
        </div>
        <div class="checked-list-item" *ngIf="activeDescrible">
          <img src="assets/images/apps/vector.svg" alt="" />
          <span>充值折扣</span>
          <div class="discount">{{activeDescrible}}</div>
        </div>
      </div> -->
    </mat-tab>
  </mat-tab-group>
</div>

<style type="text/css">
  .topArea {
    display: flex;
    justify-content: space-between;
  }

  .left-header {
    display: flex;
    align-items: center;
  }

  .header-content {
    padding-left: 10px;
  }

  .header-content-top {
    font-size: 20px;
    color: #FF5E5E;
    padding-bottom: 8px;
  }

  .header-content-bottom {
    font-size: 14px;
    color: #000;
    font-weight: 600;
  }

  .left-header-img {
    height: 56px;
    width: 56px;
  }

  .topArea-left {
    flex: 1;
  }

  .download-client {
    padding-top: 32px;
  }

  .download-client .tabs-img {
    width: 24px;
    height: 24px;
  }

  .download-client .tabs-span {
    padding-left: 6px;
  }

  .tabs-span {
    padding-left: 6px;
  }

  ::ng-deep .mat-tab-label {
    opacity: 1 !important;
    min-width: 140px;
  }

  .tabs-content .content-btn {
    display: block;
    height: 38px;
    padding: 0 20px;
    border-radius: 3px;
    background: #FF5E5E;
    color: #fff;
    font-size: 14px;
    max-width: 100px;
    line-height: 38px;
    text-align: center;
    box-sizing: border-box;
    cursor: pointer;
    margin: 6px 0;
  }

  .tabs-content .goole-btn {
    display: flex;
    align-items: center;
    height: 38px;
    padding: 0 10px;
    border-radius: 3px;
    border: 1px solid #FF5E5E;
    color: #FF5E5E;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
  }

  .tabs-content .goole-btn span {
    padding-left: 5px;
  }

  .title-h2 {
    font-size: 22px;
    font-weight: 500;
  }

  .title-p {
    color: #505050;
    font-size: 14px;
    line-height: 20px;
  }

  .other-apps {
    margin-top: 32px;
    padding-bottom: 30px;
  }

  .apps-card {
    display: flex;
    justify-content: space-between;
  }

  .mat-card {
    flex: 1;
    margin-right: 18px;
  }

  .card-header {
    display: flex;
    align-items: center;
  }

  .card-header img {
    height: 48px;
    width: 48px;
  }

  .card-header span {
    padding-left: 10px;
  }

  .content-item {
    display: flex;
    align-items: center;
    margin-top: 10px;
    cursor: pointer;
  }

  .content-item .content-img {
    height: 16px;
    width: 16px;
    margin-right: 6px;
  }

  .content-item p {
    margin: 0;
    padding: 0;
  }

  .checked-list {
    margin-top: 14px;
  }

  .checked-list-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    padding-bottom: 6px;
  }

  .checked-list-item span {
    padding: 0 10px 0 8px;
  }

  .checked-list-item .discount {
    height: 14px;
    padding: 0 5px;
    font-size: 12px;
    line-height: 14px;
    background: #FF5E5E;
    color: #fff;
    border-radius: 5px 0 5px 0;
  }

  .topArea-right {
    width: 225px;
    margin-left: 100px;
  }

  .topArea-right .swiper-container {
    padding-bottom: 15px;
  }

  .topArea-right .pagination {
    position: absolute;
    left: 0;
    text-align: center;
    bottom: 0px;
    width: 100%;
    z-index: 999;
  }

  .swiper-mobile .pagination {
    position: absolute;
    left: 0;
    text-align: center;
    bottom: 20px;
    width: 100%;
    z-index: 999;
  }

  .pagination /deep/ .swiper-pagination-bullet {
    display: inline-block;
    width: 8px;
    height: 8px;
    opacity: 1;
    border-radius: 10px;
    background: #C4C4C4;
    margin: 0 5px;
    cursor: pointer;
    transition: width 0.3s ease-in-out;
  }

  .pagination /deep/ .swiper-pagination-bullet-active {
    background: #FF5E5E;
  }

  .download-box {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  @media screen and (max-width: 599px) {
    .topArea-right {
      display: none;
    }

    .apps-card {
      flex-direction: column;
    }

    .apps-card .mat-card {
      margin-bottom: 16px;
      margin-right: 0px;
    }

    .tabsWrapper {
      width: 90vw;
    }

    .download-box {
      flex-direction: column;
      align-items: flex-start;
      gap: 6px;
    }
  }

  @media screen and (min-width: 650px) {
    .swiper-mobile {
      display: none;
    }
  }
</style>