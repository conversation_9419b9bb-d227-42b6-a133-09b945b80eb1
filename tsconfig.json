{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "module": "es2020", "moduleResolution": "node", "importHelpers": true, "target": "es2020", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"], "paths": {"@flashvpn-io/web-core": ["dist/web-core"]}}, "angularCompilerOptions": {"fullTemplateTypeCheck": true, "strictInjectionParameters": true}}