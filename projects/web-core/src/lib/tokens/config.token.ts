import { InjectionToken } from "@angular/core";
export interface Config {
  production: boolean;
  apiUrl: string;
  web: string;
  frontDomain: string;
  stripeKey: string;
  tgBot: string;
  firebase: {
    apiKey: string;
    authDomain: string;
    databaseURL: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
    measurementId: string;
  };
  vapidKey: string;
}
export const API_CONFIG = new InjectionToken<Config>("config");
