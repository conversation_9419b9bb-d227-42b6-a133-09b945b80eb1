import { HTTP_INTERCEPTORS, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from "@angular/common/http";
import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { AFTER_USER_LOGOUT, NotificationService, TOKEN_EXPRIED } from './notification.service';
import * as Rx from "rxjs";
import { flatMap, tap } from "rxjs/operators";
import { UsersService } from "../services/users.service";

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(
    @Inject(LOCALE_ID) public localeId: string,
    private usersService: UsersService,
    public router: Router,
    public route: ActivatedRoute,
    private notification: NotificationService
  ) { }

  intercept(req: HttpRequest<any>, next: HttpHandler) {
    // Get the auth token from the service.
    const token = localStorage.getItem("token");
    return Rx.of(token).pipe(
      flatMap((token) => {
        // Clone the request and replace the original headers with
        // cloned headers, updated with the authorization.
        let headers = req.headers;

        // auth
        if (!!token) {
          headers = headers.set("Authorization", `Bearer ${token}`);
        }

        // locale
        headers = headers.set("Content-Language", this.localeId);

        let clonedReq;

        if (req.url.indexOf("/cms/") < 0) {
          clonedReq = req.clone({ headers });
        } else {
          clonedReq = req; // 不设置请求头
        }
        return next.handle(clonedReq).pipe(
          tap(
            (event) => {
              return event;
            },
            async (err) => {
              if (err.status === 403 && req.url.indexOf("from=helpCenter") < 0) {
                this.notification.post(TOKEN_EXPRIED);
                if (token) {
                  await this.usersService.logout();
                }
                this.notification.post(AFTER_USER_LOGOUT);
              }
            }
          )
        );
      })
    );
  }
}
