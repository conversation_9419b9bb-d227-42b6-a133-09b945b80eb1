import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { flatMap, map } from "rxjs/operators";
import * as agent from "superagent";
import { Activity } from "../models/activity.model";
import { Campaign } from "../models/campaign.model";
import { Device } from "../models/device.model";
import { FlowDataRule } from "../models/flowdata-rule.model";
import { InviteRewardInfo } from "../models/invitation.model";
import { Invoice, InvoicePage } from "../models/invoice.model";
import { Product } from "../models/product.model";
import { Reward, RewardPageDto } from "../models/reward.model";
import { Rule } from "../models/rule.model";
import { Server } from "../models/server.model";
import { Service } from "../models/service.model";
import { Signup } from "../models/signup.model";
import { TgCode } from "../models/tg-code.model";
import { Transaction } from "../models/transaction.model";
import { User } from "../models/user.model";
import { Utm } from "../models/utm.model";
import { Wallet } from "../models/wallet.model";
import { API_CONFIG, Config } from "../tokens/config.token";

export interface Speed {
  rx: number;
  tx: number;
}

// check if we are on browser
export const isBrowser = typeof window !== "undefined";
// get from localstorage
export const getItem = (key: string): string => {
  return isBrowser ? window.localStorage.getItem(key) : "";
};

@Injectable({
  providedIn: "root",
})
export class APIManager {
  public serverUrl;
  private nodeApiBaseUrl = "http://localhost:10087";
  constructor(private http: HttpClient, @Inject(API_CONFIG) private config: Config) {
    this.validateConfig(config);
    this.serverUrl = this.config.apiUrl;
  }

  /**
   * Validate the provided config
   * @param config Config object to validate
   */
  private validateConfig(config: Config) {
    if (!config || !config.apiUrl) {
      throw new Error("No API URL provided");
    }
  }

  fetchUser(): Observable<User> {
    return this.http.get<User>(`${this.serverUrl}/api/v1/users/info`);
  }
  fetchServices(source?: string): Observable<Service[]> {
    let url = this.serverUrl + "/api/v1/services";
    if (source) {
      url = url + "?from=" + source;
    }
    return this.http.get<Service[]>(url).pipe(
      flatMap(async (data) => {
        const services = data.map((s) => Service.fromData(s));
        return services;
      })
    );
  }

  fetchServers(service: Service): Observable<Server[]> {
    let url = `${this.serverUrl}/api/v1/services/${service.id}/servers`;
    if (`${service.id}` === "sample") {
      url = `${this.serverUrl}/api/v1/open/servers`;
    }
    return this.http.get<Server[]>(url).pipe(
      flatMap(async (data) => {
        const servers = data.map((s) => Server.fromData(s));
        return servers;
      })
    );
  }

  fetchServiceDetail(service: Service, source?: string) {
    let url = `${this.serverUrl}/api/v1/services/${service.id}`;
    if (source) {
      url = url + "?from=" + source;
    }
    return this.http.get<Service>(url).pipe(
      flatMap(async (data) => {
        const service = Service.fromData(data);
        return service;
      })
    );
  }

  upgradeService(serviceId: number, body) {
    return this.http.post(`${this.serverUrl}/api/v1/services/${serviceId}/upgrade`, body);
  }

  fetchDailyUsage(serviceId: number, startDate: string, endDate: string) {
    return this.http.post(`${this.serverUrl}/api/v1/services/${serviceId}/dailyUsage`, {
      startDate,
      endDate,
    });
  }

  updateBillingCycle(serviceId: number, body) {
    return this.http.post<Invoice>(`${this.serverUrl}/api/v1/services/${serviceId}/update`, body);
  }

  deleteService(serviceId: number) {
    return this.http.get(`${this.serverUrl}/api/v1/services/${serviceId}/delete`);
  }

  fetchConfigFile(service: Service) {
    return this.http.get(`${service.subscriptionUrl}?client=jetstream`, {
      responseType: "text",
    });
  }

  fetchInvoices(): Observable<Invoice[]> {
    return this.http.get<Invoice[]>(`${this.serverUrl}/api/v1/invoices`).pipe(
      flatMap(async (data) => {
        const invoices = data.map((i) => Invoice.fromData(i));
        return invoices;
      })
    );
  }

  fetchInvoicesByPage(pageInfo: InvoicePage): Observable<InvoicePage> {
    return this.http.post<InvoicePage>(`${this.serverUrl}/api/v1/invoices/page`, pageInfo);
  }

  fetchInvoiceDetail(id: number): Observable<Invoice> {
    return this.http.get<Invoice>(`${this.serverUrl}/api/v1/invoices/${id}`);
  }
  fetchProducts(): Observable<Product[]> {
    return this.http.get<Product[]>(`${this.serverUrl}/api/v1/products`).pipe(
      flatMap(async (data) => {
        const products = data.map((p) => Product.fromData(p));
        return products;
      })
    );
  }

  createOrder(product: Product, billingcycle: string, promoCode?: string) {
    return this.http.post<any>(`${this.serverUrl}/api/v1/orders`, { productId: product.id, billingcycle, promoCode });
  }

  payInvoice(invoice: Invoice, method: string, paymentMethodId: string) {
    return this.http.get<any>(
      `${this.serverUrl}/api/v1/invoices/${invoice.id}/pay?paymentMethod=${method}${paymentMethodId ? "&paymentMethodId=" + paymentMethodId : ""}`
    );
  }

  deductionInvoice(invoice: Invoice, amount: number, rewardsid?: string, dcCode?: string, billingCycle?: string, flowdata = false) {
    return this.http.post<any>(`${this.serverUrl}/api/v1/invoices/${invoice.id}/deduct`, {
      amount: Number(amount),
      flowdata,
      rewardsid: Number(rewardsid),
      dcCode,
      billingCycle,
    });
  }

  regenerateInvoice(invoice: Invoice, force: boolean = false) {
    let url = `${this.serverUrl}/api/v1/invoices/${invoice.id}/regenerate`;
    if (force) {
      url = url + "?force=1";
    }
    return this.http.get<any>(url);
  }

  fetchLatestInvoice(service: Service): Observable<Invoice | null> {
    return this.http.get<Invoice>(`${this.serverUrl}/api/v1/services/${service.id}/latest`).pipe(
      flatMap(async (data) => {
        const invoice = Invoice.fromData(data);
        return invoice;
      })
    );
  }

  fetchLatestFlowInvoice(service: Service): Observable<Invoice> {
    return this.http.get<Invoice>(`${this.serverUrl}/api/v1/invoices/${service.id}/flow_latest`).pipe(
      flatMap(async (data) => {
        const invoice = Invoice.fromData(data);
        return invoice;
      })
    );
  }

  cancelInvoice(invoiceId: number) {
    return this.http.get<Invoice>(`${this.serverUrl}/api/v1/invoices/${invoiceId}/cancel`);
  }

  createUser(user: Signup) {
    return this.http.post(`${this.serverUrl}/api/v1/users`, user);
  }

  updateUTM(utm: Utm) {
    return this.http.post(`${this.serverUrl}/api/v1/users/utm`, utm);
  }

  isUserTried() {
    return this.http.get(`${this.serverUrl}/api/v1/invoices/isTrial`);
  }

  createTrialOrder(productId: string, paymentMethod: string) {
    return this.http.post(`${this.serverUrl}/api/v1/orders/trial`, { productId, billingcycle: "monthly", paymentMethod });
  }

  resendEmail() {
    return this.http.get(`${this.serverUrl}/api/v1/users/verify/resend`);
  }

  emailcheck(email: string) {
    return this.http.get(`${this.serverUrl}/api/v2/users/emailcheck?email=${email}`);
  }

  login(email: string, password: string) {
    return this.http.post(`${this.serverUrl}/api/v1/users/login`, { email, password });
  }

  redirect(target: string) {
    this.http.get(`${this.serverUrl}/api/v1/users/redirect/${target}`).subscribe((value) => {
      window.open(`${value["target"]}`, "_blank");
    });
  }

  proxyApp(platform: string) {
    return this.http.get(`${this.serverUrl}/api/v1/apps/FlashVPN/release/${platform}`);
  }

  /**
   * Get App By App Center
   * @param platform
   */
  getAppLinkByAppCenter(platform: string) {
    return this.http.get(`${this.serverUrl}/api/v1/apps/release/${platform}`);
  }

  /**
   * Get App By GitHub
   * @param platform
   */
  getAppLinkByGitHub(platform: string) {
    return this.http.get(`${this.serverUrl}/api/v1/apps/release-second/${platform}`);
  }

  walletsRule() {
    return this.http.get(`${this.serverUrl}/api/v1/wallets/rule`);
  }

  /**
   * Wallets and Rewards
   */

  /**
   * Get user's wallet
   * @param user
   */
  fetchWallet(): Observable<Wallet> {
    return this.http.get<Wallet>(`${this.serverUrl}/api/v1/wallets`);
  }

  /**
   * Get campaigns
   */
  fetchCampaigns(): Observable<Campaign[]> {
    return this.http.get<Campaign[]>(`${this.serverUrl}/api/v1/campaigns`);
  }

  /**
   * Get user's transactions
   * @param user
   */
  fetchTransactions(): Observable<Transaction[]> {
    return this.http.get<Transaction[]>(`${this.serverUrl}/api/v1/wallets/transactions`);
  }

  transferFunds(address: string, amount: string): Observable<Wallet> {
    return this.http.post<Wallet>(`${this.serverUrl}/api/v1/wallets/transfer`, { address, amount });
  }

  redeemData(service: Service, points: number) {
    return this.http.post<Service>(`${this.serverUrl}/api/v1/services/${service.id}/redeem`, { amount: points });
  }

  updateUserLanguage(locale: string) {
    return this.http.post(`${this.serverUrl}/api/v1/users/updateLocale`, { locale });
  }

  // selectorName = encodeURI('🔰 節點選擇');
  async updateNode(nodeName: string, selectorName: string) {
    selectorName = selectorName === "rule" ? "%F0%9F%94%B0%20%E7%AF%80%E9%BB%9E%E9%81%B8%E6%93%87" : "GLOBAL";
    return await agent.put(`${this.nodeApiBaseUrl}/proxies/${selectorName}`).send({ name: nodeName });
  }

  async updateMode(mode: string) {
    return agent.patch(`${this.nodeApiBaseUrl}/configs`).send({ mode });
  }

  getClashMode() {
    return agent.get(`${this.nodeApiBaseUrl}/configs`);
  }

  async getConnections() {
    return await agent.get(`${this.nodeApiBaseUrl}/connections`);
  }

  async deleteConnection(connectionId: string) {
    return await agent.delete(`${this.nodeApiBaseUrl}/connections/${connectionId}`);
  }

  fetchWalletsRule() {
    return this.http.get<Rule[]>(`${this.serverUrl}/api/v1/wallets/rule`);
  }

  createBalanceInvoices(invoices: { type: string; ruleId: number; amount: number }) {
    return this.http.post(`${this.serverUrl}/api/v1/invoices`, invoices);
  }

  payBalanceInvoice(invoiceid: string, method: string, token: string) {
    return this.http.get<any>(`${this.serverUrl}/api/v1/invoices/${invoiceid}/pay?paymentMethod=${method}&token=${token}`);
  }

  activities() {
    return this.http.get<any>(`${this.serverUrl}/api/v1/activities/awake`);
  }

  awake(serviceId: string) {
    const url = serviceId ? `${this.serverUrl}/api/v1/services/${serviceId}/awake` : `${this.serverUrl}/api/v1/services/awake`;
    return this.http.get<any>(url);
  }

  getFlowdataRule() {
    return this.http.get<FlowDataRule[]>(`${this.serverUrl}/api/v1/flowdata/rule`);
  }
  createFlowInvoice(invoices: { type: string; ruleId: number; amount: number; serviceId: number }): Observable<Invoice> {
    return this.http.post(`${this.serverUrl}/api/v1/invoices/createFlowInvoice`, invoices).pipe(
      flatMap(async (data) => {
        const invoice = Invoice.fromData(data);
        return invoice;
      })
    );
  }
  wakeUp(serviceId: string, activityId: number) {
    return this.http.post(`${this.serverUrl}/api/v1/services/${serviceId}/wake`, { activityId });
  }

  getActivityInfo(activityId: number = 1): Observable<Activity> {
    return this.http.get<Activity>(`${this.serverUrl}/api/v1/activities/${activityId}`);
  }

  getTgBindCode(): Observable<TgCode> {
    return this.http.get<TgCode>(`${this.serverUrl}/api/v1/users/tg`);
  }

  getHelpsCategories() {
    return this.http.get<any>(`${this.serverUrl}/cms/categories?populate=%2A`);
  }

  getHelpsArticles(params: { id?: string }) {
    let str = "?populate=%2A";
    if (params && params.id !== undefined) {
      str = `/${params.id}`;
    }
    return this.http.get<any>(`${this.serverUrl}/cms/articles${str}`);
  }

  getHelpsArticlesByShow() {
    return this.http.get<any>(`${this.serverUrl}/cms/articles?filters[show_on_service_page]=true`);
  }

  getInvitRewardsByPage(pageInfo: RewardPageDto): Observable<RewardPageDto> {
    return this.http.post<RewardPageDto>(`${this.serverUrl}/api/v1/rewards/getInvitRewardsByPage`, pageInfo);
  }

  getInvitRewardInfo(): Observable<InviteRewardInfo> {
    return this.http.get<InviteRewardInfo>(`${this.serverUrl}/api/v1/rewards/getInvitRewardInfo`);
  }

  sendInviteEmail(email: string): Observable<any> {
    return this.http.get<any>(`${this.serverUrl}/api/v1/users/invite/send?email=${email}`);
  }

  uploadFcmToken(token: string) {
    return this.http.post(`${this.serverUrl}/api/v1/users/fcm`, { token });
  }

  getMessageClient() {
    return this.http.get<any>(`${this.serverUrl}/api/v1/messages/client`);
  }

  getClientWelfareRule() {
    return this.http.get<any>(`${this.serverUrl}/api/v1/welfare/getClientRule`);
  }

  getMileageWelfareRule() {
    return this.http.get<any>(`${this.serverUrl}/api/v1/welfare/getMileageRule`);
  }

  getCommunityRule() {
    return this.http.get<any>(`${this.serverUrl}/api/v1/welfare/getCommunityRule`);
  }

  receiveWelfare(ruleId: string, type: string) {
    return this.http.post(`${this.serverUrl}/api/v1/welfare/receiveWelfare`, { ruleId, type });
  }

  getRewardsByUser() {
    return this.http.get<Reward[]>(`${this.serverUrl}/api/v1/rewards/getRewardsByUser`).pipe(
      flatMap(async (data) => {
        const rewards = data.map((r) => Reward.fromData(r));
        return rewards;
      })
    );
  }

  getCountByUser() {
    return this.http.get<any>(`${this.serverUrl}/api/v1/rewards/getCountByUser`);
  }

  verifyDcCode(code: string, billingCycle: string, serviceId: string) {
    return this.http.post<any>(`${this.serverUrl}/api/v1/invoices/verifyDcCode`, { code, billingCycle, serviceId });
  }

  claim(serviceId: number, type: "reset" | "extend", date: string) {
    return this.http.post<any>(`${this.serverUrl}/api/v1/services/${serviceId}/claim`, {
      type,
      date,
    });
  }

  trialInfo() {
    return this.http.get(`${this.serverUrl}/api/v2/products/trail`);
  }

  verifyEmail(code, email) {
    const refreshToken = localStorage.getItem("refresh_token");
    const httpOptions = {
      headers: new HttpHeaders({ authorization: `Bearer ${refreshToken}` }),
    };
    return this.http.get(`${this.serverUrl}/api/v1/users/verify/${code}/${email}`, httpOptions);
  }
  getApiUrl(): string {
    return this.config.apiUrl;
  }

  getDeliveryIPs() {
    return this.http.get<any>(`${this.serverUrl}/api/v1/apps/ips`);
  }

  getDeliveryDomains(host: string) {
    return this.http.get<any>(`${this.serverUrl}/api/v1/apps/dns-info?host=${this.config.frontDomain}`);
  }

  /**
   * Get all devices
   * GET /api/v1/devices
   */
  getAllDevices(): Observable<Device[]> {
    return this.http.get<any[]>(`${this.serverUrl}/api/v1/devices`).pipe(
      map((data) => {
        return data.map(d => Device.fromData(d));
      })
    );
  }

  /**
   * Get a device by ID
   * GET /api/v1/devices/:id
   */
  getDevice(id: number): Observable<Device> {
    return this.http.get<any>(`${this.serverUrl}/api/v1/devices/${id}`).pipe(
      map((data) => {
        return Device.fromData(data.device);
      })
    );
  }

  /**
   * Create a new device
   * POST /api/v1/devices
   */
  createDevice(deviceName: string, deviceId: string): Observable<Device> {
    return this.http.post<any>(`${this.serverUrl}/api/v1/devices`, {
      deviceName,
      deviceId
    }).pipe(
      map((data) => {
        return Device.fromData(data.device);
      })
    );
  }

  /**
   * Ban a device
   * POST /api/v1/devices/:id/ban
   */
  banDevice(id: number, code: string): Observable<Device> {
    return this.http.post<any>(`${this.serverUrl}/api/v1/devices/${id}/ban`, {
      code
    }).pipe(
      map((data) => {
        return Device.fromData(data.device);
      })
    );
  }

  /**
   * Unban a device
   * POST /api/v1/devices/:id/unban
   */
  unbanDevice(id: number, code: string): Observable<Device> {
    return this.http.post<any>(`${this.serverUrl}/api/v1/devices/${id}/unban`, {
      code
    }).pipe(
      map((data) => {
        return Device.fromData(data.device);
      })
    );
  }

  /**
   * Delete a device
   * GET /api/v1/devices/:id/delete
   */
  deleteDevice(id: number): Observable<any> {
    return this.http.get<any>(`${this.serverUrl}/api/v1/devices/${id}/delete`);
  }

  /**
   * Request verification code
   * GET /api/v1/devices/code
   */
  requestDeviceVerificationCode(): Observable<any> {
    return this.http.get<any>(`${this.serverUrl}/api/v1/devices/code`);
  }
}
