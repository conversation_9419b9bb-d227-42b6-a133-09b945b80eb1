import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { filter, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {

  constructor() { }
  private subject = new Subject<[string, any]>();
  post(name: string, value?: any) {
    this.subject.next([name, value]);
  }
  register(name: string): Observable<any> {
    return this.subject.pipe(filter(i => i[0] === name)).pipe((map(i => i[1])));
  }
}

export const AFTER_USER_LOGIN = 'AFTER_USER_LOGIN';
export const AFTER_USER_LOGOUT = 'AFTER_USER_LOGOUT';
export const USER_EMAIL_VERIFIED = 'USER_EMAIL_VERIFIED';
export const OPEN_DRAWER = 'OPEN_DRAWER';
export const AFTER_ORDER_CREATED = 'AFTER_ORDER_CREATED';
export const TURN_ON_SYSTEM_PROXY = 'TURN_ON_SYSTEM_PROXY';
export const TURN_OFF_SYSTEM_PROXY = 'TURN_OFF_SYSTEM_PROXY';
export const AFTER_TRANSFER_FUND = 'AFTER_TRANSFER_FUND';
export const AFTER_RELOAD_FUND = 'AFTER_RELOAD_FUND';
export const CHANGE_SERVICE = 'CHANGE_SERVICE';
export const AFTER_INVOICE_DEDUCT = 'AFTER_INVOICE_DEDUCT';
export const WINDOW_SRCOLL = 'WINDOW_SRCOLL';
export const AFTER_USER_PAID = 'AFTER_USER_PAID';
export const AFTER_INVOICE_CANCEL = 'AFTER_INVOICE_CANCEL';
export const TOKEN_EXPRIED = 'TOKEN_EXPRIED';

