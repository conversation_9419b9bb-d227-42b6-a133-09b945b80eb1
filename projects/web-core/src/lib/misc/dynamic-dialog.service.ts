import { Injectable, Injector, ComponentRef, Type } from '@angular/core';
import { Overlay, OverlayConfig, OverlayRef } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';

@Injectable({
  providedIn: 'root'
})
export class DynamicDialogService {
  overlayRef: OverlayRef;
  constructor(
    private overlay: Overlay,
    private injector: Injector
  ) {}

  open<T>(component: Type<T>, config?: OverlayConfig): T {
    // Create overlay
    this.overlayRef = this.overlay.create(config || new OverlayConfig({
      panelClass: 'custom-overlay',
      hasBackdrop: true,
      positionStrategy: this.overlay.position().global().centerHorizontally().centerVertically()
    }));

    // Create component portal
    const componentPortal = new ComponentPortal(component, null, this.injector);

    // Attach component to overlay
    const componentRef = this.overlayRef.attach(componentPortal);

    // Handle backdrop click
    this.overlayRef.backdropClick().subscribe(() => this.close());

    return componentRef.instance;
  }

  close() {
    if (this.overlayRef && this.overlayRef.hasAttached()) {
      this.overlayRef.dispose();
    }
  }
}