export interface SystemRule {
    id: number;
    domain: string;
    action: 'Directly' | 'Proxy' | 'Dropped';
    createdAt?: string;
    updatedAt?: string;
    isPlatform?: boolean;
}

export interface RuleCondition {
    field: string;
    operator: RuleOperator;
    value: any;
}

export interface RuleAction {
    type: RuleActionType;
    params: Record<string, any>;
}

export enum RuleOperator {
    EQUALS = 'equals',
    NOT_EQUALS = 'not_equals',
    GREATER_THAN = 'greater_than',
    LESS_THAN = 'less_than',
    CONTAINS = 'contains',
    NOT_CONTAINS = 'not_contains',
    IN = 'in',
    NOT_IN = 'not_in'
}

export enum RuleActionType {
    NOTIFY = 'notify',
    EMAIL = 'email',
    WEBHOOK = 'webhook',
    CUSTOM = 'custom'
}

export interface CreateRuleRequest {
    domain: string;
    action: 'Directly' | 'Proxy' | 'Dropped';
}

export interface UpdateRuleRequest extends CreateRuleRequest { }

export interface DeleteRuleResponse {
    message: string;
} 