import { Big } from "big.js";

export class Product {
  gid: number;
  name: string;
  description: string;
  pricing: any;
  id: number;

  constructor(gid: number, name: string, description: string, pricing: any, id: number) {
    this.gid = gid;
    this.name = name;
    this.description = description;
    this.pricing = pricing;
    this.id = id;
  }

  static fromData(data: Product) {
    const { gid, name, description, pricing, id } = data;
    return new Product(gid, name, description, pricing, id);
  }
  // calculate the saved amount if user choose to pay other than monthly
  calculateSaved(billingCycle) {
    if (billingCycle) {
      let pricePaidByMonth: Big;
      let price: Big;

      switch (billingCycle) {
        case "monthly":
          pricePaidByMonth = new Big(this.pricing.HKD.monthly);
          price = new Big(this.pricing.HKD.monthly);
          break;
        case "quarterly":
          pricePaidByMonth = new Big(this.pricing.HKD.monthly).times(3);
          price = new Big(this.pricing.HKD.quarterly);
          break;
        case "semiannually":
          pricePaidByMonth = new Big(this.pricing.HKD.monthly).times(6);
          price = new Big(this.pricing.HKD.semiannually);
          break;
        case "annually":
          pricePaidByMonth = new Big(this.pricing.HKD.monthly).times(12);
          price = new Big(this.pricing.HKD.annually);
          break;
        case "biennially":
          pricePaidByMonth = new Big(this.pricing.HKD.biennially).times(12);
          price = new Big(this.pricing.HKD.biennially);
      }
      return { pricePaidByMonth, saved: pricePaidByMonth.minus(price).div(pricePaidByMonth).times(100), price };
    }
  }

  getMonthlyAmount(cycles: string) {
    let amount = "";
    const hkd = this.pricing?.HKD;
    if (hkd && hkd[cycles]) {
      switch (cycles) {
        case "quarterly":
          amount = new Big(hkd[cycles]).div(3).toFixed(2);
          break;
        case "semiannually":
          amount = new Big(hkd[cycles]).div(6).toFixed(2);
          break;
        case "annually":
          amount = new Big(hkd[cycles]).div(12).toFixed(2);
          break;
        default:
          amount = hkd[cycles];
          break;
      }
    }
    return amount;
  }

  getAmountByBillingCycle(cycles: string) {
    return this.pricing?.HKD[cycles];
  }

  getSaveAmount(cycles: string) {
    return this.calculateSaved(cycles).saved.toFixed(0);
  }
}
