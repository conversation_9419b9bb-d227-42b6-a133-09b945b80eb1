export class Server {
    id: number;
    localId: string;
    serviceId: number;
    name: string;
    host: string;
    port: number;
    status: number;
    country: string;
    description: string;
    type: string;
    probe: string;
    tags: string[];
  
    username: string;
    password: string;
    uuid: string;
  
    latency = '-- ms';
  
    constructor(  name: string ,
                  host: string,
                  port: number,
                  status: number,
                  country: string,
                  description: string,
                  type: string,
                  probe: string,
                  tags: string[],
                  username: string,
                  password: string,
                  uuid: string,
                  serviceId: number,
                  id?: number) {
  
      if (id) { this.id = id; }
      this.name = name;
      this.host = host;
      this.port = port;
      this.status = status;
      this.country = country || this.name.match(/[A-Z]+/g).pop();
      this.description = description;
      this.type = type;
      this.probe = probe;
      this.tags = tags;
  
      this.username = username;
      this.password  = password;
      this.uuid = uuid;
      this.serviceId = serviceId;
      this.localId = `${serviceId} - ${id}`;
    }
  
    static fromData(data: any): Server {
      const {name, host, port, status, country, description, type, probe, tags,  username, password,  uuid, serviceId, id} = data;
      return new Server(name, host, port, status, country, description, type, probe, tags, username, password, uuid, serviceId, id);
    }
  
    ping(): void {
      const start = Date.now();
      fetch(`https://${this.host}:${this.port}`, { mode: 'no-cors', cache: 'no-store'})
        .then(() => {
          const latency = Math.round((Date.now() - start));
          this.latency = `${latency} ms`;
        })
        .catch(error => {
          this.latency = 'failed';
        });
    }
  }