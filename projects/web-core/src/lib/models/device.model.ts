export class Device {
    id: number;
    deviceName: string;
    deviceId: string;
    banned: boolean;
    createdAt: Date;
    updatedAt: Date;

    constructor(
        id?: number,
        deviceName?: string,
        deviceId?: string,
        banned?: boolean,
        createdAt?: Date,
        updatedAt?: Date
    ) {
        this.id = id;
        this.deviceName = deviceName;
        this.deviceId = deviceId;
        this.banned = banned;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    static fromData(data: any): Device {
        const { id, deviceName, deviceId, banned, createdAt, updatedAt } = data;
        return new Device(
            id,
            deviceName,
            deviceId,
            banned,
            createdAt ? new Date(createdAt) : undefined,
            updatedAt ? new Date(updatedAt) : undefined
        );
    }
} 