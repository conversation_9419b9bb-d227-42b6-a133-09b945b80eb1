import * as moment from 'moment';

export class Activity {
    id: number;
    name: string;
    description: string;
    type: string;
    status: string;
    gap: number;
    balance: number;
    day: number;
    traffic: number;
    expiredAt: Date;
    createdAt: Date;
    updatedAt: Date;
  
    toJSON() {
      return {
        id: this.id,
        name: this.name,
        description: this.description,
        type: this.type,
        status: this.status,
        balance: this.balance,
        day: this.day,
        traffic: this.traffic,
        expiredAt: this.expiredAt,
        createdAt: moment.utc(this.createdAt).format('YYYY-MM-DD HH:mm:ss'),
        updatedAt: moment(this.updatedAt).format('YYYY-MM-DD HH:mm:ss'),
      };
    }
  
    isExpired() {
      return moment.utc(this.expiredAt).isBefore(moment.utc());
    }
  }
  