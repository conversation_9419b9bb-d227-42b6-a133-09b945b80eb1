import * as moment from "moment";

export class InvoicePage {
    invoices?: Invoice[];
  
    limitstart: number;
  
    limitnum: number;
  
    total?: number;
  
    orderby?: string;
  
    order: string;
  
    status: string;
  }

export class InvoiceItem {
  id: number;
  description: string;
  amount: string;
  duedate: string;
  type: string;

  constructor(
    id: number,
    description: string,
    amount: string,
    duedate: string,
    type: string
  ) {
    this.id = id;
    this.description = description;
    this.amount = amount;
    this.duedate = duedate;
    this.type = type;
  }

  static fromData(data: any): InvoiceItem {
    return new InvoiceItem(
      data.id,
      data.description,
      data.amount,
      data.duedate,
      data.type
    );
  }

  getDueDate(): Date {
    return new Date(this.duedate);
  }

  getAmountAsNumber(): number {
    return parseFloat(this.amount);
  }

  getDeductionType(): DeductionType {
    if (this.description.includes('Deduction')) {
      switch (this.description.split(' ')[0]) {
        case 'Coupon': 
          return DeductionType.voucher; 
        case 'Code':
          return DeductionType.discount_code;
          // this.discountCode = deductionItems[0].description.split(' ')[2]
        case 'Balance':
          return DeductionType.balance;
        default: return DeductionType.none;
      }
    }else{
      return DeductionType.none;
    }
  }
}

export class Invoice {
    description: string;
    duedate: string;
    status: string;
    total: string;
    serviceId: number;
    datepaid: string;
    id: number;
    nextDueDate: string;
    serviceStatus: string;
    items?: InvoiceItem[];
  
    constructor(
      description: string,
      duedate: string,
      status: string,
      total: string,
      serviceId: number,
      datepaid: string,
      id: number,
      nextDueDate: string,
      serviceStatus: string,
      items?: InvoiceItem[]
    ) {
      if (id) { this.id = id; }
      if (nextDueDate) { this.nextDueDate = nextDueDate; }
      if (serviceStatus) { this.serviceStatus = serviceStatus; }
      this.description = description;
      this.duedate = duedate;
      this.status = status;
      this.total = total;
      this.serviceId = serviceId;
      this.datepaid = datepaid;
      this.items = items;
    }
  
    static fromData(data: any): Invoice | null {
      if (!data) {
        return null;
      }
      const {
        description,
        duedate,
        status,
        total,
        serviceId,
        datepaid,
        id,
        nextDueDate,
        serviceStatus,
        items
      } = data;

      const _items = items?.map(item => InvoiceItem.fromData(item));
      return new Invoice(description, duedate, status, total, serviceId, datepaid, id, nextDueDate, serviceStatus, _items);
    }
  
    daysTillDue(): number {
      return moment.duration(moment.utc(this.nextDueDate).diff(moment.utc())).asDays();
    }
    isDue(): boolean {
      // in frontend only due after 2 days, so the newly invoice can have more chance to get paid
      return this.daysTillDue() < -2 && this.status !== 'Paid';
    }

    hadDiscount() {
      return this.items?.some(item => item.getDeductionType() !== DeductionType.none);
    }

  }

export enum DeductionType{
    balance= 'BALANCE',
    voucher= 'VOUCHER',
    discount_code= 'DISCOUNT_CODE',
    none= 'NONE'
}

export enum BuyType {
    SERVICE = 1,
    FLOWDATA = 2,
    TRIAL = 3
  }
  
export enum RouterBuyType {
    SERVICE = "service",
    FLOWDATA = "flowdata",
}

  