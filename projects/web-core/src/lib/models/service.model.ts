import { Big } from "big.js";
import * as moment from "moment";

export class Service {
  id: number;
  name: string;
  nextduedate: string;
  status: string;
  createdAt: string;
  billingcycle: string;
  productId: number;

  usage: any;

  openURL: string;
  subscriptionUrl: string;

  resetDate: string;

  firstpaymentamount: string;

  recurringamount: number;

  constructor(
    name: string,
    nextduedate: string,
    status: string,
    billingcycle: string,
    productId: number,
    subscriptionUrl: string,
    usage: any,
    resetDate: string,
    id?: number,
    firstpaymentamount?: string,
    recurringamount?: number
  ) {
    if (id) {
      this.id = id;
    }
    this.name = name;
    this.nextduedate = nextduedate;
    this.status = status;
    this.billingcycle = billingcycle;
    this.productId = productId;
    this.subscriptionUrl = subscriptionUrl;
    this.usage = usage;
    this.resetDate = resetDate;
    this.firstpaymentamount = firstpaymentamount;
    this.recurringamount = recurringamount;
  }

  static fromData(data: any): Service {
    const { id, name, usage, nextduedateBuf, status, billingcycle, productId, subscriptionUrl, resetDate, firstpaymentamount, recurringamount } =
      data;
    return new Service(
      name,
      nextduedateBuf,
      status,
      billingcycle,
      productId,
      subscriptionUrl,
      usage,
      resetDate,
      id,
      firstpaymentamount,
      recurringamount
    );
  }

  daysTillDue(): number {
    if (!this.nextduedate) {
      return 0;
    }
    return moment.duration(moment.utc(this.nextduedate).diff(moment.utc())).asDays();
  }

  timeTillDue(): string {
    if (!this.nextduedate) {
      return `0 天`;
    }
    let dueHour = moment.utc(this.nextduedate).local().diff(moment.utc(), "hours");
    if (dueHour < 0) {
      return `0 天`;
    } else if (dueHour < 1) {
      return `${moment.utc(this.nextduedate).local().diff(moment.utc(), "minutes")} 分钟`;
    } else if (dueHour < 24) {
      return `${dueHour} 小时`;
    } else {
      return `${(dueHour / 24).toFixed(0)} 天`;
    }
  }

  isDue(): boolean {
    return this.daysTillDue() < 0;
  }
  usageCapInGB(): string {
    if (!this.usage) {
      return "-";
    }
    return new Big(Number(this.usage.allowed)).div(1073741824).toFixed(0);
  }
  used(): string {
    return new Big(this.usage.upload + this.usage.download)
      .div(new Big(Number(this.usage.allowed)))
      .times(100)
      .toFixed(2);
  }
  overUsed(): boolean {
    return new Big(this.usage.upload + this.usage.download).gt(new Big(Number(this.usage.allowed)));
  }
  overUsedInGB(): string {
    if (!this.usage) {
      return "-";
    }
    return new Big(Number(this.usage.allowed))
      .minus(new Big(this.usage.upload + this.usage.download))
      .div(1073741824)
      .toFixed(0);
  }
  speedLimit(): number {
    const qutoa = this.overUsedInGB();
    if (+qutoa >= 200) {
      return 15;
    }
    if (+qutoa >= 100) {
      return 10;
    }
    if (+qutoa >= 50) {
      return 5;
    }
    return 0;
  }
  speedLimitByProductId(): number {
    return this.productId === 1 ? 5 : this.productId === 2 ? 10 : 15;
  }
  terminated(): boolean {
    return this.status === "Terminated";
  }

  countResetDay() {
    return moment.utc(this.resetDate).local().diff(moment.utc(), "days");
  }

  countRestTime() {
    let resetHour = moment.utc(this.resetDate).local().diff(moment.utc(), "hours");
    if (resetHour < 0) {
      return `0 天`;
    } else if (resetHour < 1) {
      return `${moment.utc(this.resetDate).local().diff(moment.utc(), "minutes")} 分钟`;
    } else if (resetHour < 24) {
      return `${resetHour} 小时`;
    } else {
      return `${(resetHour / 24).toFixed(0)} 天`;
    }
  }

  // new methods from next version
  getNextduedate() {
    if (this.nextduedate) {
      return moment.utc(this.nextduedate).format("yyyy.MM.DD");
    }
    return "";
  }

  remaining() {
    if (!this.usage) {
      return 0;
    }
    return new Big(100).minus(new Big(this.usage?.upload + this.usage?.download).div(new Big(Number(this.usage.allowed))).times(100)).toFixed(2);
  }

  countResetDayPercentage() {
    const daysDifference = this.countResetDay();
    return ((daysDifference / 30) * 100).toFixed(0);
  }

  getRegdate() {
    if (this.resetDate) {
      return moment.utc(new Date(this.resetDate)).local().format("YYYY年MM月DD号 HH:mm");
    }
    return "";
  }
}
