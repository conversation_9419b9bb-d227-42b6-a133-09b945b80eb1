export class PayInfo  {

    invoiceId: number;
  
    paymentMethod: string;
  
    paymentToken: string;
  
    billingCycles: string;
  
  }


export interface PayMethod {
  payType: PayType;
  imgUrl: string;
  payName: string;
}

export enum PayType {
  WeChat = 'wechat',
  AliPay = 'alipay',
  Balance = 'balance',
  CreditCard = 'creditcard'
}

export const payMethodList: PayMethod[] = [
  {
    payType: PayType.WeChat,
    imgUrl: 'assets/images/products/wechat.svg',
    payName: '微信支付',
  },
  {
    payType: PayType.AliPay,
    imgUrl: 'assets/images/products/alipay.svg',
    payName: '支付宝',
  },
  {
    payType: PayType.Balance,
    imgUrl: 'assets/images/products/balance.svg',
    payName: '余额支付',
  },
  {
    payType: PayType.CreditCard,
    imgUrl: 'assets/images/products/bank.svg',
    payName: '信用卡支付',
  }
];