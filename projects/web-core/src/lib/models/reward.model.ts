export class RewardInvitDto {
  id: string;

  invitTime: string;

  inviteeEmail: string;

  award: number;

  expireDate: string;
}

export class RewardPageDto {
  rewards: RewardInvitDto[];

  limitstart: number;

  limitnum: number;

  orderby: string;

  order: string;

  total: number;
}

export enum RewardStatus {
  CLAIMED = "CLAIMED",
  USED = "USED",
  EXPIRED = "EXPIRED",
}

export class Reward {
  id: number;
  title: string;
  award: number;
  status: RewardStatus;
  note?: string;
  invoiceId?: number;
  expiryDate?: string;
  usedDate?: string;
  expiryDateTime?: Date;

  constructor(data: Partial<Reward>) {
    Object.assign(this, data);
  }

  static fromData(data: any): Reward {
    return new Reward({
      id: data.id,
      title: data.title,
      award: data.award,
      status: data.status as RewardStatus,
      note: data.note,
      invoiceId: data.invoiceId,
    });
  }
}
