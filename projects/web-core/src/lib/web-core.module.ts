import { ModuleWithProviders, NgModule } from "@angular/core";
import { WebCoreComponent } from "./web-core.component";
import { API_CONFIG, Config } from "./tokens/config.token";
import { AuthInterceptor } from "./misc/auth.interceptor";
import { HTTP_INTERCEPTORS } from "@angular/common/http";

@NgModule({
  declarations: [WebCoreComponent],
  imports: [],
  exports: [WebCoreComponent],
})
export class WebCoreModule {
  static forRoot(config: Config): ModuleWithProviders<WebCoreModule> {
    return {
      ngModule: WebCoreModule,
      providers: [
        {
          provide: HTTP_INTERCEPTORS,
          useClass: AuthInterceptor,
          multi: true,
        },
        {
          provide: API_CONFIG,
          useValue: config,
        },
      ],
    };
  }
}
