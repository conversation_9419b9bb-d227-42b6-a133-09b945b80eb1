import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { IEventService } from '../interfaces/event-service.interface';

@Injectable({
    providedIn: 'root'
})
export class CoreEventService implements IEventService {
    private subject = new Subject<[string, any]>();

    post(name: string, value?: any) {
        this.subject.next([name, value]);
    }

    register(name: string): Observable<any> {
        return this.subject.pipe(
            filter(i => i[0] === name),
            map(i => i[1])
        );
    }
}

// 定义核心事件常量
export const CORE_EVENTS = {
    AFTER_USER_LOGIN: 'AFTER_USER_LOGIN',
    AFTER_USER_LOGOUT: 'AFTER_USER_LOGOUT',
    USER_EMAIL_VERIFIED: 'USER_EMAIL_VERIFIED',
    OPEN_DRAWER: 'OPEN_DRAWER',
    AFTER_ORDER_CREATED: 'AFTER_ORDER_CREATED',
    TURN_ON_SYSTEM_PROXY: 'TURN_ON_SYSTEM_PROXY',
    TURN_OFF_SYSTEM_PROXY: 'TURN_OFF_SYSTEM_PROXY',
    AFTER_TRANSFER_FUND: 'AFTER_TRANSFER_FUND',
    AFTER_RELOAD_FUND: 'AFTER_RELOAD_FUND',
    CHANGE_SERVICE: 'CHANGE_SERVICE',
    AFTER_INVOICE_DEDUCT: 'AFTER_INVOICE_DEDUCT',
    WINDOW_SRCOLL: 'WINDOW_SRCOLL',
    AFTER_USER_PAID: 'AFTER_USER_PAID'
}; 