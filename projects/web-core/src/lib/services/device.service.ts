import { Injectable } from "@angular/core";
import { BehaviorSubject, Observable } from "rxjs";
import { map } from "rxjs/operators";
import { APIManager } from "../misc/api.manager.service";
import { Device } from "../models/device.model";

@Injectable({
    providedIn: "root",
})
export class DeviceService {
    private _devices$ = new BehaviorSubject<Device[]>([]);
    public devices$ = this._devices$.asObservable();

    constructor(private apiManager: APIManager) { }

    /**
     * Get all devices
     * GET /api/v1/devices
     */
    getAllDevices(): Observable<Device[]> {
        return this.apiManager.getAllDevices().pipe(
            map((devices) => {
                this._devices$.next(devices);
                return devices;
            })
        );
    }

    /**
     * Get a device by ID
     * GET /api/v1/devices/:id
     */
    getDevice(id: number): Observable<Device> {
        return this.apiManager.getDevice(id);
    }

    /**
     * Create a new device
     * POST /api/v1/devices
     */
    createDevice(deviceName: string, deviceId: string): Observable<Device> {
        return this.apiManager.createDevice(deviceName, deviceId).pipe(
            map((device) => {
                // Update devices list
                const devices = this._devices$.value;
                this._devices$.next([...devices, device]);
                return device;
            })
        );
    }

    /**
     * Ban a device
     * POST /api/v1/devices/:id/ban
     */
    banDevice(id: number, code: string): Observable<Device> {
        return this.apiManager.banDevice(id, code).pipe(
            map((device) => {
                // Update devices list
                const devices = this._devices$.value;
                const index = devices.findIndex((d) => d.id === device.id);
                if (index !== -1) {
                    devices[index] = device;
                    this._devices$.next([...devices]);
                }
                return device;
            })
        );
    }

    /**
     * Unban a device
     * POST /api/v1/devices/:id/unban
     */
    unbanDevice(id: number, code: string): Observable<Device> {
        return this.apiManager.unbanDevice(id, code).pipe(
            map((device) => {
                // Update devices list
                const devices = this._devices$.value;
                const index = devices.findIndex((d) => d.id === device.id);
                if (index !== -1) {
                    devices[index] = device;
                    this._devices$.next([...devices]);
                }
                return device;
            })
        );
    }

    /**
     * Delete a device
     * GET /api/v1/devices/:id/delete
     */
    deleteDevice(id: number): Observable<any> {
        return this.apiManager.deleteDevice(id).pipe(
            map((result) => {
                if (result.success) {
                    // Update devices list
                    const devices = this._devices$.value;
                    this._devices$.next(devices.filter((d) => d.id !== id));
                }
                return result;
            })
        );
    }

    /**
     * Request verification code
     * GET /api/v1/devices/code
     */
    requestVerificationCode(): Observable<any> {
        return this.apiManager.requestDeviceVerificationCode();
    }
} 