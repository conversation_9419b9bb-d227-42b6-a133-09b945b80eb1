import { Injectable } from "@angular/core";
import { APIManager } from "../misc/api.manager.service";
import { UsersService } from "./users.service";
import { filter, tap } from "rxjs/operators";
import { BehaviorSubject } from 'rxjs';

interface StoredReadState {
  messageIds: number[];  // 存储已读消息的ID
  userId: number;        // 存储用户ID，用于区分不同用户
}

@Injectable({
  providedIn: "root",
})
export class FcmService {
  // 将 unreadNum 改为 BehaviorSubject
  private _unreadNum = new BehaviorSubject<number>(0);
  public unreadNum$ = this._unreadNum.asObservable();

  public currentUserId: number = 0;
  public messageList: Array<any> = [];
  public originMessageList: Array<any> = [];
  public checked = false;
  private allMessageIds: number[] = [];

  constructor(private apiManager: APIManager, private userService: UsersService) {
    this.userService.user$
      .pipe(
        filter((user) => !!user),
        tap((user) => {
          this.currentUserId = user.id; // 保存当前用户ID
          this.getFcm();
        })
      )
      .subscribe();
  }

  private async updateUnreadNum() {
    const storedState = localStorage.getItem(`messageReadState_${this.currentUserId}`);
    let readMessageIds: number[] = [];
    if (storedState) {
      const parsed = JSON.parse(storedState) as StoredReadState;
      if (parsed.userId === this.currentUserId) {
        readMessageIds = parsed.messageIds;
      }
    }

    try {
      // 直接从 originMessageList 获取所有消息 ID
      this.allMessageIds = this.originMessageList.map(message => message.id);

      // 计算未读消息数量
      const unreadCount = this.originMessageList.filter(
        (item) => !item.readUserIds.includes(this.currentUserId)
      ).length;
      this._unreadNum.next(unreadCount);
    } catch (error) {
      console.error('Failed to calculate unread messages:', error);
      const currentUnreadCount = this.originMessageList.filter(
        (item) => !item.readUserIds.includes(this.currentUserId)
      ).length;
      this._unreadNum.next(currentUnreadCount);
    }
  }

  async getFcm() {
    // 获取本地存储的已读状态
    const storedState = localStorage.getItem(`messageReadState_${this.currentUserId}`);
    let readMessageIds: number[] = [];
    if (storedState) {
      const parsed = JSON.parse(storedState) as StoredReadState;
      if (parsed.userId === this.currentUserId) {
        readMessageIds = parsed.messageIds;
      }
    }

    this.checked = localStorage.getItem("messageShowRead") === "true";

    this.apiManager.getMessageClient().subscribe(async (response) => {
      // 确保我们正确处理 API 返回的数据
      const messageList = Array.isArray(response) ? response : (response.list || []);

      // 合并服务器数据和本地已读状态
      this.originMessageList = messageList.map(element => ({
        ...element,
        readUserIds: readMessageIds.includes(element.id) ? [this.currentUserId] : []
      }));

      // 更新未读数量
      await this.updateUnreadNum();

      this.methodOnChange(this.checked);
    });
  }

  private saveReadState() {
    const storedState = localStorage.getItem(`messageReadState_${this.currentUserId}`);
    let readMessageIds: number[] = [];

    if (storedState) {
      const parsed = JSON.parse(storedState) as StoredReadState;
      if (parsed.userId === this.currentUserId) {
        readMessageIds = parsed.messageIds;
      }
    }

    // 添加新的已读消息ID
    this.originMessageList.forEach(message => {
      if (message.readUserIds.includes(this.currentUserId)
        && !readMessageIds.includes(message.id)) {
        readMessageIds.push(message.id);
      }
    });

    const readState: StoredReadState = {
      messageIds: readMessageIds,
      userId: this.currentUserId
    };

    localStorage.setItem(
      `messageReadState_${this.currentUserId}`,
      JSON.stringify(readState)
    );
  }

  handleReadAll() {
    this.originMessageList.forEach((el) => {
      if (!el.readUserIds.includes(this.currentUserId)) {
        el.readUserIds.push(this.currentUserId);
      }
    });

    const readState: StoredReadState = {
      messageIds: this.allMessageIds,
      userId: this.currentUserId
    };

    localStorage.setItem(
      `messageReadState_${this.currentUserId}`,
      JSON.stringify(readState)
    );

    // 更新未读数量为 0
    this._unreadNum.next(0);

    // 根据 checked 状态决定显示哪些消息
    this.methodOnChange(this.checked);
  }

  async methodOnChange(e) {
    localStorage.setItem("messageShowRead", e);
    this.checked = e;
    if (e) {
      // 只显示当前用户未读的消息
      this.messageList = this.originMessageList.filter(
        (el) => !el.readUserIds.includes(this.currentUserId)
      );
      // 如果所有消息都已读，messageList 将是空数组，这是正确的行为
    } else {
      // 显示所有消息
      this.messageList = this.originMessageList;
    }
  }

  async handleSwitch(info) {
    this.originMessageList.forEach((el) => {
      if (el.id === info.id && !el.readUserIds.includes(this.currentUserId)) {
        el.readUserIds.push(this.currentUserId);
      }
    });

    // 保存已读状态
    this.saveReadState();

    // 更新未读数量
    await this.updateUnreadNum();

    this.messageList = this.originMessageList;
    this.methodOnChange(this.checked);
  }
}
