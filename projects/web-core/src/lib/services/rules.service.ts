import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { CreateRuleRequest, DeleteRuleResponse, SystemRule, UpdateRuleRequest } from '../interfaces/rule.interface';
import { APIManager } from '../misc/api.manager.service';

@Injectable({
    providedIn: 'root'
})
export class RulesService {
    private readonly baseUrl = '/api/v1/rules';

    constructor(
        private http: HttpClient,
        private apiManager: APIManager
    ) { }

    /**
     * Get all rules
     */
    getRules(): Observable<SystemRule[]> {
        return this.http.get<SystemRule[]>(`${this.apiManager.serverUrl}${this.baseUrl}`);
    }

    /**
     * Get a specific rule by ID
     */
    getRule(id: number): Observable<SystemRule> {
        return this.http.get<SystemRule>(`${this.apiManager.serverUrl}${this.baseUrl}/${id}`);
    }

    /**
     * Create a new rule
     */
    createRule(rule: CreateRuleRequest): Observable<SystemRule> {
        return this.http.post<SystemRule>(`${this.apiManager.serverUrl}${this.baseUrl}`, rule);
    }

    /**
     * Update an existing rule
     * @param id The numeric ID of the rule to update
     * @param rule The updated rule data
     */
    updateRule(id: number, rule: UpdateRuleRequest): Observable<SystemRule> {
        return this.http.post<SystemRule>(`${this.apiManager.serverUrl}${this.baseUrl}/${id}`, rule);
    }

    /**
     * Delete a rule by ID
     * @param id The numeric ID of the rule to delete
     * @returns Observable<DeleteRuleResponse> Success message
     */
    deleteRule(id: number): Observable<DeleteRuleResponse> {
        return this.http.get<DeleteRuleResponse>(`${this.apiManager.serverUrl}${this.baseUrl}/${id}/delete`);
    }

    /**
     * Enable a rule
     */
    enableRule(id: number): Observable<SystemRule> {
        return this.http.post<SystemRule>(`${this.apiManager.serverUrl}${this.baseUrl}/${id}/enable`, {});
    }

    /**
     * Disable a rule
     */
    disableRule(id: number): Observable<SystemRule> {
        return this.http.post<SystemRule>(`${this.apiManager.serverUrl}${this.baseUrl}/${id}/disable`, {});
    }
} 