import { Injectable } from "@angular/core";
import { BehaviorSubject } from "rxjs";
import { filter, tap } from "rxjs/operators";
import { InviteRewardInfo } from "../models/invitation.model";
import { UsersService } from "./users.service";
import { APIManager } from "../misc/api.manager.service";
import { Reward, RewardStatus } from "../models/reward.model";
import { NotificationService, AFTER_INVOICE_CANCEL } from "../misc/notification.service";

@Injectable({
  providedIn: "root",
})
export class RewardService {
  public inviteRewards$ = new BehaviorSubject<InviteRewardInfo | null>(null);
  public coupons$ = new BehaviorSubject<Reward[] | null>(null);
  public availableCoupons$ = new BehaviorSubject<Reward[] | null>(null);
  public countByUser$ = new BehaviorSubject<number | null>(null);

  constructor(
    private apiManager: APIManager,
    private userService: UsersService,
    private notificationService: NotificationService,
  ) {
    this.userService.user$
      .pipe(
        filter((user) => !!user),
        tap(() => {
          this.refreshRewardInfo();
          this.getRewards();
        })
      )
      .subscribe();

    this.notificationService.register(AFTER_INVOICE_CANCEL).subscribe(() => {
      this.refreshRewardInfo();
      this.getRewards();
    });
  }

  public refreshRewardInfo(): void {
    this.apiManager.getInvitRewardInfo().subscribe((rewardInfo) => this.inviteRewards$.next(rewardInfo));
  }

  public getRewards(): Promise<Reward[]> {
    return this.apiManager
      .getRewardsByUser()
      .pipe(
        tap((rewards) => {
          this.coupons$.next(rewards);
          this.availableCoupons$.next(rewards.filter((reward) => reward.status === RewardStatus.CLAIMED));
        })
      )
      .toPromise();
  }

  public getCountByUser(): Promise<number> {
    return this.apiManager
      .getCountByUser()
      .pipe(tap((num) => this.countByUser$.next(num)))
      .toPromise();
  }
}
