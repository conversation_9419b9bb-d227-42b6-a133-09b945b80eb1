import { Injectable } from "@angular/core";
import { BehaviorSubject, Observable } from "rxjs";
import { filter, tap } from "rxjs/operators";
import { APIManager } from "../misc/api.manager.service";
import { Invoice, InvoicePage } from "../models/invoice.model";
import { UsersService } from "./users.service";

@Injectable({
  providedIn: "root",
})
export class InvoiceService {
  public invoiceInfo$ = new BehaviorSubject<Invoice[] | null>(null);

  constructor(private apiManager: APIManager, private userService: UsersService) {
    this.userService.user$
      .pipe(
        filter((user) => !!user),
        tap(() => this.refreshInvoiceInfo())
      )
      .subscribe();
  }

  public refreshInvoiceInfo(invoicePage?: InvoicePage): Observable<InvoicePage> {
    if (!invoicePage) {
      invoicePage = { limitstart: 0, limitnum: 10, orderby: "id", order: "DESC", status: "" };
    }
    return this.apiManager.fetchInvoicesByPage(invoicePage).pipe(
      tap((invoicePage: InvoicePage) => {
        this.invoiceInfo$.next(invoicePage.invoices);
      })
    );
  }
}
