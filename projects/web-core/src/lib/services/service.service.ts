import { Injectable } from "@angular/core";
import { BehaviorSubject, Observable } from "rxjs";
import { filter, tap } from "rxjs/operators";
import { Service } from "../models/service.model";
import { UsersService } from "./users.service";
import { APIManager } from "../misc/api.manager.service";
import { AFTER_USER_PAID, NotificationService } from "../misc/notification.service";
import { Product } from "../models/product.model";
@Injectable({
  providedIn: "root",
})
export class ServiceService {
  public currentService$ = new BehaviorSubject<Service | null>(null);
  public serviceInfo$ = new BehaviorSubject<Service[] | null>(null);
  public isTried$ = new BehaviorSubject<boolean | null>(null);
  public trialInvoiceId$ = new BehaviorSubject<string | null>(null);
  public products$: BehaviorSubject<Product[] | null> = new BehaviorSubject<Product[] | null>(null);

  constructor(private apiManager: APIManager, private userService: UsersService, private notification: NotificationService) {
    this.fetchProducts();
    this.getTrialProductInfo();
    this.notification.register(AFTER_USER_PAID).subscribe(async () => {
      await this.checkUserTrial();
    });
    this.userService.user$
      .pipe(
        filter((user) => !!user),
        tap(() => {
          this.refreshServiceInfo();
          this.checkUserTrial();
        })
      )
      .subscribe();
  }

  public fetchProducts(): Promise<Product[]> {
    return this.apiManager
      .fetchProducts()
      .pipe(
        tap((products: Product[]) => {
          this.products$.next(products);
        })
      )
      .toPromise();
  }

  public fetchServiceDailyUsage(startDate: string, endDate: string) {
    const currentServiceId = localStorage.getItem("currentServiceId");
    return this.apiManager
      .fetchDailyUsage(+currentServiceId, startDate, endDate)
      .toPromise()
  }

  public refreshServiceInfo(): Promise<Service[]> {
    const currentServiceId = localStorage.getItem("currentServiceId");
    return this.apiManager
      .fetchServices()
      .pipe(
        tap((services: Service[]) => {
          this.serviceInfo$.next(services);
          const selectedService = currentServiceId ?
            services.find(s => s.id === parseInt(currentServiceId)) :
            services[0];
          this.setCurrentService(selectedService || services[0]);
        })
      )
      .toPromise();
  }

  public setCurrentService(service: Service) {
    this.currentService$.next(service);
    this.apiManager.fetchServiceDetail(service).subscribe((service) => {
      this.currentService$.next(service);
    });
  }
  async checkUserTrial() {
    try {
      const data = await this.apiManager.isUserTried().toPromise();
      this.isTried$.next(data["isTrial"]);
      this.trialInvoiceId$.next(data["invoiceId"]);
      return data;
    } catch (error) {
      console.log(`failed to get isTrial`);
      throw error;
    }
  }

  async getTrialProductInfo() {
    return this.apiManager.trialInfo().toPromise().then(data => {
      localStorage.setItem("trialPrice", data["trialPrice"]);
      localStorage.setItem("trialTraffic", data["trialTraffic"]);
      localStorage.setItem("trialPeriod", data["trialPeriod"]);
      return data;
    });
  }
}
