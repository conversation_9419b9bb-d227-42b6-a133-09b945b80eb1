import { Injectable } from "@angular/core";
import { APIManager } from "../misc/api.manager.service";
import { UsersService } from "./users.service";
import { CoreEventService, CORE_EVENTS } from "./core-event.service";
import { Wallet } from "../models/wallet.model";
import { filter, tap } from "rxjs/operators";
import { Transaction } from "../models/transaction.model";
import { BehaviorSubject } from "rxjs";
import { Rule } from "../models/rule.model";

@Injectable({
  providedIn: "root",
})
export class WalletsService {
  public wallet$ = new BehaviorSubject<Wallet | null>(null);
  public transactions$ = new BehaviorSubject<Transaction[]>([]);
  public rechargeList$ = new BehaviorSubject<Rule[]>([]);

  constructor(private apiManager: APIManager, private usersService: UsersService, private eventService: CoreEventService) {
    this.getWalletsRule();
    this.usersService.user$
      .pipe(
        filter((user) => !!user),
        tap(() => this.reloadWallet())
      )
      .subscribe();
    this.eventService.register(CORE_EVENTS.AFTER_TRANSFER_FUND).subscribe((_) => this.reloadWallet());
  }

  reloadWallet() {
    this.apiManager.fetchWallet().subscribe((value) => {
      this.wallet$.next(value);
      this.eventService.post(CORE_EVENTS.AFTER_RELOAD_FUND, value);
    });
    this.apiManager
      .fetchTransactions()
      .pipe(
        tap((value) => {
          this.transactions$.next(value["records"]);
        })
      )
      .subscribe();
  }

  /**
   * 获取优惠
   */
  getWalletsRule = () => {
    this.apiManager
      .fetchWalletsRule()
      .pipe(
        tap((value) => {
          this.rechargeList$.next(value);
        })
      )
      .subscribe();
  };
}
