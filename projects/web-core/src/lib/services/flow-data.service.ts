import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { BehaviorSubject } from "rxjs";
import { tap } from "rxjs/operators";
import { APIManager } from "../misc/api.manager.service";
import { FlowDataRule } from "../models/flowdata-rule.model";

@Injectable({
  providedIn: "root",
})
export class FlowDataService {
  public flowData$ = new BehaviorSubject<FlowDataRule[]>([]);

  constructor(private http: HttpClient, private apiManager: APIManager) {
    this.fetchFlowData();
  }

  fetchFlowData(): Promise<FlowDataRule[]> {
    return this.apiManager
      .getFlowdataRule()
      .pipe(tap((data) => this.flowData$.next(data)))
      .toPromise();
  }
}
