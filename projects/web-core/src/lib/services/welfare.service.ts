import { Injectable } from "@angular/core";
import { BehaviorSubject } from "rxjs";
import { filter, tap } from "rxjs/operators";
import { APIManager } from "../misc/api.manager.service";
import { UsersService } from "./users.service";

@Injectable({
  providedIn: "root",
})
export class WelfareService {
  public clientWelfareRule$ = new BehaviorSubject<any[]>([]);
  public mileageWelfareRule$ = new BehaviorSubject<any[]>([]);
  public communityRule$ = new BehaviorSubject<any[]>([]);
  public receiveWelfare$ = new BehaviorSubject<any>(null);
  public serviceDays$ = new BehaviorSubject<number>(0);

  constructor(private apiManager: APIManager, private usersService: UsersService) {
    this.usersService.user$
      .pipe(
        filter((user) => !!user),
        tap(() => this.loadAllWelfareRules())
      )
      .subscribe();
  }

  private loadAllWelfareRules() {
    this.getClientWelfareRule();
    this.getMileageWelfareRule();
    this.getCommunityRule();
  }

  getClientWelfareRule() {
    this.apiManager
      .getClientWelfareRule()
      .pipe(
        tap((value) => {
          this.clientWelfareRule$.next(value);
        })
      )
      .subscribe();
  }

  getMileageWelfareRule() {
    this.apiManager
      .getMileageWelfareRule()
      .pipe(
        tap((value) => {
          this.mileageWelfareRule$.next(value?.welfareRule);
          this.serviceDays$.next(value?.serviceDays);
        })
      )
      .subscribe();
  }

  getCommunityRule() {
    this.apiManager
      .getCommunityRule()
      .pipe(
        tap((value) => {
          this.communityRule$.next(value);
        })
      )
      .subscribe();
  }

  receiveWelfare(ruleId: string, type: string) {
    return this.apiManager.receiveWelfare(ruleId, type).pipe(
      tap((value) => {
        this.receiveWelfare$.next(value);
      })
    );
  }
}
