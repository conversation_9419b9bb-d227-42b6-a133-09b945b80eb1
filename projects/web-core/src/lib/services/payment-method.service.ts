import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { BehaviorSubject, Observable } from "rxjs";
import { tap } from "rxjs/operators";
import { Inject } from "@angular/core";
import { Config } from "../tokens/config.token";
import { PayMethod, payMethodList } from "../models/pay-info.model";
import { API_CONFIG } from "../tokens/config.token";
@Injectable({
  providedIn: "root",
})
export class PaymentMethodService {
  private serverUrl = ""
  public paymentMethods$ = new BehaviorSubject<PayMethod[]>([]);
  public currentMethod$ = new BehaviorSubject<PayMethod>(null);

  constructor(private http: HttpClient, @Inject(API_CONFIG) private config: Config) {
    this.serverUrl = config.apiUrl;
    this.fetchPaymentMethods();
  }

  fetchPaymentMethods() {
    this.http
      .get<string[]>(`${this.serverUrl}/api/v1/invoices/paymentMethods`)
      .pipe(
        tap((methods) => {
          const filteredMethods = payMethodList.filter((method) => methods.includes(method.payType));
          this.paymentMethods$.next(filteredMethods);
          if (filteredMethods.length > 0) {
            this.currentMethod$.next(filteredMethods[0]);
          }
        })
      )
      .subscribe();
  }
  public methodOnChange(event: any) {
    console.log(event);
  }
}
