import { Injectable } from "@angular/core";
import { APIManager } from "../misc/api.manager.service";
import { BehaviorSubject } from "rxjs";
import { User } from "../models/user.model";
import { Utm } from "../models/utm.model";
import { CoreEventService, CORE_EVENTS } from "./core-event.service";
declare var chrome;

@Injectable({
  providedIn: "root",
})
export class UsersService {
  public user?: User;
  public user$ = new BehaviorSubject<User | undefined>(null);

  public sigupToken: string;
  constructor(private eventService: CoreEventService, private apiManager: APIManager) {
    if (this.isTokenSaved()) {
      this.reloadUser();
    }
  }

  isTokenSaved(): boolean {
    return localStorage.getItem("token") !== null;
  }

  async isLoggedIn(): Promise<boolean> {
    const u = await this.currentUser();
    return u !== undefined;
  }

  // fetch user from db if not in memory
  async currentUser(): Promise<User> {
    if (this.user === undefined) {
      await this.reloadUser();
    }
    return this.user;
  }

  // save user token to db
  async login(token: string, email: string) {
    const user = new User();
    user.token = token;
    user.email = email;
    await this.loginByUser(user);

    await this.reloadUser();
  }

  async loginByUser(user: User) {
    localStorage.setItem("token", user.token);
    this.user = user;
    this.user$.next(this.user);
    this.eventService.post(CORE_EVENTS.AFTER_USER_LOGIN, user.token);
  }
  // clear out all db related to logged user
  async logout() {
    delete this.user;
    this.user$.next(null);

    // reset current server
    localStorage.removeItem("token");
    localStorage.removeItem("currentService");
    localStorage.removeItem("currentServer");
    // logout for chatwoot
    // @ts-ignore
    window?.$chatwoot?.reset();
  }

  // fetch user info from server
  async reloadUser() {
    try {
      let user = await this.apiManager.fetchUser().toPromise();
      let token = localStorage.getItem("token");
      if (!!token && !!user) {
        user.token = token;
        await this.loginByUser(user as User);
      }
    } catch (e) { }
  }

  /**
   * call signup again to trigger verification email
   */
  async resendEmail() {
    try {
      await this.apiManager.resendEmail().toPromise();
    } catch (e) { }
  }

  async setUtm(data: Utm) {
    if (data.utmSource) {
      localStorage.setItem("utmSource", data.utmSource);
    }
    if (data.utmMedium) {
      localStorage.setItem("utmMedium", data.utmMedium);
    }
    if (data.utmCampaign) {
      localStorage.setItem("utmCampaign", data.utmCampaign);
    }
    if (data.utmTerm) {
      localStorage.setItem("utmTerm", data.utmTerm);
    }
    if (data.utmContent) {
      localStorage.setItem("utmContent", data.utmContent);
    }
  }

  removeUtm() {
    localStorage.removeItem("utmSource");
    localStorage.removeItem("utmMedium");
    localStorage.removeItem("utmCampaign");
    localStorage.removeItem("utmTerm");
    localStorage.removeItem("utmContent");
  }
}
