{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"web": {"i18n": {"sourceLocale": {"code": "en-US", "baseHref": "/"}, "locales": {"zh-Hans": {"translation": "src/locale/messages.zh-Hans.xlf", "baseHref": "/"}}}, "projectType": "application", "schematics": {"@schematics/angular:component": {"style": "less"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/web", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": true, "assets": ["src/favicon.ico", "src/robots.txt", "src/epay.html", "src/launcher.html", "src/assets", "src/static", "src/firebase-messaging-sw.js", "src/manifest.json", "src/vendors/lazysizes.min.js"], "styles": ["src/styles.css", "src/theme.scss"], "scripts": [], "allowedCommonJsDependencies": ["big.js", "lodash", "moment", "superagent", "qrcode"]}, "configurations": {"production": {"localize": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "120kb"}]}, "uat": {"localize": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.uat.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "120kb"}]}, "development": {"optimization": false, "sourceMap": true, "namedChunks": true, "extractLicenses": false, "vendorChunk": true, "buildOptimizer": false, "budgets": [{"type": "initial", "maximumWarning": "15mb", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "web:build"}, "configurations": {"development": {"browserTarget": "web:build:development"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "web:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/robots.txt", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.css"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}}}, "web-core": {"projectType": "library", "root": "projects/web-core", "sourceRoot": "projects/web-core/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/web-core/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/web-core/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/web-core/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/web-core/src/test.ts", "tsConfig": "projects/web-core/tsconfig.spec.json", "karmaConfig": "projects/web-core/karma.conf.js"}}}}}, "cli": {"analytics": "c838b909-796f-4c34-a46b-3767155f9149"}}